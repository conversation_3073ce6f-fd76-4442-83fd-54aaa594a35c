package br.com.ksisolucoes.vo.vigilancia.investigacao.base;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;

import java.io.Serializable;


/**
 * This is an object that contains data related to the investigacao_agr_doenca_chagas_deslocamento table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="investigacao_agr_doenca_chagas_deslocamento"
 */

public abstract class BaseInvestigacaoAgravoDoencaChagasDeslocamento extends BaseRootVO implements Serializable {

	public static String REF = "InvestigacaoAgravoDoencaChagasDeslocamento";
	public static final String PROP_PAIS_DESLOCAMENTO_TAB = "paisDeslocamentoTab";
	public static final String PROP_INVESTIGACAO_AGRAVO_DOENCA_CHAGAS = "investigacaoAgravoDoencaChagas";
	public static final String PROP_TXT_UF_ESTADO = "txtUfEstado";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_CIDADE_DESLOCAMENTO_TAB = "cidadeDeslocamentoTab";


	// constructors
	public BaseInvestigacaoAgravoDoencaChagasDeslocamento () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseInvestigacaoAgravoDoencaChagasDeslocamento (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String txtUfEstado;

	// many to one
	private br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoDoencaChagas investigacaoAgravoDoencaChagas;
	private br.com.ksisolucoes.vo.basico.Cidade cidadeDeslocamentoTab;
	private br.com.ksisolucoes.vo.basico.Pais paisDeslocamentoTab;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_invest_agr_doenca_chagas_deslocamento"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: txt_uf_estado
	 */
	public java.lang.String getTxtUfEstado () {
		return getPropertyValue(this, txtUfEstado, PROP_TXT_UF_ESTADO); 
	}

	/**
	 * Set the value related to the column: txt_uf_estado
	 * @param txtUfEstado the txt_uf_estado value
	 */
	public void setTxtUfEstado (java.lang.String txtUfEstado) {
//        java.lang.String txtUfEstadoOld = this.txtUfEstado;
		this.txtUfEstado = txtUfEstado;
//        this.getPropertyChangeSupport().firePropertyChange ("txtUfEstado", txtUfEstadoOld, txtUfEstado);
	}



	/**
	 * Return the value associated with the column: cd_invest_agr_doenca_chagas
	 */
	public br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoDoencaChagas getInvestigacaoAgravoDoencaChagas () {
		return getPropertyValue(this, investigacaoAgravoDoencaChagas, PROP_INVESTIGACAO_AGRAVO_DOENCA_CHAGAS); 
	}

	/**
	 * Set the value related to the column: cd_invest_agr_doenca_chagas
	 * @param investigacaoAgravoDoencaChagas the cd_invest_agr_doenca_chagas value
	 */
	public void setInvestigacaoAgravoDoencaChagas (br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoDoencaChagas investigacaoAgravoDoencaChagas) {
//        br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoDoencaChagas investigacaoAgravoDoencaChagasOld = this.investigacaoAgravoDoencaChagas;
		this.investigacaoAgravoDoencaChagas = investigacaoAgravoDoencaChagas;
//        this.getPropertyChangeSupport().firePropertyChange ("investigacaoAgravoDoencaChagas", investigacaoAgravoDoencaChagasOld, investigacaoAgravoDoencaChagas);
	}



	/**
	 * Return the value associated with the column: cd_cidade_deslocamento
	 */
	public br.com.ksisolucoes.vo.basico.Cidade getCidadeDeslocamentoTab () {
		return getPropertyValue(this, cidadeDeslocamentoTab, PROP_CIDADE_DESLOCAMENTO_TAB); 
	}

	/**
	 * Set the value related to the column: cd_cidade_deslocamento
	 * @param cidadeDeslocamentoTab the cd_cidade_deslocamento value
	 */
	public void setCidadeDeslocamentoTab (br.com.ksisolucoes.vo.basico.Cidade cidadeDeslocamentoTab) {
//        br.com.ksisolucoes.vo.basico.Cidade cidadeDeslocamentoTabOld = this.cidadeDeslocamentoTab;
		this.cidadeDeslocamentoTab = cidadeDeslocamentoTab;
//        this.getPropertyChangeSupport().firePropertyChange ("cidadeDeslocamentoTab", cidadeDeslocamentoTabOld, cidadeDeslocamentoTab);
	}



	/**
	 * Return the value associated with the column: cd_pais_deslocamento
	 */
	public br.com.ksisolucoes.vo.basico.Pais getPaisDeslocamentoTab () {
		return getPropertyValue(this, paisDeslocamentoTab, PROP_PAIS_DESLOCAMENTO_TAB); 
	}

	/**
	 * Set the value related to the column: cd_pais_deslocamento
	 * @param paisDeslocamentoTab the cd_pais_deslocamento value
	 */
	public void setPaisDeslocamentoTab (br.com.ksisolucoes.vo.basico.Pais paisDeslocamentoTab) {
//        br.com.ksisolucoes.vo.basico.Pais paisDeslocamentoTabOld = this.paisDeslocamentoTab;
		this.paisDeslocamentoTab = paisDeslocamentoTab;
//        this.getPropertyChangeSupport().firePropertyChange ("paisDeslocamentoTab", paisDeslocamentoTabOld, paisDeslocamentoTab);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoDoencaChagasDeslocamento)) return false;
		else {
			br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoDoencaChagasDeslocamento investigacaoAgravoDoencaChagasDeslocamento = (br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoDoencaChagasDeslocamento) obj;
			if (null == this.getCodigo() || null == investigacaoAgravoDoencaChagasDeslocamento.getCodigo()) return false;
			else return (this.getCodigo().equals(investigacaoAgravoDoencaChagasDeslocamento.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}