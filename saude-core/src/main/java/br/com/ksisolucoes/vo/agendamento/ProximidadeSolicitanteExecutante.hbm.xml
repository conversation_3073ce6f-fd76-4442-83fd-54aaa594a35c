<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.agendamento">
    <class
            name="ProximidadeSolicitanteExecutante"
            table="proximidade_solic_execu"
    >

        <id
                name="codigo"
                type="java.lang.Long"
                column="cd_proximidade_solic_execu"
        >
            <generator class="sequence">
                <param name="sequence">seq_proximidade_solic_execu</param>
            </generator>
        </id>
        <version column="version" name="version" type="long"/>

        <many-to-one
                class="br.com.ksisolucoes.vo.basico.Empresa"
                column="empresa_solic"
                name="empresaSolicitante"
                not-null="true"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.controle.Usuario"
                column="cd_usuario"
                name="usuario"
                not-null="true"
        />

        <many-to-one
                name="tipoExame"
                class="br.com.ksisolucoes.vo.prontuario.basico.TipoExame"
                column="cd_tp_exame"
                not-null="false"
        />

        <property
                name="dataCadastro"
                column="dt_cadastro"
                type="timestamp"
                not-null="true"
        />

    </class>
</hibernate-mapping>