<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.entradas.dispensacao"  >
    <class name="DispensacaoMedicamentoItem" table="dispensacao_medicamento_item">
        
         <id
            name="codigo"
            type="java.lang.Long" 
            column="cd_dis_med_item"
        > 
            <generator class="assigned"/>
        </id> 
        
        <version column="version" name="version" type="long" />
        
        <many-to-one
            class="DispensacaoMedicamento"
            name="dispensacaoMedicamento"
            column="nr_dispensacao"
        />
            
        <property
            name="item"
            column="item"
            type="java.lang.Long"
            />
        
        <property
            column="posologia"
            name="posologia"
            not-null="false"
            type="java.lang.Double"
        />
        
        <property
            column="quantidade_prescrita"
            name="quantidadePrescrita"
            not-null="true"
            type="java.lang.Double"
        />
        
        <property
            column="quantidade_dispensada"
            name="quantidadeDispensada"
            not-null="true"
            type="java.lang.Double"
        />
        
        <property
            column="quantidade_dispensada"
            name="quantidadeDispensadaOriginal"
            not-null="true"
            type="java.lang.Double"
            insert="false"
            update="false"
        />
        
        <property
            column="preco_custo"
            name="precoCusto"
            not-null="false"
            type="java.lang.Double"
        />
        
        <property
            column="preco_medio"
            name="precoMedio"
            not-null="false"
            type="java.lang.Double"
        />
        
        <property
            column="status"
            name="status"
            not-null="true"
            type="java.lang.Long"
        />
        
        <property
            column="dt_prox_dispensacao"
            name="dataProximaDispensacao"
            not-null="false"
            type="java.util.Date"
        />
        
        <property
            column="dt_validade_receita"
            name="dataValidadeReceita"
            not-null="true"
            type="java.util.Date"
        />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.entradas.estoque.Produto"
            column="cod_pro"
            name="produto"
            not-null="true"
        />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.cadsus.UsuarioCadsus"
            column="cd_usu_cadsus_destino"
            name="usuarioCadsusDestino"
            not-null="false"
        />
        
        <property
            name="justificativa"
            column="justificativa"
            type="java.lang.String"
            length="200"
       	/>
       	
        <property
            column="dt_ultima_dispensacao"
            name="dataUltimaDispensacao"
            not-null="false"
            type="java.util.Date"
        />
        
        <property
            column="tp_uso"
            name="tipoUso"
            not-null="false"
            type="java.lang.Long"
        />
        
        <property
            column="ultimo_preco"
            name="ultimoPreco"
            not-null="false"
            type="java.lang.Double"
        />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.prontuario.basico.ReceituarioItem"
            column="cd_receiturario_item"
            name="receituarioItem"
            not-null="false"  
        />

        <property
            column="quantidade_devolvida"
            name="quantidadeDevolvida"
            not-null="false"
            type="java.lang.Double"
        />
        
        <property
            column="preco_unitario"
            name="precoUnitario"
            not-null="false"
            type="java.lang.Double"
        />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.prontuario.basico.ReceituarioItemComponente"
            column="cd_receituario_componente"
            name="receituarioItemComponente"
            not-null="false"  
        />
        
        <property
            column="tipo"
            name="tipo"
            not-null="false"
            type="java.lang.Long"
        />
        
         <property
            column="dt_integracao_inovamfri"
            name="dataIntegracaoInovamfri"
            not-null="false"
            type="timestamp"
        />

        <many-to-one
            class="br.com.ksisolucoes.vo.entradas.estoque.UsuarioCadsusKit"
            name="usuarioCadsusKit"
            column="cd_usuario_cadsus_kit"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.prontuario.basico.Cid"
                name="cid"
                column="cd_cid"
        />

    </class>
</hibernate-mapping>
