<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
"-//Hibernate/Hibernate Mapping DTD//EN"
"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.consorcio"  >
    <class name="Licitacao" table="licitacao">
        
        <id
            name="codigo"
            type="java.lang.Long"
            column="cd_licitacao"
        >
            <generator class="sequence">
               <param name="sequence">seq_licitacao</param>
            </generator>
        </id> <version column="version" name="version" type="long" />

        <many-to-one
            class="br.com.ksisolucoes.vo.basico.Empresa"
            column="empresa"
            name="empresa"
            not-null="true"
        />

        <many-to-one
            class="br.com.ksisolucoes.vo.controle.Usuario"
            column="cd_usuario"
            name="usuarioCadastro"
            not-null="true"
        />
        
        <property
            column="dt_cadastro"
            name="dataCadastro"
            not-null="true"
            type="java.util.Date"
        />
        
        <property
            column="observacao"
            name="observacao"
            not-null="false"
            type="java.lang.String"
            length="512"
        />

        <property
            column="num_pregao"
            name="numeroPregao"
            not-null="false"
            type="java.lang.String"
            length="20"
        />
        
        <property
            column="dt_validade"
            name="dataValidade"
            not-null="false"
            type="java.util.Date"
        />
        
        <property
            column="status"
            name="status"
            type="java.lang.Long"
            not-null="true"
        />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.controle.Usuario"
            column="cd_usu_aprovacao"
            name="usuarioAprovacao"
            not-null="false"
        />
        
        <property
            column="dt_aprovacao"
            name="dataAprovacao"
            not-null="false"
            type="java.util.Date"
        />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.controle.Usuario"
            column="cd_usu_cancelamento"
            name="usuarioCancelamento"
            not-null="false"
        />
        
        <property
            column="dt_cancelamento"
            name="dataCancelamento"
            not-null="false"
            type="java.util.Date"
        />
                
        <property
            column="dt_fechamento"
            name="dataFechamento"
            not-null="false"
            type="timestamp"
        />
                
        <many-to-one
            class="br.com.ksisolucoes.vo.controle.Usuario"
            column="cd_usu_fechamento"
            name="usuarioFechamento"
            not-null="false"
        />
        
        <many-to-one  
            class="br.com.ksisolucoes.vo.consorcio.TipoConta"
            name="tipoConta"
            column="cd_tp_conta"
            not-null="true"
        />       
        
        
    </class>
</hibernate-mapping>
