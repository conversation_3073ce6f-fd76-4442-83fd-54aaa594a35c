package br.com.ksisolucoes.vo.consorcio.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the ped_transf_licitacao_item table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="ped_transf_licitacao_item"
 */

public abstract class BasePedidoTransferenciaLicitacaoItem extends BaseRootVO implements Serializable {

	public static String REF = "PedidoTransferenciaLicitacaoItem";
	public static final String PROP_STATUS = "status";
	public static final String PROP_QUANTIDADE_ENVIADA = "quantidadeEnviada";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_PRODUTO = "produto";
	public static final String PROP_PEDIDO_TRANSFERENCIA_LICITACAO = "pedidoTransferenciaLicitacao";
	public static final String PROP_QUANTIDADE_CANCELADA = "quantidadeCancelada";
	public static final String PROP_PRECO_UNITARIO = "precoUnitario";
	public static final String PROP_USUARIO_CANCELAMENTO = "usuarioCancelamento";
	public static final String PROP_MOTIVO_CANCELAMENTO = "motivoCancelamento";
	public static final String PROP_QUANTIDADE_SEPARADA = "quantidadeSeparada";
	public static final String PROP_FLAG_SEPARADO = "flagSeparado";
	public static final String PROP_DATA_CANCELAMENTO = "dataCancelamento";
	public static final String PROP_QUANTIDADE = "quantidade";


	// constructors
	public BasePedidoTransferenciaLicitacaoItem () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BasePedidoTransferenciaLicitacaoItem (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BasePedidoTransferenciaLicitacaoItem (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.consorcio.PedidoTransferenciaLicitacao pedidoTransferenciaLicitacao,
		br.com.ksisolucoes.vo.entradas.estoque.Produto produto,
		java.lang.Long quantidade,
		java.lang.Long status) {

		this.setCodigo(codigo);
		this.setPedidoTransferenciaLicitacao(pedidoTransferenciaLicitacao);
		this.setProduto(produto);
		this.setQuantidade(quantidade);
		this.setStatus(status);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Long quantidade;
	private java.lang.Long quantidadeEnviada;
	private java.lang.Long quantidadeSeparada;
	private java.lang.Long status;
	private java.util.Date dataCancelamento;
	private java.lang.Double precoUnitario;
	private java.lang.Double quantidadeCancelada;
	private java.lang.String motivoCancelamento;
	private java.lang.Long flagSeparado;

	// many to one
	private br.com.ksisolucoes.vo.consorcio.PedidoTransferenciaLicitacao pedidoTransferenciaLicitacao;
	private br.com.ksisolucoes.vo.entradas.estoque.Produto produto;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamento;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_ped_transf_lic_item"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: qtd_pedido
	 */
	public java.lang.Long getQuantidade () {
		return getPropertyValue(this, quantidade, PROP_QUANTIDADE); 
	}

	/**
	 * Set the value related to the column: qtd_pedido
	 * @param quantidade the qtd_pedido value
	 */
	public void setQuantidade (java.lang.Long quantidade) {
//        java.lang.Long quantidadeOld = this.quantidade;
		this.quantidade = quantidade;
//        this.getPropertyChangeSupport().firePropertyChange ("quantidade", quantidadeOld, quantidade);
	}



	/**
	 * Return the value associated with the column: qtd_enviada
	 */
	public java.lang.Long getQuantidadeEnviada () {
		return getPropertyValue(this, quantidadeEnviada, PROP_QUANTIDADE_ENVIADA); 
	}

	/**
	 * Set the value related to the column: qtd_enviada
	 * @param quantidadeEnviada the qtd_enviada value
	 */
	public void setQuantidadeEnviada (java.lang.Long quantidadeEnviada) {
//        java.lang.Long quantidadeEnviadaOld = this.quantidadeEnviada;
		this.quantidadeEnviada = quantidadeEnviada;
//        this.getPropertyChangeSupport().firePropertyChange ("quantidadeEnviada", quantidadeEnviadaOld, quantidadeEnviada);
	}



	/**
	 * Return the value associated with the column: qtd_separada
	 */
	public java.lang.Long getQuantidadeSeparada () {
		return getPropertyValue(this, quantidadeSeparada, PROP_QUANTIDADE_SEPARADA); 
	}

	/**
	 * Set the value related to the column: qtd_separada
	 * @param quantidadeSeparada the qtd_separada value
	 */
	public void setQuantidadeSeparada (java.lang.Long quantidadeSeparada) {
//        java.lang.Long quantidadeSeparadaOld = this.quantidadeSeparada;
		this.quantidadeSeparada = quantidadeSeparada;
//        this.getPropertyChangeSupport().firePropertyChange ("quantidadeSeparada", quantidadeSeparadaOld, quantidadeSeparada);
	}



	/**
	 * Return the value associated with the column: status
	 */
	public java.lang.Long getStatus () {
		return getPropertyValue(this, status, PROP_STATUS); 
	}

	/**
	 * Set the value related to the column: status
	 * @param status the status value
	 */
	public void setStatus (java.lang.Long status) {
//        java.lang.Long statusOld = this.status;
		this.status = status;
//        this.getPropertyChangeSupport().firePropertyChange ("status", statusOld, status);
	}



	/**
	 * Return the value associated with the column: dt_cancelamento
	 */
	public java.util.Date getDataCancelamento () {
		return getPropertyValue(this, dataCancelamento, PROP_DATA_CANCELAMENTO); 
	}

	/**
	 * Set the value related to the column: dt_cancelamento
	 * @param dataCancelamento the dt_cancelamento value
	 */
	public void setDataCancelamento (java.util.Date dataCancelamento) {
//        java.util.Date dataCancelamentoOld = this.dataCancelamento;
		this.dataCancelamento = dataCancelamento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCancelamento", dataCancelamentoOld, dataCancelamento);
	}



	/**
	 * Return the value associated with the column: prc_unitario
	 */
	public java.lang.Double getPrecoUnitario () {
		return getPropertyValue(this, precoUnitario, PROP_PRECO_UNITARIO); 
	}

	/**
	 * Set the value related to the column: prc_unitario
	 * @param precoUnitario the prc_unitario value
	 */
	public void setPrecoUnitario (java.lang.Double precoUnitario) {
//        java.lang.Double precoUnitarioOld = this.precoUnitario;
		this.precoUnitario = precoUnitario;
//        this.getPropertyChangeSupport().firePropertyChange ("precoUnitario", precoUnitarioOld, precoUnitario);
	}



	/**
	 * Return the value associated with the column: qt_cancelada
	 */
	public java.lang.Double getQuantidadeCancelada () {
		return getPropertyValue(this, quantidadeCancelada, PROP_QUANTIDADE_CANCELADA); 
	}

	/**
	 * Set the value related to the column: qt_cancelada
	 * @param quantidadeCancelada the qt_cancelada value
	 */
	public void setQuantidadeCancelada (java.lang.Double quantidadeCancelada) {
//        java.lang.Double quantidadeCanceladaOld = this.quantidadeCancelada;
		this.quantidadeCancelada = quantidadeCancelada;
//        this.getPropertyChangeSupport().firePropertyChange ("quantidadeCancelada", quantidadeCanceladaOld, quantidadeCancelada);
	}



	/**
	 * Return the value associated with the column: mot_cancelamento
	 */
	public java.lang.String getMotivoCancelamento () {
		return getPropertyValue(this, motivoCancelamento, PROP_MOTIVO_CANCELAMENTO); 
	}

	/**
	 * Set the value related to the column: mot_cancelamento
	 * @param motivoCancelamento the mot_cancelamento value
	 */
	public void setMotivoCancelamento (java.lang.String motivoCancelamento) {
//        java.lang.String motivoCancelamentoOld = this.motivoCancelamento;
		this.motivoCancelamento = motivoCancelamento;
//        this.getPropertyChangeSupport().firePropertyChange ("motivoCancelamento", motivoCancelamentoOld, motivoCancelamento);
	}



	/**
	 * Return the value associated with the column: flag_separado
	 */
	public java.lang.Long getFlagSeparado () {
		return getPropertyValue(this, flagSeparado, PROP_FLAG_SEPARADO); 
	}

	/**
	 * Set the value related to the column: flag_separado
	 * @param flagSeparado the flag_separado value
	 */
	public void setFlagSeparado (java.lang.Long flagSeparado) {
//        java.lang.Long flagSeparadoOld = this.flagSeparado;
		this.flagSeparado = flagSeparado;
//        this.getPropertyChangeSupport().firePropertyChange ("flagSeparado", flagSeparadoOld, flagSeparado);
	}



	/**
	 * Return the value associated with the column: cd_ped_transf_lic
	 */
	public br.com.ksisolucoes.vo.consorcio.PedidoTransferenciaLicitacao getPedidoTransferenciaLicitacao () {
		return getPropertyValue(this, pedidoTransferenciaLicitacao, PROP_PEDIDO_TRANSFERENCIA_LICITACAO); 
	}

	/**
	 * Set the value related to the column: cd_ped_transf_lic
	 * @param pedidoTransferenciaLicitacao the cd_ped_transf_lic value
	 */
	public void setPedidoTransferenciaLicitacao (br.com.ksisolucoes.vo.consorcio.PedidoTransferenciaLicitacao pedidoTransferenciaLicitacao) {
//        br.com.ksisolucoes.vo.consorcio.PedidoTransferenciaLicitacao pedidoTransferenciaLicitacaoOld = this.pedidoTransferenciaLicitacao;
		this.pedidoTransferenciaLicitacao = pedidoTransferenciaLicitacao;
//        this.getPropertyChangeSupport().firePropertyChange ("pedidoTransferenciaLicitacao", pedidoTransferenciaLicitacaoOld, pedidoTransferenciaLicitacao);
	}



	/**
	 * Return the value associated with the column: cod_pro
	 */
	public br.com.ksisolucoes.vo.entradas.estoque.Produto getProduto () {
		return getPropertyValue(this, produto, PROP_PRODUTO); 
	}

	/**
	 * Set the value related to the column: cod_pro
	 * @param produto the cod_pro value
	 */
	public void setProduto (br.com.ksisolucoes.vo.entradas.estoque.Produto produto) {
//        br.com.ksisolucoes.vo.entradas.estoque.Produto produtoOld = this.produto;
		this.produto = produto;
//        this.getPropertyChangeSupport().firePropertyChange ("produto", produtoOld, produto);
	}



	/**
	 * Return the value associated with the column: cd_usuario_cancelamento
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioCancelamento () {
		return getPropertyValue(this, usuarioCancelamento, PROP_USUARIO_CANCELAMENTO); 
	}

	/**
	 * Set the value related to the column: cd_usuario_cancelamento
	 * @param usuarioCancelamento the cd_usuario_cancelamento value
	 */
	public void setUsuarioCancelamento (br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamento) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamentoOld = this.usuarioCancelamento;
		this.usuarioCancelamento = usuarioCancelamento;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCancelamento", usuarioCancelamentoOld, usuarioCancelamento);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.consorcio.PedidoTransferenciaLicitacaoItem)) return false;
		else {
			br.com.ksisolucoes.vo.consorcio.PedidoTransferenciaLicitacaoItem pedidoTransferenciaLicitacaoItem = (br.com.ksisolucoes.vo.consorcio.PedidoTransferenciaLicitacaoItem) obj;
			if (null == this.getCodigo() || null == pedidoTransferenciaLicitacaoItem.getCodigo()) return false;
			else return (this.getCodigo().equals(pedidoTransferenciaLicitacaoItem.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}