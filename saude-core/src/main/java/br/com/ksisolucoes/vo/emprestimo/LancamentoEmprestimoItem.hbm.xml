<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.emprestimo"  >
    <class name="LancamentoEmprestimoItem" table="lancamento_emprestimo_item">
            <id
                    name="codigo"
                    type="java.lang.Long"
                    column="cd_emprestimo_item"
            >
            <generator class="assigned" />
            </id> <version column="version" name="version" type="long" />
            
            <many-to-one
                    name="lancamentoEmprestimo"
                    class="br.com.ksisolucoes.vo.emprestimo.LancamentoEmprestimo"
                    column="cd_emprestimo"
                    not-null="true"
            />
            
            <many-to-one
                    name="produto"
                    class="br.com.ksisolucoes.vo.entradas.estoque.Produto"
                    column="cod_pro"
                    not-null="true"
            />
            
            <property
                    name="grupoEstoque"
                    column="grupo_estoque"
                    type="java.lang.String"
                    not-null="true"
                    length="20"
            />
            
            <property
                    name="quantidade"
                    column="quantidade"
                    type="java.lang.Double"
                    not-null="true"
            />
            
            <property
                    name="quantidadeDevolvida"
                    column="qtd_devolvida"
                    type="java.lang.Double"
                    not-null="false"
            />
            
            <property
                    name="status"
                    column="status"
                    type="java.lang.Long"
                    not-null="true"
            />
            
            <property 
                    name="dataCadastro"
                    type="timestamp"
                    column="dt_cadastro"
                    not-null="true"
            />
            
            <many-to-one
                    name="usuario"
                    class="br.com.ksisolucoes.vo.controle.Usuario"
                    column="cd_usuario"
                    not-null="true"
            />
            
            <property
                    name="motivoCancelamento"
                    column="mot_cancelamento"
                    type="java.lang.String"
                    not-null="false"
                    length="512"
            />
            
            <property 
                    name="dataCancelamento"
                    type="timestamp"
                    column="dt_cancelamento"
                    not-null="false"
            />
            
            <many-to-one
                    name="usuarioCancelamento"
                    class="br.com.ksisolucoes.vo.controle.Usuario"
                    column="cd_usuario_can"
                    not-null="false"
            />
            
            <property
                    name="quantidadeCancelada"
                    column="qtd_cancelada"
                    type="java.lang.Double"
                    not-null="false"
            />
            
            <property 
                    name="dataValidade"
                    type="java.util.Date"
                    column="dt_validade"
                    not-null="false"
            />
            
    </class>
</hibernate-mapping>