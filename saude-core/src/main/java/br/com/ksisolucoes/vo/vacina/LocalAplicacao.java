package br.com.ksisolucoes.vo.vacina;

import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.vacina.base.BaseLocalAplicacao;

import java.io.Serializable;



public class LocalAplicacao extends BaseLocalAplicacao implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public LocalAplicacao () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public LocalAplicacao (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public LocalAplicacao (
		java.lang.Long codigo,
		java.lang.String descricao) {

		super (
			codigo,
			descricao);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }


	public String getDescricaoFormatado() {
		return super.getCodigo() + " - " + super.getDescricao();
	}
}