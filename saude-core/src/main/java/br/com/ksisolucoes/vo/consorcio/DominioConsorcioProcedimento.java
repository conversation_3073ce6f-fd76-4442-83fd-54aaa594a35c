package br.com.ksisolucoes.vo.consorcio;

import java.io.Serializable;

import br.com.ksisolucoes.vo.consorcio.base.BaseDominioConsorcioProcedimento;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.interfaces.PesquisaObjectInterface;



public class DominioConsorcioProcedimento extends BaseDominioConsorcioProcedimento implements CodigoManager, PesquisaObjectInterface {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public DominioConsorcioProcedimento () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public DominioConsorcioProcedimento (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public DominioConsorcioProcedimento (
		java.lang.Long codigo,
		java.lang.String descricaoProcedimento,
		java.lang.String referencia,
		java.lang.String keyword) {

		super (
			codigo,
			descricaoProcedimento,
			referencia,
			keyword);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    @Override
    public String getDescricaoVO() {
        return getDescricaoProcedimento();
    }

    @Override
    public String getIdentificador() {
        return getConsorcioProcedimento().getCodigo().toString();
    }
}