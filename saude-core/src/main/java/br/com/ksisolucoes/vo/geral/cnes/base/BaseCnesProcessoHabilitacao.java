package br.com.ksisolucoes.vo.geral.cnes.base;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;

import java.io.Serializable;


/**
 * This is an object that contains data related to the cnes_processo_habilitacao table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="cnes_processo_habilitacao"
 */

public abstract class BaseCnesProcessoHabilitacao extends BaseRootVO implements Serializable {

	public static String REF = "CnesProcessoHabilitacao";
	public static final String PROP_USUARIO = "usuario";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_DATA_LANCAMENTO = "dataLancamento";
	public static final String PROP_QUANTIDADE_LEITOS = "quantidadeLeitos";
	public static final String PROP_DESCRICAO_HABILITACAO = "descricaoHabilitacao";
	public static final String PROP_NUMERO_PORTARIA = "numeroPortaria";
	public static final String PROP_COMPETENCIA_INICIAL = "competenciaInicial";
	public static final String PROP_CODIGO_HABILITACAO = "codigoHabilitacao";
	public static final String PROP_CNES_PROCESSO_EMPRESA = "cnesProcessoEmpresa";
	public static final String PROP_COMPETENCIA_FINAL = "competenciaFinal";


	// constructors
	public BaseCnesProcessoHabilitacao () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseCnesProcessoHabilitacao (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseCnesProcessoHabilitacao (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.geral.cnes.CnesProcessoEmpresa cnesProcessoEmpresa,
		java.lang.String codigoHabilitacao,
		java.lang.String descricaoHabilitacao,
		java.lang.String competenciaInicial,
		java.lang.String competenciaFinal,
		java.lang.Long quantidadeLeitos) {

		this.setCodigo(codigo);
		this.setCnesProcessoEmpresa(cnesProcessoEmpresa);
		this.setCodigoHabilitacao(codigoHabilitacao);
		this.setDescricaoHabilitacao(descricaoHabilitacao);
		this.setCompetenciaInicial(competenciaInicial);
		this.setCompetenciaFinal(competenciaFinal);
		this.setQuantidadeLeitos(quantidadeLeitos);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String codigoHabilitacao;
	private java.lang.String descricaoHabilitacao;
	private java.lang.String competenciaInicial;
	private java.lang.String competenciaFinal;
	private java.lang.Long quantidadeLeitos;
	private java.lang.String numeroPortaria;
	private java.util.Date dataLancamento;
	private java.lang.String usuario;

	// many to one
	private br.com.ksisolucoes.vo.geral.cnes.CnesProcessoEmpresa cnesProcessoEmpresa;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_cnes_processo_habilitacao"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: cod_habilitacao
	 */
	public java.lang.String getCodigoHabilitacao () {
		return getPropertyValue(this, codigoHabilitacao, PROP_CODIGO_HABILITACAO); 
	}

	/**
	 * Set the value related to the column: cod_habilitacao
	 * @param codigoHabilitacao the cod_habilitacao value
	 */
	public void setCodigoHabilitacao (java.lang.String codigoHabilitacao) {
//        java.lang.String codigoHabilitacaoOld = this.codigoHabilitacao;
		this.codigoHabilitacao = codigoHabilitacao;
//        this.getPropertyChangeSupport().firePropertyChange ("codigoHabilitacao", codigoHabilitacaoOld, codigoHabilitacao);
	}



	/**
	 * Return the value associated with the column: ds_habilitacao
	 */
	public java.lang.String getDescricaoHabilitacao () {
		return getPropertyValue(this, descricaoHabilitacao, PROP_DESCRICAO_HABILITACAO); 
	}

	/**
	 * Set the value related to the column: ds_habilitacao
	 * @param descricaoHabilitacao the ds_habilitacao value
	 */
	public void setDescricaoHabilitacao (java.lang.String descricaoHabilitacao) {
//        java.lang.String descricaoHabilitacaoOld = this.descricaoHabilitacao;
		this.descricaoHabilitacao = descricaoHabilitacao;
//        this.getPropertyChangeSupport().firePropertyChange ("descricaoHabilitacao", descricaoHabilitacaoOld, descricaoHabilitacao);
	}



	/**
	 * Return the value associated with the column: cmpt_inicial
	 */
	public java.lang.String getCompetenciaInicial () {
		return getPropertyValue(this, competenciaInicial, PROP_COMPETENCIA_INICIAL); 
	}

	/**
	 * Set the value related to the column: cmpt_inicial
	 * @param competenciaInicial the cmpt_inicial value
	 */
	public void setCompetenciaInicial (java.lang.String competenciaInicial) {
//        java.lang.String competenciaInicialOld = this.competenciaInicial;
		this.competenciaInicial = competenciaInicial;
//        this.getPropertyChangeSupport().firePropertyChange ("competenciaInicial", competenciaInicialOld, competenciaInicial);
	}



	/**
	 * Return the value associated with the column: cmpt_final
	 */
	public java.lang.String getCompetenciaFinal () {
		return getPropertyValue(this, competenciaFinal, PROP_COMPETENCIA_FINAL); 
	}

	/**
	 * Set the value related to the column: cmpt_final
	 * @param competenciaFinal the cmpt_final value
	 */
	public void setCompetenciaFinal (java.lang.String competenciaFinal) {
//        java.lang.String competenciaFinalOld = this.competenciaFinal;
		this.competenciaFinal = competenciaFinal;
//        this.getPropertyChangeSupport().firePropertyChange ("competenciaFinal", competenciaFinalOld, competenciaFinal);
	}



	/**
	 * Return the value associated with the column: qt_leitos
	 */
	public java.lang.Long getQuantidadeLeitos () {
		return getPropertyValue(this, quantidadeLeitos, PROP_QUANTIDADE_LEITOS); 
	}

	/**
	 * Set the value related to the column: qt_leitos
	 * @param quantidadeLeitos the qt_leitos value
	 */
	public void setQuantidadeLeitos (java.lang.Long quantidadeLeitos) {
//        java.lang.Long quantidadeLeitosOld = this.quantidadeLeitos;
		this.quantidadeLeitos = quantidadeLeitos;
//        this.getPropertyChangeSupport().firePropertyChange ("quantidadeLeitos", quantidadeLeitosOld, quantidadeLeitos);
	}



	/**
	 * Return the value associated with the column: num_portaria
	 */
	public java.lang.String getNumeroPortaria () {
		return getPropertyValue(this, numeroPortaria, PROP_NUMERO_PORTARIA); 
	}

	/**
	 * Set the value related to the column: num_portaria
	 * @param numeroPortaria the num_portaria value
	 */
	public void setNumeroPortaria (java.lang.String numeroPortaria) {
//        java.lang.String numeroPortariaOld = this.numeroPortaria;
		this.numeroPortaria = numeroPortaria;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroPortaria", numeroPortariaOld, numeroPortaria);
	}



	/**
	 * Return the value associated with the column: dt_lancamento
	 */
	public java.util.Date getDataLancamento () {
		return getPropertyValue(this, dataLancamento, PROP_DATA_LANCAMENTO); 
	}

	/**
	 * Set the value related to the column: dt_lancamento
	 * @param dataLancamento the dt_lancamento value
	 */
	public void setDataLancamento (java.util.Date dataLancamento) {
//        java.util.Date dataLancamentoOld = this.dataLancamento;
		this.dataLancamento = dataLancamento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataLancamento", dataLancamentoOld, dataLancamento);
	}



	/**
	 * Return the value associated with the column: usuario
	 */
	public java.lang.String getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO); 
	}

	/**
	 * Set the value related to the column: usuario
	 * @param usuario the usuario value
	 */
	public void setUsuario (java.lang.String usuario) {
//        java.lang.String usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}



	/**
	 * Return the value associated with the column: cd_cnes_processo_empresa
	 */
	public br.com.ksisolucoes.vo.geral.cnes.CnesProcessoEmpresa getCnesProcessoEmpresa () {
		return getPropertyValue(this, cnesProcessoEmpresa, PROP_CNES_PROCESSO_EMPRESA); 
	}

	/**
	 * Set the value related to the column: cd_cnes_processo_empresa
	 * @param cnesProcessoEmpresa the cd_cnes_processo_empresa value
	 */
	public void setCnesProcessoEmpresa (br.com.ksisolucoes.vo.geral.cnes.CnesProcessoEmpresa cnesProcessoEmpresa) {
//        br.com.ksisolucoes.vo.geral.cnes.CnesProcessoEmpresa cnesProcessoEmpresaOld = this.cnesProcessoEmpresa;
		this.cnesProcessoEmpresa = cnesProcessoEmpresa;
//        this.getPropertyChangeSupport().firePropertyChange ("cnesProcessoEmpresa", cnesProcessoEmpresaOld, cnesProcessoEmpresa);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.geral.cnes.CnesProcessoHabilitacao)) return false;
		else {
			br.com.ksisolucoes.vo.geral.cnes.CnesProcessoHabilitacao cnesProcessoHabilitacao = (br.com.ksisolucoes.vo.geral.cnes.CnesProcessoHabilitacao) obj;
			if (null == this.getCodigo() || null == cnesProcessoHabilitacao.getCodigo()) return false;
			else return (this.getCodigo().equals(cnesProcessoHabilitacao.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}