package br.com.ksisolucoes.vo.prontuario.basico.base;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;

import java.io.Serializable;


/**
 * This is an object that contains data related to the im_solic_agend_to_aih table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="im_solic_agend_to_aih"
 */

public abstract class BaseIMTableSolicitacaoAgendamentoToAih extends BaseRootVO implements Serializable {

	public static String REF = "IMTableSolicitacaoAgendamentoToAih";
	public static final String PROP_AIH = "aih";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_SOLICITACAO_AGENDAMENTO = "solicitacaoAgendamento";


	// constructors
	public BaseIMTableSolicitacaoAgendamentoToAih () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseIMTableSolicitacaoAgendamentoToAih (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseIMTableSolicitacaoAgendamentoToAih (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento solicitacaoAgendamento,
		br.com.ksisolucoes.vo.prontuario.hospital.Aih aih) {

		this.setCodigo(codigo);
		this.setSolicitacaoAgendamento(solicitacaoAgendamento);
		this.setAih(aih);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// many to one
	private br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento solicitacaoAgendamento;
	private br.com.ksisolucoes.vo.prontuario.hospital.Aih aih;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_im_aih"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: cd_solicitacao
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento getSolicitacaoAgendamento () {
		return getPropertyValue(this, solicitacaoAgendamento, PROP_SOLICITACAO_AGENDAMENTO); 
	}

	/**
	 * Set the value related to the column: cd_solicitacao
	 * @param solicitacaoAgendamento the cd_solicitacao value
	 */
	public void setSolicitacaoAgendamento (br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento solicitacaoAgendamento) {
//        br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento solicitacaoAgendamentoOld = this.solicitacaoAgendamento;
		this.solicitacaoAgendamento = solicitacaoAgendamento;
//        this.getPropertyChangeSupport().firePropertyChange ("solicitacaoAgendamento", solicitacaoAgendamentoOld, solicitacaoAgendamento);
	}



	/**
	 * Return the value associated with the column: cd_aut_intern_hosp
	 */
	public br.com.ksisolucoes.vo.prontuario.hospital.Aih getAih () {
		return getPropertyValue(this, aih, PROP_AIH); 
	}

	/**
	 * Set the value related to the column: cd_aut_intern_hosp
	 * @param aih the cd_aut_intern_hosp value
	 */
	public void setAih (br.com.ksisolucoes.vo.prontuario.hospital.Aih aih) {
//        br.com.ksisolucoes.vo.prontuario.hospital.Aih aihOld = this.aih;
		this.aih = aih;
//        this.getPropertyChangeSupport().firePropertyChange ("aih", aihOld, aih);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.basico.IMTableSolicitacaoAgendamentoToAih)) return false;
		else {
			br.com.ksisolucoes.vo.prontuario.basico.IMTableSolicitacaoAgendamentoToAih iMTableSolicitacaoAgendamentoToAih = (br.com.ksisolucoes.vo.prontuario.basico.IMTableSolicitacaoAgendamentoToAih) obj;
			if (null == this.getCodigo() || null == iMTableSolicitacaoAgendamentoToAih.getCodigo()) return false;
			else return (this.getCodigo().equals(iMTableSolicitacaoAgendamentoToAih.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}