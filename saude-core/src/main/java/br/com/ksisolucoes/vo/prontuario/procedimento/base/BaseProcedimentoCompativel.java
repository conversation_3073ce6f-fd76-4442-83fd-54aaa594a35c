package br.com.ksisolucoes.vo.prontuario.procedimento.base;

import java.io.Serializable;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;


/**
 * This is an object that contains data related to the procedimento_compativel table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="procedimento_compativel"
 */

public abstract class BaseProcedimentoCompativel extends BaseRootVO implements Serializable {

	public static String REF = "ProcedimentoCompativel";
	public static final String PROP_ID = "id";
	public static final String PROP_TP_COMPATIBILIDADE = "tpCompatibilidade";
	public static final String PROP_QUANTIDADE_PERMITIDA = "quantidadePermitida";


	// constructors
	public BaseProcedimentoCompativel () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseProcedimentoCompativel (br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompativelPK id) {
		this.setId(id);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseProcedimentoCompativel (
		br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompativelPK id,
		java.lang.Long tpCompatibilidade,
		java.lang.Double quantidadePermitida) {

		this.setId(id);
		this.setTpCompatibilidade(tpCompatibilidade);
		this.setQuantidadePermitida(quantidadePermitida);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompativelPK id;

	// fields
	private java.lang.Long tpCompatibilidade;
	private java.lang.Double quantidadePermitida;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     */
	public br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompativelPK getId () {
	    return getPropertyValue(this,  id, "id" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param id the new ID
	 */
	public void setId (br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompativelPK id) {
		this.id = id;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: tp_compatibilidade
	 */
	public java.lang.Long getTpCompatibilidade () {
		return getPropertyValue(this, tpCompatibilidade, PROP_TP_COMPATIBILIDADE); 
	}

	/**
	 * Set the value related to the column: tp_compatibilidade
	 * @param tpCompatibilidade the tp_compatibilidade value
	 */
	public void setTpCompatibilidade (java.lang.Long tpCompatibilidade) {
//        java.lang.Long tpCompatibilidadeOld = this.tpCompatibilidade;
		this.tpCompatibilidade = tpCompatibilidade;
//        this.getPropertyChangeSupport().firePropertyChange ("tpCompatibilidade", tpCompatibilidadeOld, tpCompatibilidade);
	}



	/**
	 * Return the value associated with the column: qt_permitida
	 */
	public java.lang.Double getQuantidadePermitida () {
		return getPropertyValue(this, quantidadePermitida, PROP_QUANTIDADE_PERMITIDA); 
	}

	/**
	 * Set the value related to the column: qt_permitida
	 * @param quantidadePermitida the qt_permitida value
	 */
	public void setQuantidadePermitida (java.lang.Double quantidadePermitida) {
//        java.lang.Double quantidadePermitidaOld = this.quantidadePermitida;
		this.quantidadePermitida = quantidadePermitida;
//        this.getPropertyChangeSupport().firePropertyChange ("quantidadePermitida", quantidadePermitidaOld, quantidadePermitida);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompativel)) return false;
		else {
			br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompativel procedimentoCompativel = (br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompativel) obj;
			if (null == this.getId() || null == procedimentoCompativel.getId()) return false;
			else return (this.getId().equals(procedimentoCompativel.getId()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getId()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getId().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }

    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}