<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.prontuario"  >
    <class name="AtividadeTerapeutica" table="atividade_terapeutica">
        <id
            column="cd_atividade_terapeutica"
            name="codigo"
            type="java.lang.Long"
        >
            <generator class="assigned" />
        </id> 
        <version column="version" name="version" type="long" />

        <many-to-one
            class="br.com.ksisolucoes.vo.prontuario.FichaAcolhimento"
            column="cd_ficha_acolhimento"
            not-null="true"
            name="fichaAcolhimento"
		/>
                 
        <property
            column="dia_da_semana"
            name="diaSemana"
            not-null="true"
            type="java.lang.Long"
		 />
                
        <property
            column="ds_atividade_terapeutica"
            name="descricao"
            not-null="true"
            type="java.lang.String"
		 />
        
        <property
            column="hora"
            name="hora"
            type="java.util.Date"
        />
    </class>
</hibernate-mapping>
