package br.com.ksisolucoes.vo.service.sms;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.EnumUtil;
import br.com.ksisolucoes.util.Util;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.service.sms.base.BaseSmsMensagem;

import java.io.Serializable;

public class SmsMensagem extends BaseSmsMensagem implements CodigoManager {

    private static final long serialVersionUID = 1L;

    public enum OrigemSms implements IEnum {

        PROCESSO_DESCONHECIDO(1L, Bundle.getStringApplication("rotulo_desconhecido")),;

        private OrigemSms(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        private final Long value;
        private final String descricao;

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }
    }

    public enum StatusSms implements IEnum {

        ENCAMINHADO(0L, Bundle.getStringApplication("rotulo_encaminhado")),
        RECEBIDO(1L, Bundle.getStringApplication("rotulo_recebido")),
        RESPONDIDO(2L, Bundle.getStringApplication("rotulo_respondido")),
        CANCELADO(3L, Bundle.getStringApplication("rotulo_cancelado")),
        ERRO(4L, Bundle.getStringApplication("rotulo_erro")),;

        private StatusSms(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        private final Long value;
        private final String descricao;

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }
    }

    public enum TipoCadastro implements IEnum {

        CADASTRO(0L, Bundle.getStringApplication("rotulo_cadastro")),
        RESPOSTA(1L, Bundle.getStringApplication("rotulo_resposta")),;

        private TipoCadastro(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        private final Long value;
        private final String descricao;

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }
    }

    /*[CONSTRUCTOR MARKER BEGIN]*/
	public SmsMensagem () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public SmsMensagem (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public SmsMensagem (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		java.lang.String telefone,
		java.lang.String mensagem,
		java.util.Date dataCadastro,
		java.lang.Long status,
		java.lang.Long origem) {

		super (
			codigo,
			usuario,
			telefone,
			mensagem,
			dataCadastro,
			status,
			origem);
	}

    /*[CONSTRUCTOR MARKER END]*/
    public void setCodigoManager(Serializable key) {
        this.setCodigo((java.lang.Long) key);
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    public String getDescricaoStatus() {
        return new EnumUtil().resolveDescricao(StatusSms.values(), getStatus());
    }

    public String getDescricaoOrigem() {
        return new EnumUtil().resolveDescricao(OrigemSms.values(), getOrigem());
    }

    public String getTelefoneFormatado() {
        return Util.getTelefoneFormatado(getTelefone());
    }
}
