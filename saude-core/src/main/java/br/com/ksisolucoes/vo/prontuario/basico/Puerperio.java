package br.com.ksisolucoes.vo.prontuario.basico;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.prontuario.basico.base.BasePuerperio;

import java.io.Serializable;


public class Puerperio extends BasePuerperio implements CodigoManager {
    private static final long serialVersionUID = 1L;

    /*[CONSTRUCTOR MARKER BEGIN]*/
	public Puerperio () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public Puerperio (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public Puerperio (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.prontuario.basico.PreNatal preNatal,
		br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus,
		br.com.ksisolucoes.vo.controle.Usuario usuarioCadastro,
		java.util.Date dataCadastro) {

		super (
			codigo,
			preNatal,
			usuarioCadsus,
			usuarioCadastro,
			dataCadastro);
	}

    /*[CONSTRUCTOR MARKER END]*/
    public enum Desfecho implements IEnum {
        PARTO(1L, Bundle.getStringApplication("rotulo_parto")),;

        private Long value;
        private String descricao;

        private Desfecho(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static Desfecho valeuOf(Long value) {
            for (Desfecho desfecho : Desfecho.values()) {
                if (desfecho.value().equals(value)) {
                    return desfecho;
                }
            }
            return null;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

    }

    public enum TipoParto implements IEnum {

        CESARIO(0L, Bundle.getStringApplication("rotulo_cesario")),
        VAGINAL(1L, Bundle.getStringApplication("rotulo_vaginal")),
        FORCEPS(2L, Bundle.getStringApplication("rotulo_forceps"));

        private Long value;
        private String descricao;

        private TipoParto(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static TipoParto valeuOf(Long value) {
            for (TipoParto tipoParto : TipoParto.values()) {
                if (tipoParto.value().equals(value)) {
                    return tipoParto;
                }
            }
            return null;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

    }

    public enum LocalOcorrencia implements IEnum {
        HOSPITAL(0L, Bundle.getStringApplication("rotulo_hospital")),
        CENTRO_PARTO_NORMAL(1L, Bundle.getStringApplication("rotulo_centro_parto_normal")),
        DOMICILIO(2L, Bundle.getStringApplication("rotulo_domicilio")),
        OUTROS_ESTABELECIMENTOS(3L, Bundle.getStringApplication("rotulo_outros_estabelecimentos_sus"));

        private Long value;
        private String descricao;

        private LocalOcorrencia(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static LocalOcorrencia valeuOf(Long value) {
            for (LocalOcorrencia localOcorrencia : LocalOcorrencia.values()) {
                if (localOcorrencia.value().equals(value)) {
                    return localOcorrencia;
                }
            }
            return null;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

    }
    public String getDescricaoDesfecho() {
        Desfecho desfecho = Desfecho.valueOf(getDesfecho().toString());
        if (desfecho != null && desfecho.descricao != null) {
            return desfecho.descricao();
        }
        return "";
    }

    public void setCodigoManager(Serializable key) {
        this.setCodigo((java.lang.Long) key);
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}
