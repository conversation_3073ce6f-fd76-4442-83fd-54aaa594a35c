package br.com.ksisolucoes.vo.prontuario.procedimento;

import java.util.Date;

import br.com.ksisolucoes.associacao.annotations.IdNameSIGTAP;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.vo.prontuario.procedimento.base.BaseProcedimentoCompetenciaPK;

public class ProcedimentoCompetenciaPK extends BaseProcedimentoCompetenciaPK {
	private static final long serialVersionUID = 1L;
        public static String PROP_DATA_COMPETENCIA_FORMATADA="dataCompetenciaFormatada";

        private String dataCompetenciaFormatada;
/*[CONSTRUCTOR MARKER BEGIN]*/
	public ProcedimentoCompetenciaPK () {}
	
	public ProcedimentoCompetenciaPK (
		br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento procedimento,
		java.util.Date dataCompetencia) {

		super (
			procedimento,
			dataCompetencia);
	}

    @IdNameSIGTAP("DT_COMPETENCIA")
    @Override
    public Date getDataCompetencia() {
        return super.getDataCompetencia();
    }

    @IdNameSIGTAP
    @Override
    public Procedimento getProcedimento() {
        return super.getProcedimento();
    }

    @Override
    public void setDataCompetencia(Date dataCompetencia) {
        super.setDataCompetencia(dataCompetencia);
    }

    @Override
    public void setProcedimento(Procedimento procedimento) {
        super.setProcedimento(procedimento);
    }

    public String getDataCompetenciaFormatada() {
        return Data.formatarMounthYear(this.getDataCompetencia());
    }

    public void setDataCompetenciaFormatada(String dataCompetenciaFormatada) {
        this.dataCompetenciaFormatada = dataCompetenciaFormatada;
    }

}
