package br.com.ksisolucoes.vo.vigilancia.requerimentos;

import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.base.BaseRequerimentoTreinamentoMinistrantes;

import java.io.Serializable;



public class RequerimentoTreinamentoMinistrantes extends BaseRequerimentoTreinamentoMinistrantes implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public RequerimentoTreinamentoMinistrantes () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public RequerimentoTreinamentoMinistrantes (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public RequerimentoTreinamentoMinistrantes (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoTreinamento requerimentoTreinamento,
		java.lang.String nome) {

		super (
			codigo,
			requerimentoTreinamento,
			nome);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}