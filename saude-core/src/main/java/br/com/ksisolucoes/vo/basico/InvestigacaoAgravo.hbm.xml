<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.basico"  >
    <class name="InvestigacaoAgravo" table="investigacao_agravo">

        <id
                name="codigo"
                type="java.lang.Long"
                column="cd_investigacao_agravo"
        >
            <generator class="sequence">
                <param name="sequence">seq_investigacao_agravo</param>
            </generator>
        </id>
        <version column="version" name="version" type="long" />

		<many-to-one class="br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo"
                name="registroAgravo" not-null="true">
            <column name="cd_registro_agravo" />
        </many-to-one>
        
        <property 
            name="dataCadastro"
            column="dt_cadastro"
            type="timestamp"
            not-null="true"
        />
        
        <property 
            name="dataNotificacao"
            column="dt_notificacao"
            type="timestamp"
            not-null="true"
        />
        
        <property
            name="flagCriancaAleitamento"
            column="flag_crianca_aleitamento"
            type="java.lang.Long"
         />
         
         <property
            name="flagMulherAmamentando"
            column="flag_mulher_amamentando"
            type="java.lang.Long"
         />
        
        <property
            name="eventoAdverso"
            column="evento_adverso"
            type="java.lang.String"
        />
        
        <property
            name="classificacao"
            column="classificacao"
            type="java.lang.Long"
        />
         
        <many-to-one class="br.com.ksisolucoes.vo.cadsus.Profissional"
                     name="profissionalResponsavelNotificacao" not-null="true">
            <column name="cd_responsavel_notificacao"/>
        </many-to-one>
        
        <many-to-one
            class="br.com.ksisolucoes.vo.basico.Empresa"
            name="empresaNotificacao">
            <column name="empresa_notificacao" />
        </many-to-one>

		<property
            name="eapvPresente"
            column="eapv_presente"
            type="java.lang.Long"
        />
        
        <property
            name="doencaPreExistentes"
            column="doenca_pre_existentes"
            type="java.lang.Long"
        />
        
        <property
            name="somatorioDoencasExistentes"
            column="somatorio_doencas_existentes"
            type="java.lang.Long"
        />
        
        <property
            name="descricaoOutrosDoencasExistentes"
            column="ds_outros_doencas_existentes"
            type="java.lang.String"
        />
        
        <property
            name="medicacaoUso"
            column="medicacao_uso"
            type="java.lang.Long"
        />
        
        <property
            name="transfusaoUltimoMes"
            column="transfusao_ultimo_mes"
            type="java.lang.Long"
        />
        
        <property 
            name="dataTransfusaoUltimoMes"
            column="data_transfusao_ultimo_mes"
            type="timestamp"
        />
        
        <property
            name="historiaPreviaConvulsao"
            column="historia_previa_convulsao"
            type="java.lang.Long"
        />
        
        <property
            name="viajouUltimosDias"
            column="viajou_ultimos_dias"
            type="java.lang.Long"
        />
        
        <property 
            name="dataInicioViagem"
            column="data_inicio_viagem"
            type="timestamp"
        />
        
        <property 
            name="dataTerminoViagem"
            column="data_termino_viagem"
            type="timestamp"
        />

		<property
            name="paisViagem"
            column="pais_viagem"
            type="java.lang.String"
        />
        
        <property
            name="localViagem"
            column="local_viagem"
            type="java.lang.String"
        />
        
        <property
            name="ufViagem"
            column="uf_viagem"
            type="java.lang.String"
            length="2"
        />
        
        <property
            name="municipioViagem"
            column="municipio_viagem"
            type="java.lang.String"
        />
        
        <property
            name="atendimentoMedico"
            column="atendimento_medico"
            type="java.lang.Long"
        />
        
        <property
            name="tipoAtendimentoMedico"
            column="tipo_at_medico"
            type="java.lang.Long"
        />
        
        <property
            name="cnesHospitalAtendimentoMedico"
            column="cnes_hosp_at_medico"
            type="java.lang.String"
            length="12"
        />
        
        <property
            name="nomeHospitalAtendimentoMedico"
            column="nome_hospital_at_medico"
            type="java.lang.String"
        />
        
        <property 
            name="dataInternacaoAtendimentoMedico"
            column="data_internacao_at_medico"
            type="timestamp"
        />
        
        <property 
            name="dataAltaAtendimentoMedico"
            column="data_alta_at_medico"
            type="timestamp"
        />
        
        <property
            name="municipioAtendimentoMedico"
            column="municipio_at_medico"
            type="java.lang.String"
        />
        
        <property
            name="ufAtendimentoMedico"
            column="uf_at_medico"
            type="java.lang.String"
            length="12"
        />
        
        <property
            name="informacoesComplementares"
            column="informacoes_complementares"
            type="java.lang.String"
        />
        
        <property
            name="diagnostico"
            column="diagnostico"
            type="java.lang.String"
        />
        
        <property
            name="evolucao"
            column="evolucao"
            type="java.lang.Long"
        />
        
        <many-to-one class="br.com.ksisolucoes.vo.cadsus.Profissional"
                     name="profissionalResponsavelInvestigacao">
            <column name="cd_responsavel_investigacao"/>
        </many-to-one>
        
        <many-to-one
            class="br.com.ksisolucoes.vo.basico.Empresa"
            name="empresaInvestigacao">
            <column name="empresa_investigacao" />
        </many-to-one>
        
        <property
            name="classificacaoFinal"
            column="classificacao_final"
            type="java.lang.Long"
        />
        
        <property
            name="errosImunizacao"
            column="erros_imunizacao"
            type="java.lang.Long"
        />
        
        <property
            name="descricaoOutrosErrosImunizacao"
            column="ds_outros_erros_imunizacao"
            type="java.lang.String"
        />
        
        <property
            name="condutaErrosImunizacao"
            column="conduta_erros_imunizacao"
            type="java.lang.Long"
        />
        
        <property
            name="condutaErrosImunizacaoItem"
            column="conduta_erros_imu_item"
            type="java.lang.Long"
        />
        
        <property
            name="descricaoOutrosCondutaErrosImunizacaoItem"
            column="ds_outros_con_err_imu_item"
            type="java.lang.String"
        />
        
        <property
            name="descricaoOutrosCondutaFrenteEsquemaVacinacao"
            column="ds_outros_cond_fr_es_vac"
            type="java.lang.String"
        />
        
        <property
            name="descricaoOutrosClassificacaoCasual"
            column="ds_outros_class_casual"
            type="java.lang.String"
        />
        
        <many-to-one class="br.com.ksisolucoes.vo.cadsus.Profissional"
                     name="profissionalResponsavelEncerramento">
            <column name="cd_responsavel_encerramento"/>
        </many-to-one>
        
        <many-to-one
            class="br.com.ksisolucoes.vo.basico.Empresa"
            name="empresaEncerramento">
            <column name="empresa_encerramento" />
        </many-to-one>
        
    </class>
</hibernate-mapping>
