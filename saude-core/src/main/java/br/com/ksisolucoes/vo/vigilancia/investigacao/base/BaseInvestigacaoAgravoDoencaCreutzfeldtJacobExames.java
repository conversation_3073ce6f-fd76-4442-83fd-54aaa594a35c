package br.com.ksisolucoes.vo.vigilancia.investigacao.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the investigacao_agr_doenca_creutzfeldt_jacob_exames table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="investigacao_agr_doenca_creutzfeldt_jacob_exames"
 */

public abstract class BaseInvestigacaoAgravoDoencaCreutzfeldtJacobExames extends BaseRootVO implements Serializable {

	public static String REF = "InvestigacaoAgravoDoencaCreutzfeldtJacobExames";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_INVESTIGACAO_AGRAVO_DOENCA_CREUTZ_FELDT_JACOB = "investigacaoAgravoDoencaCreutzFeldtJacob";
	public static final String PROP_TIPO_EXAME = "tipoExame";
	public static final String PROP_RESULTADO_EXAME = "resultadoExame";


	// constructors
	public BaseInvestigacaoAgravoDoencaCreutzfeldtJacobExames () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseInvestigacaoAgravoDoencaCreutzfeldtJacobExames (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseInvestigacaoAgravoDoencaCreutzfeldtJacobExames (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoDoencaCreutzfeldtJacob investigacaoAgravoDoencaCreutzFeldtJacob) {

		this.setCodigo(codigo);
		this.setInvestigacaoAgravoDoencaCreutzFeldtJacob(investigacaoAgravoDoencaCreutzFeldtJacob);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String tipoExame;
	private java.lang.String resultadoExame;

	// many to one
	private br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoDoencaCreutzfeldtJacob investigacaoAgravoDoencaCreutzFeldtJacob;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_exame"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: exame_tipo
	 */
	public java.lang.String getTipoExame () {
		return getPropertyValue(this, tipoExame, PROP_TIPO_EXAME); 
	}

	/**
	 * Set the value related to the column: exame_tipo
	 * @param tipoExame the exame_tipo value
	 */
	public void setTipoExame (java.lang.String tipoExame) {
//        java.lang.String tipoExameOld = this.tipoExame;
		this.tipoExame = tipoExame;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoExame", tipoExameOld, tipoExame);
	}



	/**
	 * Return the value associated with the column: exame_resultado
	 */
	public java.lang.String getResultadoExame () {
		return getPropertyValue(this, resultadoExame, PROP_RESULTADO_EXAME); 
	}

	/**
	 * Set the value related to the column: exame_resultado
	 * @param resultadoExame the exame_resultado value
	 */
	public void setResultadoExame (java.lang.String resultadoExame) {
//        java.lang.String resultadoExameOld = this.resultadoExame;
		this.resultadoExame = resultadoExame;
//        this.getPropertyChangeSupport().firePropertyChange ("resultadoExame", resultadoExameOld, resultadoExame);
	}



	/**
	 * Return the value associated with the column: cd_investigacao_agr_doenca_creutzfeldt_jacob
	 */
	public br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoDoencaCreutzfeldtJacob getInvestigacaoAgravoDoencaCreutzFeldtJacob () {
		return getPropertyValue(this, investigacaoAgravoDoencaCreutzFeldtJacob, PROP_INVESTIGACAO_AGRAVO_DOENCA_CREUTZ_FELDT_JACOB); 
	}

	/**
	 * Set the value related to the column: cd_investigacao_agr_doenca_creutzfeldt_jacob
	 * @param investigacaoAgravoDoencaCreutzFeldtJacob the cd_investigacao_agr_doenca_creutzfeldt_jacob value
	 */
	public void setInvestigacaoAgravoDoencaCreutzFeldtJacob (br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoDoencaCreutzfeldtJacob investigacaoAgravoDoencaCreutzFeldtJacob) {
//        br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoDoencaCreutzfeldtJacob investigacaoAgravoDoencaCreutzFeldtJacobOld = this.investigacaoAgravoDoencaCreutzFeldtJacob;
		this.investigacaoAgravoDoencaCreutzFeldtJacob = investigacaoAgravoDoencaCreutzFeldtJacob;
//        this.getPropertyChangeSupport().firePropertyChange ("investigacaoAgravoDoencaCreutzFeldtJacob", investigacaoAgravoDoencaCreutzFeldtJacobOld, investigacaoAgravoDoencaCreutzFeldtJacob);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoDoencaCreutzfeldtJacobExames)) return false;
		else {
			br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoDoencaCreutzfeldtJacobExames investigacaoAgravoDoencaCreutzfeldtJacobExames = (br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoDoencaCreutzfeldtJacobExames) obj;
			if (null == this.getCodigo() || null == investigacaoAgravoDoencaCreutzfeldtJacobExames.getCodigo()) return false;
			else return (this.getCodigo().equals(investigacaoAgravoDoencaCreutzfeldtJacobExames.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}