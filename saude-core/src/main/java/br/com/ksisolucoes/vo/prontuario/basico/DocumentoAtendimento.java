package br.com.ksisolucoes.vo.prontuario.basico;

import br.com.celk.util.StringUtil;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.prontuario.basico.base.BaseDocumentoAtendimento;

import java.io.Serializable;
import java.util.Date;


public class DocumentoAtendimento extends BaseDocumentoAtendimento implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public DocumentoAtendimento () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public DocumentoAtendimento (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public DocumentoAtendimento (
		java.lang.Long codigo,
		java.lang.String descricao,
		java.util.Date dataCadastro) {

		super (
			codigo,
			descricao,
			dataCadastro);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
    
    public String getDescricaoTipoDocumentoFormatado() {
        String descricao;

        if (getTipoDocumentoAtendimento() == null) {
            descricao = "Geral";
        } else {
            descricao = getTipoDocumentoAtendimento().getDescricaoFormatado();
        }

        return descricao;
    }

	public String getDataMinutos() {
		return Data.formatarDataHora(new Date());
	}
}