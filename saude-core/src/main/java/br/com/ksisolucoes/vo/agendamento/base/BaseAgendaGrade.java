package br.com.ksisolucoes.vo.agendamento.base;

import java.io.Serializable;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;


/**
 * This is an object that contains data related to the agenda_grade table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="agenda_grade"
 */

public abstract class BaseAgendaGrade extends BaseRootVO implements Serializable {

	public static String REF = "AgendaGrade";
	public static final String PROP_DATA = "data";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_AGENDA = "agenda";
	public static final String PROP_HORA_INICIAL = "horaInicial";
	public static final String PROP_HORA_FINAL = "horaFinal";


	// constructors
	public BaseAgendaGrade () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseAgendaGrade (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseAgendaGrade (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.agendamento.Agenda agenda,
		java.util.Date data,
		java.util.Date horaInicial,
		java.util.Date horaFinal) {

		this.setCodigo(codigo);
		this.setAgenda(agenda);
		this.setData(data);
		this.setHoraInicial(horaInicial);
		this.setHoraFinal(horaFinal);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.util.Date data;
	private java.util.Date horaInicial;
	private java.util.Date horaFinal;

	// many to one
	private br.com.ksisolucoes.vo.agendamento.Agenda agenda;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_ag_grade"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: data
	 */
	public java.util.Date getData () {
		return getPropertyValue(this, data, PROP_DATA); 
	}

	/**
	 * Set the value related to the column: data
	 * @param data the data value
	 */
	public void setData (java.util.Date data) {
//        java.util.Date dataOld = this.data;
		this.data = data;
//        this.getPropertyChangeSupport().firePropertyChange ("data", dataOld, data);
	}



	/**
	 * Return the value associated with the column: hora_inicial
	 */
	public java.util.Date getHoraInicial () {
		return getPropertyValue(this, horaInicial, PROP_HORA_INICIAL); 
	}

	/**
	 * Set the value related to the column: hora_inicial
	 * @param horaInicial the hora_inicial value
	 */
	public void setHoraInicial (java.util.Date horaInicial) {
//        java.util.Date horaInicialOld = this.horaInicial;
		this.horaInicial = horaInicial;
//        this.getPropertyChangeSupport().firePropertyChange ("horaInicial", horaInicialOld, horaInicial);
	}



	/**
	 * Return the value associated with the column: hora_final
	 */
	public java.util.Date getHoraFinal () {
		return getPropertyValue(this, horaFinal, PROP_HORA_FINAL); 
	}

	/**
	 * Set the value related to the column: hora_final
	 * @param horaFinal the hora_final value
	 */
	public void setHoraFinal (java.util.Date horaFinal) {
//        java.util.Date horaFinalOld = this.horaFinal;
		this.horaFinal = horaFinal;
//        this.getPropertyChangeSupport().firePropertyChange ("horaFinal", horaFinalOld, horaFinal);
	}



	/**
	 * Return the value associated with the column: cd_agenda
	 */
	public br.com.ksisolucoes.vo.agendamento.Agenda getAgenda () {
		return getPropertyValue(this, agenda, PROP_AGENDA); 
	}

	/**
	 * Set the value related to the column: cd_agenda
	 * @param agenda the cd_agenda value
	 */
	public void setAgenda (br.com.ksisolucoes.vo.agendamento.Agenda agenda) {
//        br.com.ksisolucoes.vo.agendamento.Agenda agendaOld = this.agenda;
		this.agenda = agenda;
//        this.getPropertyChangeSupport().firePropertyChange ("agenda", agendaOld, agenda);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.agendamento.AgendaGrade)) return false;
		else {
			br.com.ksisolucoes.vo.agendamento.AgendaGrade agendaGrade = (br.com.ksisolucoes.vo.agendamento.AgendaGrade) obj;
			if (null == this.getCodigo() || null == agendaGrade.getCodigo()) return false;
			else return (this.getCodigo().equals(agendaGrade.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }

    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}