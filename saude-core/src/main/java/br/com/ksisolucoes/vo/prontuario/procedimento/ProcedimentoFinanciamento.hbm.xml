<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.prontuario.procedimento"  >
	<class name="ProcedimentoFinanciamento" table="procedimento_financiamento" >
		<id
			name="codigo"
			type="java.lang.Long"
			column="cd_financiamento"
		>
			<generator class="assigned" />
		</id> <version column="version" name="version" type="long" /> 

		<property
			column="ds_financiamento"
			length="100"
			name="descricao"
			not-null="true"
			type="string"
		 />

		<property
			column="dt_competencia"
			name="dataCompetencia"
			not-null="true"
			type="java.util.Date"
		 />

	</class>
</hibernate-mapping>
