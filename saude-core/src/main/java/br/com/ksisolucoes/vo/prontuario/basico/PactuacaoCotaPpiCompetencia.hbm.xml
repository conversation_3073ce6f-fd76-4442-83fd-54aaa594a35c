<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.prontuario.basico">
    <class name="br.com.ksisolucoes.vo.prontuario.basico.PpiScretaria" table="pactuacao_cota_ppi_competencia">

        <id name="codigo" type="java.lang.Long" column="cd_pactuacao_cota_ppi_competencia">
            <generator class="sequence">
                <param name="sequence">seq_pactuacao_cota_ppi_competencia</param>
            </generator>
        </id>

        <version column="version" name="version" type="long"/>

        <many-to-one name="pactuacaoExameCotaPpi" class="br.com.ksisolucoes.vo.prontuario.basico.PactuacaoExameCotaPpi" column="cd_pactuacao_exame_cota_ppi" not-null="true"/>
        <property name="realizado" column="realizado" type="java.lang.Double" not-null="true"/>
        <property name="dtCompetencia" column="dt_competencia" type="java.util.Date" not-null="true"/>
    </class>
</hibernate-mapping>
