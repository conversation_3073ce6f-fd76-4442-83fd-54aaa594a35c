<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
"-//Hibernate/Hibernate Mapping DTD//EN"
"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >
 
<hibernate-mapping package="br.com.ksisolucoes.vo.prontuario.basico"  >
    <class 
        name="IMTableSolicitacaoAgendamentoToAih"
        table="im_solic_agend_to_aih"
    >
        <id
            name="codigo"
            type="java.lang.Long"
            column="cd_im_aih"
        >
            <generator class="sequence">
                <param name="sequence">seq_im_solic_agend_to_aih</param>
            </generator>
        </id> 
        <version column="version" name="version" type="long" />
		          
        <many-to-one class="br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento"
                             name="solicitacaoAgendamento"
                             not-null="true">
            <column name="cd_solicitacao" />
        </many-to-one>
        
        <many-to-one class="br.com.ksisolucoes.vo.prontuario.hospital.Aih"
                             name="aih"
                             not-null="true">
            <column name="cd_aut_intern_hosp" />
        </many-to-one>

    </class>
</hibernate-mapping>