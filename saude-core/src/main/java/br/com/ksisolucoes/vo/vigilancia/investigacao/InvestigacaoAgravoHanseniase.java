package br.com.ksisolucoes.vo.vigilancia.investigacao;

import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo;
import br.com.ksisolucoes.vo.vigilancia.investigacao.base.BaseInvestigacaoAgravoHanseniase;
import br.com.ksisolucoes.vo.vigilancia.investigacao.base.BaseInvestigacaoAgravoMalaria;

import java.io.Serializable;


public class InvestigacaoAgravoHanseniase extends BaseInvestigacaoAgravoHanseniase implements CodigoManager {
    public InvestigacaoAgravoHanseniase() { super(); }

    public InvestigacaoAgravoHanseniase(Long codigo) {
        super(codigo);
    }

    public InvestigacaoAgravoHanseniase(
            Long codigo,
            RegistroAgravo registroAgravo,
            String flagInformacoesComplementares
    ) {
        super (
            codigo,
            registroAgravo,
            flagInformacoesComplementares
        );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    public void setCodigoManager(Serializable key) {
        this.setCodigo((Long) key);
    }

    public static InvestigacaoAgravoHanseniase buscaPorRegistroAgravo(RegistroAgravo registroAgravo) {
        InvestigacaoAgravoHanseniase investigacao =
                LoadManager.getInstance(InvestigacaoAgravoHanseniase.class)
                        .addProperties(new HQLProperties(InvestigacaoAgravoHanseniase.class).getProperties())
                        .addProperties(new HQLProperties(
                                RegistroAgravo.class,
                                VOUtils.montarPath(InvestigacaoAgravoHanseniase.PROP_REGISTRO_AGRAVO)).getProperties())
                        .addParameter(new QueryCustom.QueryCustomParameter(
                                VOUtils.montarPath(InvestigacaoAgravoHanseniase.PROP_REGISTRO_AGRAVO, RegistroAgravo.PROP_CODIGO),
                                registroAgravo.getCodigo()))
                        .start().getVO();
        return investigacao;
    }
}
