package br.com.ksisolucoes.vo.atendimento.raas.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the raas_configuracao table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="raas_configuracao"
 */

public abstract class BaseRaasConfiguracao extends BaseRootVO implements Serializable {

	public static String REF = "RaasConfiguracao";
	public static final String PROP_CNPJ_EMPRESA = "cnpjEmpresa";
	public static final String PROP_NOME_DESTINO = "nomeDestino";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_VERSAO = "versao";
	public static final String PROP_TIPO_DESTINO = "tipoDestino";
	public static final String PROP_NOME_EMPRESA = "nomeEmpresa";
	public static final String PROP_SIGLA_ORIGEM = "siglaOrigem";


	// constructors
	public BaseRaasConfiguracao () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseRaasConfiguracao (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String nomeEmpresa;
	private java.lang.String cnpjEmpresa;
	private java.lang.String siglaOrigem;
	private java.lang.String nomeDestino;
	private java.lang.String tipoDestino;
	private java.lang.String versao;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_raas_configuracao"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: nome_empresa
	 */
	public java.lang.String getNomeEmpresa () {
		return getPropertyValue(this, nomeEmpresa, PROP_NOME_EMPRESA); 
	}

	/**
	 * Set the value related to the column: nome_empresa
	 * @param nomeEmpresa the nome_empresa value
	 */
	public void setNomeEmpresa (java.lang.String nomeEmpresa) {
//        java.lang.String nomeEmpresaOld = this.nomeEmpresa;
		this.nomeEmpresa = nomeEmpresa;
//        this.getPropertyChangeSupport().firePropertyChange ("nomeEmpresa", nomeEmpresaOld, nomeEmpresa);
	}



	/**
	 * Return the value associated with the column: cnpj_empresa    
	 */
	public java.lang.String getCnpjEmpresa () {
		return getPropertyValue(this, cnpjEmpresa, PROP_CNPJ_EMPRESA); 
	}

	/**
	 * Set the value related to the column: cnpj_empresa    
	 * @param cnpjEmpresa the cnpj_empresa     value
	 */
	public void setCnpjEmpresa (java.lang.String cnpjEmpresa) {
//        java.lang.String cnpjEmpresaOld = this.cnpjEmpresa;
		this.cnpjEmpresa = cnpjEmpresa;
//        this.getPropertyChangeSupport().firePropertyChange ("cnpjEmpresa", cnpjEmpresaOld, cnpjEmpresa);
	}



	/**
	 * Return the value associated with the column: sigla_origem
	 */
	public java.lang.String getSiglaOrigem () {
		return getPropertyValue(this, siglaOrigem, PROP_SIGLA_ORIGEM); 
	}

	/**
	 * Set the value related to the column: sigla_origem
	 * @param siglaOrigem the sigla_origem value
	 */
	public void setSiglaOrigem (java.lang.String siglaOrigem) {
//        java.lang.String siglaOrigemOld = this.siglaOrigem;
		this.siglaOrigem = siglaOrigem;
//        this.getPropertyChangeSupport().firePropertyChange ("siglaOrigem", siglaOrigemOld, siglaOrigem);
	}



	/**
	 * Return the value associated with the column: nome_destino
	 */
	public java.lang.String getNomeDestino () {
		return getPropertyValue(this, nomeDestino, PROP_NOME_DESTINO); 
	}

	/**
	 * Set the value related to the column: nome_destino
	 * @param nomeDestino the nome_destino value
	 */
	public void setNomeDestino (java.lang.String nomeDestino) {
//        java.lang.String nomeDestinoOld = this.nomeDestino;
		this.nomeDestino = nomeDestino;
//        this.getPropertyChangeSupport().firePropertyChange ("nomeDestino", nomeDestinoOld, nomeDestino);
	}



	/**
	 * Return the value associated with the column: tipo_destino
	 */
	public java.lang.String getTipoDestino () {
		return getPropertyValue(this, tipoDestino, PROP_TIPO_DESTINO); 
	}

	/**
	 * Set the value related to the column: tipo_destino
	 * @param tipoDestino the tipo_destino value
	 */
	public void setTipoDestino (java.lang.String tipoDestino) {
//        java.lang.String tipoDestinoOld = this.tipoDestino;
		this.tipoDestino = tipoDestino;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoDestino", tipoDestinoOld, tipoDestino);
	}



	/**
	 * Return the value associated with the column: versao
	 */
	public java.lang.String getVersao () {
		return getPropertyValue(this, versao, PROP_VERSAO); 
	}

	/**
	 * Set the value related to the column: versao
	 * @param versao the versao value
	 */
	public void setVersao (java.lang.String versao) {
//        java.lang.String versaoOld = this.versao;
		this.versao = versao;
//        this.getPropertyChangeSupport().firePropertyChange ("versao", versaoOld, versao);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.atendimento.raas.RaasConfiguracao)) return false;
		else {
			br.com.ksisolucoes.vo.atendimento.raas.RaasConfiguracao raasConfiguracao = (br.com.ksisolucoes.vo.atendimento.raas.RaasConfiguracao) obj;
			if (null == this.getCodigo() || null == raasConfiguracao.getCodigo()) return false;
			else return (this.getCodigo().equals(raasConfiguracao.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}