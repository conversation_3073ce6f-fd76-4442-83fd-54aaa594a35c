package br.com.ksisolucoes.vo.cadsus.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the usuario_cadsus_exclusao table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="usuario_cadsus_exclusao"
 */

public abstract class BaseUsuarioCadsusExclusao extends BaseRootVO implements Serializable {

	public static String REF = "UsuarioCadsusExclusao";
	public static final String PROP_STATUS = "status";
	public static final String PROP_USUARIO_CANCELAMENTO = "usuarioCancelamento";
	public static final String PROP_USUARIO_UNIFICACAO = "usuarioUnificacao";
	public static final String PROP_USUARIO = "usuario";
	public static final String PROP_MOTIVO_EXCLUSAO_PACIENTE = "motivoExclusaoPaciente";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_USUARIO_CONFIRMACAO = "usuarioConfirmacao";
	public static final String PROP_DATA_CADASTRO = "dataCadastro";
	public static final String PROP_DATA_CANCELAMENTO = "dataCancelamento";
	public static final String PROP_DATA_UNIFICACAO = "dataUnificacao";
	public static final String PROP_MOTIVO_REVERSAO = "motivoReversao";
	public static final String PROP_DATA_CONFIRMACAO = "dataConfirmacao";
	public static final String PROP_USUARIO_CADSUS = "usuarioCadsus";
	public static final String PROP_SITUACAO_PACIENTE = "situacaoPaciente";
	public static final String PROP_DATA_REVERSAO = "dataReversao";
	public static final String PROP_USUARIO_REVERSAO = "usuarioReversao";


	// constructors
	public BaseUsuarioCadsusExclusao () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseUsuarioCadsusExclusao (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseUsuarioCadsusExclusao (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		java.lang.Long status,
		java.util.Date dataCadastro,
		java.lang.Long situacaoPaciente,
		java.lang.Long motivoExclusaoPaciente) {

		this.setCodigo(codigo);
		this.setUsuarioCadsus(usuarioCadsus);
		this.setUsuario(usuario);
		this.setStatus(status);
		this.setDataCadastro(dataCadastro);
		this.setSituacaoPaciente(situacaoPaciente);
		this.setMotivoExclusaoPaciente(motivoExclusaoPaciente);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Long status;
	private java.util.Date dataCadastro;
	private java.util.Date dataConfirmacao;
	private java.util.Date dataReversao;
	private java.util.Date dataUnificacao;
	private java.util.Date dataCancelamento;
	private java.lang.String motivoReversao;
	private java.lang.Long situacaoPaciente;
	private java.lang.Long motivoExclusaoPaciente;

	// many to one
	private br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus;
	private br.com.ksisolucoes.vo.controle.Usuario usuario;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioConfirmacao;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioReversao;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioUnificacao;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamento;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_usuario_cadsus_exclusao"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: status
	 */
	public java.lang.Long getStatus () {
		return getPropertyValue(this, status, PROP_STATUS); 
	}

	/**
	 * Set the value related to the column: status
	 * @param status the status value
	 */
	public void setStatus (java.lang.Long status) {
//        java.lang.Long statusOld = this.status;
		this.status = status;
//        this.getPropertyChangeSupport().firePropertyChange ("status", statusOld, status);
	}



	/**
	 * Return the value associated with the column: dt_cadastro
	 */
	public java.util.Date getDataCadastro () {
		return getPropertyValue(this, dataCadastro, PROP_DATA_CADASTRO); 
	}

	/**
	 * Set the value related to the column: dt_cadastro
	 * @param dataCadastro the dt_cadastro value
	 */
	public void setDataCadastro (java.util.Date dataCadastro) {
//        java.util.Date dataCadastroOld = this.dataCadastro;
		this.dataCadastro = dataCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCadastro", dataCadastroOld, dataCadastro);
	}



	/**
	 * Return the value associated with the column: dt_confirmacao
	 */
	public java.util.Date getDataConfirmacao () {
		return getPropertyValue(this, dataConfirmacao, PROP_DATA_CONFIRMACAO); 
	}

	/**
	 * Set the value related to the column: dt_confirmacao
	 * @param dataConfirmacao the dt_confirmacao value
	 */
	public void setDataConfirmacao (java.util.Date dataConfirmacao) {
//        java.util.Date dataConfirmacaoOld = this.dataConfirmacao;
		this.dataConfirmacao = dataConfirmacao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataConfirmacao", dataConfirmacaoOld, dataConfirmacao);
	}



	/**
	 * Return the value associated with the column: dt_reversao
	 */
	public java.util.Date getDataReversao () {
		return getPropertyValue(this, dataReversao, PROP_DATA_REVERSAO); 
	}

	/**
	 * Set the value related to the column: dt_reversao
	 * @param dataReversao the dt_reversao value
	 */
	public void setDataReversao (java.util.Date dataReversao) {
//        java.util.Date dataReversaoOld = this.dataReversao;
		this.dataReversao = dataReversao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataReversao", dataReversaoOld, dataReversao);
	}



	/**
	 * Return the value associated with the column: dt_unificacao
	 */
	public java.util.Date getDataUnificacao () {
		return getPropertyValue(this, dataUnificacao, PROP_DATA_UNIFICACAO); 
	}

	/**
	 * Set the value related to the column: dt_unificacao
	 * @param dataUnificacao the dt_unificacao value
	 */
	public void setDataUnificacao (java.util.Date dataUnificacao) {
//        java.util.Date dataUnificacaoOld = this.dataUnificacao;
		this.dataUnificacao = dataUnificacao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataUnificacao", dataUnificacaoOld, dataUnificacao);
	}



	/**
	 * Return the value associated with the column: dt_cancelamento
	 */
	public java.util.Date getDataCancelamento () {
		return getPropertyValue(this, dataCancelamento, PROP_DATA_CANCELAMENTO); 
	}

	/**
	 * Set the value related to the column: dt_cancelamento
	 * @param dataCancelamento the dt_cancelamento value
	 */
	public void setDataCancelamento (java.util.Date dataCancelamento) {
//        java.util.Date dataCancelamentoOld = this.dataCancelamento;
		this.dataCancelamento = dataCancelamento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCancelamento", dataCancelamentoOld, dataCancelamento);
	}



	/**
	 * Return the value associated with the column: motivo_reversao
	 */
	public java.lang.String getMotivoReversao () {
		return getPropertyValue(this, motivoReversao, PROP_MOTIVO_REVERSAO); 
	}

	/**
	 * Set the value related to the column: motivo_reversao
	 * @param motivoReversao the motivo_reversao value
	 */
	public void setMotivoReversao (java.lang.String motivoReversao) {
//        java.lang.String motivoReversaoOld = this.motivoReversao;
		this.motivoReversao = motivoReversao;
//        this.getPropertyChangeSupport().firePropertyChange ("motivoReversao", motivoReversaoOld, motivoReversao);
	}



	/**
	 * Return the value associated with the column: situacao_paciente
	 */
	public java.lang.Long getSituacaoPaciente () {
		return getPropertyValue(this, situacaoPaciente, PROP_SITUACAO_PACIENTE); 
	}

	/**
	 * Set the value related to the column: situacao_paciente
	 * @param situacaoPaciente the situacao_paciente value
	 */
	public void setSituacaoPaciente (java.lang.Long situacaoPaciente) {
//        java.lang.Long situacaoPacienteOld = this.situacaoPaciente;
		this.situacaoPaciente = situacaoPaciente;
//        this.getPropertyChangeSupport().firePropertyChange ("situacaoPaciente", situacaoPacienteOld, situacaoPaciente);
	}



	/**
	 * Return the value associated with the column: mot_exclusao_paciente
	 */
	public java.lang.Long getMotivoExclusaoPaciente () {
		return getPropertyValue(this, motivoExclusaoPaciente, PROP_MOTIVO_EXCLUSAO_PACIENTE); 
	}

	/**
	 * Set the value related to the column: mot_exclusao_paciente
	 * @param motivoExclusaoPaciente the mot_exclusao_paciente value
	 */
	public void setMotivoExclusaoPaciente (java.lang.Long motivoExclusaoPaciente) {
//        java.lang.Long motivoExclusaoPacienteOld = this.motivoExclusaoPaciente;
		this.motivoExclusaoPaciente = motivoExclusaoPaciente;
//        this.getPropertyChangeSupport().firePropertyChange ("motivoExclusaoPaciente", motivoExclusaoPacienteOld, motivoExclusaoPaciente);
	}



	/**
	 * Return the value associated with the column: cd_usuario_cadsus
	 */
	public br.com.ksisolucoes.vo.cadsus.UsuarioCadsus getUsuarioCadsus () {
		return getPropertyValue(this, usuarioCadsus, PROP_USUARIO_CADSUS); 
	}

	/**
	 * Set the value related to the column: cd_usuario_cadsus
	 * @param usuarioCadsus the cd_usuario_cadsus value
	 */
	public void setUsuarioCadsus (br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus) {
//        br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsusOld = this.usuarioCadsus;
		this.usuarioCadsus = usuarioCadsus;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCadsus", usuarioCadsusOld, usuarioCadsus);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuario the cd_usuario value
	 */
	public void setUsuario (br.com.ksisolucoes.vo.controle.Usuario usuario) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}



	/**
	 * Return the value associated with the column: cd_usuario_confirmacao
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioConfirmacao () {
		return getPropertyValue(this, usuarioConfirmacao, PROP_USUARIO_CONFIRMACAO); 
	}

	/**
	 * Set the value related to the column: cd_usuario_confirmacao
	 * @param usuarioConfirmacao the cd_usuario_confirmacao value
	 */
	public void setUsuarioConfirmacao (br.com.ksisolucoes.vo.controle.Usuario usuarioConfirmacao) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioConfirmacaoOld = this.usuarioConfirmacao;
		this.usuarioConfirmacao = usuarioConfirmacao;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioConfirmacao", usuarioConfirmacaoOld, usuarioConfirmacao);
	}



	/**
	 * Return the value associated with the column: cd_usuario_reversao
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioReversao () {
		return getPropertyValue(this, usuarioReversao, PROP_USUARIO_REVERSAO); 
	}

	/**
	 * Set the value related to the column: cd_usuario_reversao
	 * @param usuarioReversao the cd_usuario_reversao value
	 */
	public void setUsuarioReversao (br.com.ksisolucoes.vo.controle.Usuario usuarioReversao) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioReversaoOld = this.usuarioReversao;
		this.usuarioReversao = usuarioReversao;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioReversao", usuarioReversaoOld, usuarioReversao);
	}



	/**
	 * Return the value associated with the column: cd_usuario_unificacao
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioUnificacao () {
		return getPropertyValue(this, usuarioUnificacao, PROP_USUARIO_UNIFICACAO); 
	}

	/**
	 * Set the value related to the column: cd_usuario_unificacao
	 * @param usuarioUnificacao the cd_usuario_unificacao value
	 */
	public void setUsuarioUnificacao (br.com.ksisolucoes.vo.controle.Usuario usuarioUnificacao) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioUnificacaoOld = this.usuarioUnificacao;
		this.usuarioUnificacao = usuarioUnificacao;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioUnificacao", usuarioUnificacaoOld, usuarioUnificacao);
	}



	/**
	 * Return the value associated with the column: cd_usuario_cancelamento
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioCancelamento () {
		return getPropertyValue(this, usuarioCancelamento, PROP_USUARIO_CANCELAMENTO); 
	}

	/**
	 * Set the value related to the column: cd_usuario_cancelamento
	 * @param usuarioCancelamento the cd_usuario_cancelamento value
	 */
	public void setUsuarioCancelamento (br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamento) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamentoOld = this.usuarioCancelamento;
		this.usuarioCancelamento = usuarioCancelamento;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCancelamento", usuarioCancelamentoOld, usuarioCancelamento);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.cadsus.UsuarioCadsusExclusao)) return false;
		else {
			br.com.ksisolucoes.vo.cadsus.UsuarioCadsusExclusao usuarioCadsusExclusao = (br.com.ksisolucoes.vo.cadsus.UsuarioCadsusExclusao) obj;
			if (null == this.getCodigo() || null == usuarioCadsusExclusao.getCodigo()) return false;
			else return (this.getCodigo().equals(usuarioCadsusExclusao.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}