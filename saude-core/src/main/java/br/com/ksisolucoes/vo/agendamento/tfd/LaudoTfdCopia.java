package br.com.ksisolucoes.vo.agendamento.tfd;

import java.io.Serializable;

import br.com.ksisolucoes.vo.agendamento.tfd.base.BaseLaudoTfdCopia;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class LaudoTfdCopia extends BaseLaudoTfdCopia implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public LaudoTfdCopia () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public LaudoTfdCopia (java.lang.Long codigo) {
		super(codigo);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}