package br.com.ksisolucoes.vo.prontuario.basico.tuberculose.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the tuberc_acompanhamento table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="tuberc_acompanhamento"
 */

public abstract class BaseTuberculoseAcompanhamento extends BaseRootVO implements Serializable {

	public static String REF = "TuberculoseAcompanhamento";
	public static final String PROP_USUARIO_UPDATE = "usuarioUpdate";
	public static final String PROP_DOENCAS_AGRAVOS_ALCOOLISMO = "doencasAgravosAlcoolismo";
	public static final String PROP_NUMERO_SINAN = "numeroSinan";
	public static final String PROP_PPD = "ppd";
	public static final String PROP_DATA_INICIO_TRATAMENTO = "dataInicioTratamento";
	public static final String PROP_DATA_CADASTRO = "dataCadastro";
	public static final String PROP_USUARIO_CADASTRO = "usuarioCadastro";
	public static final String PROP_FORMA_CLINICA_PULMONAR = "formaClinicaPulmonar";
	public static final String PROP_DOENCAS_AGRAVOS_DOENCA_MENTAL = "doencasAgravosDoencaMental";
	public static final String PROP_CULTURA_OUTROS = "culturaOutros";
	public static final String PROP_DATA_ENCERRAMENTO = "dataEncerramento";
	public static final String PROP_MOTIVO_ENCERRAMENTO = "motivoEncerramento";
	public static final String PROP_OBSERVACAO = "observacao";
	public static final String PROP_ATENDIMENTO = "atendimento";
	public static final String PROP_DOENCAS_AGRAVOS_TABAGISMO = "doencasAgravosTabagismo";
	public static final String PROP_ESQUEMA_TRATAMENTO = "esquemaTratamento";
	public static final String PROP_POPULACAO_PRIVADA_LIBERDADE = "populacaoPrivadaLiberdade";
	public static final String PROP_DOENCAS_AGRAVOS_USO_DROGAS_ILICITAS = "doencasAgravosUsoDrogasIlicitas";
	public static final String PROP_POPULACAO_IMIGRANTE = "populacaoImigrante";
	public static final String PROP_DOENCAS_AGRAVOS_DESCRICAO_OUTROS = "doencasAgravosDescricaoOutros";
	public static final String PROP_TERAPIA_ANTIRRETROVIAL_DURANTE_TRATAMENTO = "terapiaAntirretrovialDuranteTratamento";
	public static final String PROP_POPULACAO_PROFISSIONAIS_SAUDE = "populacaoProfissionaisSaude";
	public static final String PROP_DOENCAS_AGRAVOS_AIDS = "doencasAgravosAids";
	public static final String PROP_FORMA_TRATAMENTO = "formaTratamento";
	public static final String PROP_DOENCAS_AGRAVOS_OUTRO = "doencasAgravosOutro";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_HISTOPATOLOGIA = "histopatologia";
	public static final String PROP_RAIO_X_TORAX = "raioXTorax";
	public static final String PROP_DATA_REGISTRO_SINAN = "dataRegistroSinan";
	public static final String PROP_CONTACTANTE_EXAMINADOS = "contactanteExaminados";
	public static final String PROP_DOENCAS_AGRAVOS_DIABETES = "doencasAgravosDiabetes";
	public static final String PROP_BACILOSCOPIA1_AMOSTRA = "baciloscopia1Amostra";
	public static final String PROP_BACILOSCOPIA2_AMOSTRA = "baciloscopia2Amostra";
	public static final String PROP_FORMA_CLINICA_EXTRAPULMONAR = "formaClinicaExtrapulmonar";
	public static final String PROP_CULTURA_ESCARRO = "culturaEscarro";
	public static final String PROP_DATA_USUARIO = "dataUsuario";
	public static final String PROP_POPULACAO_SITUACAO_RUA = "populacaoSituacaoRua";
	public static final String PROP_CONTACTANTE_REGISTRADOS = "contactanteRegistrados";
	public static final String PROP_HIV = "hiv";
	public static final String PROP_TUBERCULOSE_SINTOMATICO = "tuberculoseSintomatico";
	public static final String PROP_OUTROS_EXAMES = "outrosExames";
	public static final String PROP_TIPO_ENTRADA = "tipoEntrada";


	// constructors
	public BaseTuberculoseAcompanhamento () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseTuberculoseAcompanhamento (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseTuberculoseAcompanhamento (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.prontuario.basico.tuberculose.TuberculoseSintomatico tuberculoseSintomatico,
		br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimento,
		br.com.ksisolucoes.vo.controle.Usuario usuarioCadastro,
		br.com.ksisolucoes.vo.controle.Usuario usuarioUpdate,
		java.util.Date dataEncerramento,
		java.util.Date dataCadastro,
		java.util.Date dataUsuario) {

		this.setCodigo(codigo);
		this.setTuberculoseSintomatico(tuberculoseSintomatico);
		this.setAtendimento(atendimento);
		this.setUsuarioCadastro(usuarioCadastro);
		this.setUsuarioUpdate(usuarioUpdate);
		this.setDataEncerramento(dataEncerramento);
		this.setDataCadastro(dataCadastro);
		this.setDataUsuario(dataUsuario);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Long numeroSinan;
	private java.util.Date dataRegistroSinan;
	private java.lang.Long baciloscopia1Amostra;
	private java.lang.Long baciloscopia2Amostra;
	private java.lang.Long culturaEscarro;
	private java.lang.Long culturaOutros;
	private java.lang.Long ppd;
	private java.lang.Long histopatologia;
	private java.lang.Long raioXTorax;
	private java.lang.Long hiv;
	private java.lang.Long outrosExames;
	private java.lang.Long formaClinicaPulmonar;
	private java.lang.Long formaClinicaExtrapulmonar;
	private java.lang.Long tipoEntrada;
	private java.lang.Long esquemaTratamento;
	private java.util.Date dataInicioTratamento;
	private java.lang.Long formaTratamento;
	private java.lang.Long contactanteRegistrados;
	private java.lang.Long contactanteExaminados;
	private java.lang.String observacao;
	private java.lang.Long motivoEncerramento;
	private java.util.Date dataEncerramento;
	private java.util.Date dataCadastro;
	private java.util.Date dataUsuario;
	private java.lang.Long terapiaAntirretrovialDuranteTratamento;
	private java.lang.Long populacaoPrivadaLiberdade;
	private java.lang.Long populacaoSituacaoRua;
	private java.lang.Long populacaoProfissionaisSaude;
	private java.lang.Long populacaoImigrante;
	private java.lang.Long doencasAgravosAids;
	private java.lang.Long doencasAgravosUsoDrogasIlicitas;
	private java.lang.Long doencasAgravosAlcoolismo;
	private java.lang.Long doencasAgravosDiabetes;
	private java.lang.Long doencasAgravosDoencaMental;
	private java.lang.Long doencasAgravosTabagismo;
	private java.lang.Long doencasAgravosOutro;
	private java.lang.String doencasAgravosDescricaoOutros;

	// many to one
	private br.com.ksisolucoes.vo.prontuario.basico.tuberculose.TuberculoseSintomatico tuberculoseSintomatico;
	private br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimento;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioCadastro;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioUpdate;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_tuberc_acompanhamento"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: nr_sinan
	 */
	public java.lang.Long getNumeroSinan () {
		return getPropertyValue(this, numeroSinan, PROP_NUMERO_SINAN); 
	}

	/**
	 * Set the value related to the column: nr_sinan
	 * @param numeroSinan the nr_sinan value
	 */
	public void setNumeroSinan (java.lang.Long numeroSinan) {
//        java.lang.Long numeroSinanOld = this.numeroSinan;
		this.numeroSinan = numeroSinan;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroSinan", numeroSinanOld, numeroSinan);
	}



	/**
	 * Return the value associated with the column: dt_registro_sinan
	 */
	public java.util.Date getDataRegistroSinan () {
		return getPropertyValue(this, dataRegistroSinan, PROP_DATA_REGISTRO_SINAN); 
	}

	/**
	 * Set the value related to the column: dt_registro_sinan
	 * @param dataRegistroSinan the dt_registro_sinan value
	 */
	public void setDataRegistroSinan (java.util.Date dataRegistroSinan) {
//        java.util.Date dataRegistroSinanOld = this.dataRegistroSinan;
		this.dataRegistroSinan = dataRegistroSinan;
//        this.getPropertyChangeSupport().firePropertyChange ("dataRegistroSinan", dataRegistroSinanOld, dataRegistroSinan);
	}



	/**
	 * Return the value associated with the column: baciloscopia_1_amostra
	 */
	public java.lang.Long getBaciloscopia1Amostra () {
		return getPropertyValue(this, baciloscopia1Amostra, PROP_BACILOSCOPIA1_AMOSTRA); 
	}

	/**
	 * Set the value related to the column: baciloscopia_1_amostra
	 * @param baciloscopia1Amostra the baciloscopia_1_amostra value
	 */
	public void setBaciloscopia1Amostra (java.lang.Long baciloscopia1Amostra) {
//        java.lang.Long baciloscopia1AmostraOld = this.baciloscopia1Amostra;
		this.baciloscopia1Amostra = baciloscopia1Amostra;
//        this.getPropertyChangeSupport().firePropertyChange ("baciloscopia1Amostra", baciloscopia1AmostraOld, baciloscopia1Amostra);
	}



	/**
	 * Return the value associated with the column: baciloscopia_2_amostra
	 */
	public java.lang.Long getBaciloscopia2Amostra () {
		return getPropertyValue(this, baciloscopia2Amostra, PROP_BACILOSCOPIA2_AMOSTRA); 
	}

	/**
	 * Set the value related to the column: baciloscopia_2_amostra
	 * @param baciloscopia2Amostra the baciloscopia_2_amostra value
	 */
	public void setBaciloscopia2Amostra (java.lang.Long baciloscopia2Amostra) {
//        java.lang.Long baciloscopia2AmostraOld = this.baciloscopia2Amostra;
		this.baciloscopia2Amostra = baciloscopia2Amostra;
//        this.getPropertyChangeSupport().firePropertyChange ("baciloscopia2Amostra", baciloscopia2AmostraOld, baciloscopia2Amostra);
	}



	/**
	 * Return the value associated with the column: cultura_escarro
	 */
	public java.lang.Long getCulturaEscarro () {
		return getPropertyValue(this, culturaEscarro, PROP_CULTURA_ESCARRO); 
	}

	/**
	 * Set the value related to the column: cultura_escarro
	 * @param culturaEscarro the cultura_escarro value
	 */
	public void setCulturaEscarro (java.lang.Long culturaEscarro) {
//        java.lang.Long culturaEscarroOld = this.culturaEscarro;
		this.culturaEscarro = culturaEscarro;
//        this.getPropertyChangeSupport().firePropertyChange ("culturaEscarro", culturaEscarroOld, culturaEscarro);
	}



	/**
	 * Return the value associated with the column: cultura_outros
	 */
	public java.lang.Long getCulturaOutros () {
		return getPropertyValue(this, culturaOutros, PROP_CULTURA_OUTROS); 
	}

	/**
	 * Set the value related to the column: cultura_outros
	 * @param culturaOutros the cultura_outros value
	 */
	public void setCulturaOutros (java.lang.Long culturaOutros) {
//        java.lang.Long culturaOutrosOld = this.culturaOutros;
		this.culturaOutros = culturaOutros;
//        this.getPropertyChangeSupport().firePropertyChange ("culturaOutros", culturaOutrosOld, culturaOutros);
	}



	/**
	 * Return the value associated with the column: ppd
	 */
	public java.lang.Long getPpd () {
		return getPropertyValue(this, ppd, PROP_PPD); 
	}

	/**
	 * Set the value related to the column: ppd
	 * @param ppd the ppd value
	 */
	public void setPpd (java.lang.Long ppd) {
//        java.lang.Long ppdOld = this.ppd;
		this.ppd = ppd;
//        this.getPropertyChangeSupport().firePropertyChange ("ppd", ppdOld, ppd);
	}



	/**
	 * Return the value associated with the column: histopatologia
	 */
	public java.lang.Long getHistopatologia () {
		return getPropertyValue(this, histopatologia, PROP_HISTOPATOLOGIA); 
	}

	/**
	 * Set the value related to the column: histopatologia
	 * @param histopatologia the histopatologia value
	 */
	public void setHistopatologia (java.lang.Long histopatologia) {
//        java.lang.Long histopatologiaOld = this.histopatologia;
		this.histopatologia = histopatologia;
//        this.getPropertyChangeSupport().firePropertyChange ("histopatologia", histopatologiaOld, histopatologia);
	}



	/**
	 * Return the value associated with the column: raio_x_torax
	 */
	public java.lang.Long getRaioXTorax () {
		return getPropertyValue(this, raioXTorax, PROP_RAIO_X_TORAX); 
	}

	/**
	 * Set the value related to the column: raio_x_torax
	 * @param raioXTorax the raio_x_torax value
	 */
	public void setRaioXTorax (java.lang.Long raioXTorax) {
//        java.lang.Long raioXToraxOld = this.raioXTorax;
		this.raioXTorax = raioXTorax;
//        this.getPropertyChangeSupport().firePropertyChange ("raioXTorax", raioXToraxOld, raioXTorax);
	}



	/**
	 * Return the value associated with the column: hiv
	 */
	public java.lang.Long getHiv () {
		return getPropertyValue(this, hiv, PROP_HIV); 
	}

	/**
	 * Set the value related to the column: hiv
	 * @param hiv the hiv value
	 */
	public void setHiv (java.lang.Long hiv) {
//        java.lang.Long hivOld = this.hiv;
		this.hiv = hiv;
//        this.getPropertyChangeSupport().firePropertyChange ("hiv", hivOld, hiv);
	}



	/**
	 * Return the value associated with the column: outros_exames
	 */
	public java.lang.Long getOutrosExames () {
		return getPropertyValue(this, outrosExames, PROP_OUTROS_EXAMES); 
	}

	/**
	 * Set the value related to the column: outros_exames
	 * @param outrosExames the outros_exames value
	 */
	public void setOutrosExames (java.lang.Long outrosExames) {
//        java.lang.Long outrosExamesOld = this.outrosExames;
		this.outrosExames = outrosExames;
//        this.getPropertyChangeSupport().firePropertyChange ("outrosExames", outrosExamesOld, outrosExames);
	}



	/**
	 * Return the value associated with the column: forma_clin_pulmonar
	 */
	public java.lang.Long getFormaClinicaPulmonar () {
		return getPropertyValue(this, formaClinicaPulmonar, PROP_FORMA_CLINICA_PULMONAR); 
	}

	/**
	 * Set the value related to the column: forma_clin_pulmonar
	 * @param formaClinicaPulmonar the forma_clin_pulmonar value
	 */
	public void setFormaClinicaPulmonar (java.lang.Long formaClinicaPulmonar) {
//        java.lang.Long formaClinicaPulmonarOld = this.formaClinicaPulmonar;
		this.formaClinicaPulmonar = formaClinicaPulmonar;
//        this.getPropertyChangeSupport().firePropertyChange ("formaClinicaPulmonar", formaClinicaPulmonarOld, formaClinicaPulmonar);
	}



	/**
	 * Return the value associated with the column: forma_clin_extrapulmonar
	 */
	public java.lang.Long getFormaClinicaExtrapulmonar () {
		return getPropertyValue(this, formaClinicaExtrapulmonar, PROP_FORMA_CLINICA_EXTRAPULMONAR); 
	}

	/**
	 * Set the value related to the column: forma_clin_extrapulmonar
	 * @param formaClinicaExtrapulmonar the forma_clin_extrapulmonar value
	 */
	public void setFormaClinicaExtrapulmonar (java.lang.Long formaClinicaExtrapulmonar) {
//        java.lang.Long formaClinicaExtrapulmonarOld = this.formaClinicaExtrapulmonar;
		this.formaClinicaExtrapulmonar = formaClinicaExtrapulmonar;
//        this.getPropertyChangeSupport().firePropertyChange ("formaClinicaExtrapulmonar", formaClinicaExtrapulmonarOld, formaClinicaExtrapulmonar);
	}



	/**
	 * Return the value associated with the column: tipo_entrada
	 */
	public java.lang.Long getTipoEntrada () {
		return getPropertyValue(this, tipoEntrada, PROP_TIPO_ENTRADA); 
	}

	/**
	 * Set the value related to the column: tipo_entrada
	 * @param tipoEntrada the tipo_entrada value
	 */
	public void setTipoEntrada (java.lang.Long tipoEntrada) {
//        java.lang.Long tipoEntradaOld = this.tipoEntrada;
		this.tipoEntrada = tipoEntrada;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoEntrada", tipoEntradaOld, tipoEntrada);
	}



	/**
	 * Return the value associated with the column: esquema_tratamento
	 */
	public java.lang.Long getEsquemaTratamento () {
		return getPropertyValue(this, esquemaTratamento, PROP_ESQUEMA_TRATAMENTO); 
	}

	/**
	 * Set the value related to the column: esquema_tratamento
	 * @param esquemaTratamento the esquema_tratamento value
	 */
	public void setEsquemaTratamento (java.lang.Long esquemaTratamento) {
//        java.lang.Long esquemaTratamentoOld = this.esquemaTratamento;
		this.esquemaTratamento = esquemaTratamento;
//        this.getPropertyChangeSupport().firePropertyChange ("esquemaTratamento", esquemaTratamentoOld, esquemaTratamento);
	}



	/**
	 * Return the value associated with the column: dt_inicio_tratamento
	 */
	public java.util.Date getDataInicioTratamento () {
		return getPropertyValue(this, dataInicioTratamento, PROP_DATA_INICIO_TRATAMENTO); 
	}

	/**
	 * Set the value related to the column: dt_inicio_tratamento
	 * @param dataInicioTratamento the dt_inicio_tratamento value
	 */
	public void setDataInicioTratamento (java.util.Date dataInicioTratamento) {
//        java.util.Date dataInicioTratamentoOld = this.dataInicioTratamento;
		this.dataInicioTratamento = dataInicioTratamento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataInicioTratamento", dataInicioTratamentoOld, dataInicioTratamento);
	}



	/**
	 * Return the value associated with the column: forma_tratamento
	 */
	public java.lang.Long getFormaTratamento () {
		return getPropertyValue(this, formaTratamento, PROP_FORMA_TRATAMENTO); 
	}

	/**
	 * Set the value related to the column: forma_tratamento
	 * @param formaTratamento the forma_tratamento value
	 */
	public void setFormaTratamento (java.lang.Long formaTratamento) {
//        java.lang.Long formaTratamentoOld = this.formaTratamento;
		this.formaTratamento = formaTratamento;
//        this.getPropertyChangeSupport().firePropertyChange ("formaTratamento", formaTratamentoOld, formaTratamento);
	}



	/**
	 * Return the value associated with the column: contactante_registrados
	 */
	public java.lang.Long getContactanteRegistrados () {
		return getPropertyValue(this, contactanteRegistrados, PROP_CONTACTANTE_REGISTRADOS); 
	}

	/**
	 * Set the value related to the column: contactante_registrados
	 * @param contactanteRegistrados the contactante_registrados value
	 */
	public void setContactanteRegistrados (java.lang.Long contactanteRegistrados) {
//        java.lang.Long contactanteRegistradosOld = this.contactanteRegistrados;
		this.contactanteRegistrados = contactanteRegistrados;
//        this.getPropertyChangeSupport().firePropertyChange ("contactanteRegistrados", contactanteRegistradosOld, contactanteRegistrados);
	}



	/**
	 * Return the value associated with the column: contactante_examinados
	 */
	public java.lang.Long getContactanteExaminados () {
		return getPropertyValue(this, contactanteExaminados, PROP_CONTACTANTE_EXAMINADOS); 
	}

	/**
	 * Set the value related to the column: contactante_examinados
	 * @param contactanteExaminados the contactante_examinados value
	 */
	public void setContactanteExaminados (java.lang.Long contactanteExaminados) {
//        java.lang.Long contactanteExaminadosOld = this.contactanteExaminados;
		this.contactanteExaminados = contactanteExaminados;
//        this.getPropertyChangeSupport().firePropertyChange ("contactanteExaminados", contactanteExaminadosOld, contactanteExaminados);
	}



	/**
	 * Return the value associated with the column: observacao
	 */
	public java.lang.String getObservacao () {
		return getPropertyValue(this, observacao, PROP_OBSERVACAO); 
	}

	/**
	 * Set the value related to the column: observacao
	 * @param observacao the observacao value
	 */
	public void setObservacao (java.lang.String observacao) {
//        java.lang.String observacaoOld = this.observacao;
		this.observacao = observacao;
//        this.getPropertyChangeSupport().firePropertyChange ("observacao", observacaoOld, observacao);
	}



	/**
	 * Return the value associated with the column: motivo_encerramento
	 */
	public java.lang.Long getMotivoEncerramento () {
		return getPropertyValue(this, motivoEncerramento, PROP_MOTIVO_ENCERRAMENTO); 
	}

	/**
	 * Set the value related to the column: motivo_encerramento
	 * @param motivoEncerramento the motivo_encerramento value
	 */
	public void setMotivoEncerramento (java.lang.Long motivoEncerramento) {
//        java.lang.Long motivoEncerramentoOld = this.motivoEncerramento;
		this.motivoEncerramento = motivoEncerramento;
//        this.getPropertyChangeSupport().firePropertyChange ("motivoEncerramento", motivoEncerramentoOld, motivoEncerramento);
	}



	/**
	 * Return the value associated with the column: dt_encerramento
	 */
	public java.util.Date getDataEncerramento () {
		return getPropertyValue(this, dataEncerramento, PROP_DATA_ENCERRAMENTO); 
	}

	/**
	 * Set the value related to the column: dt_encerramento
	 * @param dataEncerramento the dt_encerramento value
	 */
	public void setDataEncerramento (java.util.Date dataEncerramento) {
//        java.util.Date dataEncerramentoOld = this.dataEncerramento;
		this.dataEncerramento = dataEncerramento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataEncerramento", dataEncerramentoOld, dataEncerramento);
	}



	/**
	 * Return the value associated with the column: dt_cadastro
	 */
	public java.util.Date getDataCadastro () {
		return getPropertyValue(this, dataCadastro, PROP_DATA_CADASTRO); 
	}

	/**
	 * Set the value related to the column: dt_cadastro
	 * @param dataCadastro the dt_cadastro value
	 */
	public void setDataCadastro (java.util.Date dataCadastro) {
//        java.util.Date dataCadastroOld = this.dataCadastro;
		this.dataCadastro = dataCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCadastro", dataCadastroOld, dataCadastro);
	}



	/**
	 * Return the value associated with the column: dt_usuario
	 */
	public java.util.Date getDataUsuario () {
		return getPropertyValue(this, dataUsuario, PROP_DATA_USUARIO); 
	}

	/**
	 * Set the value related to the column: dt_usuario
	 * @param dataUsuario the dt_usuario value
	 */
	public void setDataUsuario (java.util.Date dataUsuario) {
//        java.util.Date dataUsuarioOld = this.dataUsuario;
		this.dataUsuario = dataUsuario;
//        this.getPropertyChangeSupport().firePropertyChange ("dataUsuario", dataUsuarioOld, dataUsuario);
	}



	/**
	 * Return the value associated with the column: terapia_antirretrovial_durante_tratamento
	 */
	public java.lang.Long getTerapiaAntirretrovialDuranteTratamento () {
		return getPropertyValue(this, terapiaAntirretrovialDuranteTratamento, PROP_TERAPIA_ANTIRRETROVIAL_DURANTE_TRATAMENTO); 
	}

	/**
	 * Set the value related to the column: terapia_antirretrovial_durante_tratamento
	 * @param terapiaAntirretrovialDuranteTratamento the terapia_antirretrovial_durante_tratamento value
	 */
	public void setTerapiaAntirretrovialDuranteTratamento (java.lang.Long terapiaAntirretrovialDuranteTratamento) {
//        java.lang.Long terapiaAntirretrovialDuranteTratamentoOld = this.terapiaAntirretrovialDuranteTratamento;
		this.terapiaAntirretrovialDuranteTratamento = terapiaAntirretrovialDuranteTratamento;
//        this.getPropertyChangeSupport().firePropertyChange ("terapiaAntirretrovialDuranteTratamento", terapiaAntirretrovialDuranteTratamentoOld, terapiaAntirretrovialDuranteTratamento);
	}



	/**
	 * Return the value associated with the column: populacao_privada_liberdade
	 */
	public java.lang.Long getPopulacaoPrivadaLiberdade () {
		return getPropertyValue(this, populacaoPrivadaLiberdade, PROP_POPULACAO_PRIVADA_LIBERDADE); 
	}

	/**
	 * Set the value related to the column: populacao_privada_liberdade
	 * @param populacaoPrivadaLiberdade the populacao_privada_liberdade value
	 */
	public void setPopulacaoPrivadaLiberdade (java.lang.Long populacaoPrivadaLiberdade) {
//        java.lang.Long populacaoPrivadaLiberdadeOld = this.populacaoPrivadaLiberdade;
		this.populacaoPrivadaLiberdade = populacaoPrivadaLiberdade;
//        this.getPropertyChangeSupport().firePropertyChange ("populacaoPrivadaLiberdade", populacaoPrivadaLiberdadeOld, populacaoPrivadaLiberdade);
	}



	/**
	 * Return the value associated with the column: populacao_situacao_rua
	 */
	public java.lang.Long getPopulacaoSituacaoRua () {
		return getPropertyValue(this, populacaoSituacaoRua, PROP_POPULACAO_SITUACAO_RUA); 
	}

	/**
	 * Set the value related to the column: populacao_situacao_rua
	 * @param populacaoSituacaoRua the populacao_situacao_rua value
	 */
	public void setPopulacaoSituacaoRua (java.lang.Long populacaoSituacaoRua) {
//        java.lang.Long populacaoSituacaoRuaOld = this.populacaoSituacaoRua;
		this.populacaoSituacaoRua = populacaoSituacaoRua;
//        this.getPropertyChangeSupport().firePropertyChange ("populacaoSituacaoRua", populacaoSituacaoRuaOld, populacaoSituacaoRua);
	}



	/**
	 * Return the value associated with the column: populacao_profissionais_saude
	 */
	public java.lang.Long getPopulacaoProfissionaisSaude () {
		return getPropertyValue(this, populacaoProfissionaisSaude, PROP_POPULACAO_PROFISSIONAIS_SAUDE); 
	}

	/**
	 * Set the value related to the column: populacao_profissionais_saude
	 * @param populacaoProfissionaisSaude the populacao_profissionais_saude value
	 */
	public void setPopulacaoProfissionaisSaude (java.lang.Long populacaoProfissionaisSaude) {
//        java.lang.Long populacaoProfissionaisSaudeOld = this.populacaoProfissionaisSaude;
		this.populacaoProfissionaisSaude = populacaoProfissionaisSaude;
//        this.getPropertyChangeSupport().firePropertyChange ("populacaoProfissionaisSaude", populacaoProfissionaisSaudeOld, populacaoProfissionaisSaude);
	}



	/**
	 * Return the value associated with the column: populacao_imigrante
	 */
	public java.lang.Long getPopulacaoImigrante () {
		return getPropertyValue(this, populacaoImigrante, PROP_POPULACAO_IMIGRANTE); 
	}

	/**
	 * Set the value related to the column: populacao_imigrante
	 * @param populacaoImigrante the populacao_imigrante value
	 */
	public void setPopulacaoImigrante (java.lang.Long populacaoImigrante) {
//        java.lang.Long populacaoImigranteOld = this.populacaoImigrante;
		this.populacaoImigrante = populacaoImigrante;
//        this.getPropertyChangeSupport().firePropertyChange ("populacaoImigrante", populacaoImigranteOld, populacaoImigrante);
	}



	/**
	 * Return the value associated with the column: doencas_agravos_aids
	 */
	public java.lang.Long getDoencasAgravosAids () {
		return getPropertyValue(this, doencasAgravosAids, PROP_DOENCAS_AGRAVOS_AIDS); 
	}

	/**
	 * Set the value related to the column: doencas_agravos_aids
	 * @param doencasAgravosAids the doencas_agravos_aids value
	 */
	public void setDoencasAgravosAids (java.lang.Long doencasAgravosAids) {
//        java.lang.Long doencasAgravosAidsOld = this.doencasAgravosAids;
		this.doencasAgravosAids = doencasAgravosAids;
//        this.getPropertyChangeSupport().firePropertyChange ("doencasAgravosAids", doencasAgravosAidsOld, doencasAgravosAids);
	}



	/**
	 * Return the value associated with the column: doencas_agravos_uso_drogas_ilicitas
	 */
	public java.lang.Long getDoencasAgravosUsoDrogasIlicitas () {
		return getPropertyValue(this, doencasAgravosUsoDrogasIlicitas, PROP_DOENCAS_AGRAVOS_USO_DROGAS_ILICITAS); 
	}

	/**
	 * Set the value related to the column: doencas_agravos_uso_drogas_ilicitas
	 * @param doencasAgravosUsoDrogasIlicitas the doencas_agravos_uso_drogas_ilicitas value
	 */
	public void setDoencasAgravosUsoDrogasIlicitas (java.lang.Long doencasAgravosUsoDrogasIlicitas) {
//        java.lang.Long doencasAgravosUsoDrogasIlicitasOld = this.doencasAgravosUsoDrogasIlicitas;
		this.doencasAgravosUsoDrogasIlicitas = doencasAgravosUsoDrogasIlicitas;
//        this.getPropertyChangeSupport().firePropertyChange ("doencasAgravosUsoDrogasIlicitas", doencasAgravosUsoDrogasIlicitasOld, doencasAgravosUsoDrogasIlicitas);
	}



	/**
	 * Return the value associated with the column: doencas_agravos_alcoolismo
	 */
	public java.lang.Long getDoencasAgravosAlcoolismo () {
		return getPropertyValue(this, doencasAgravosAlcoolismo, PROP_DOENCAS_AGRAVOS_ALCOOLISMO); 
	}

	/**
	 * Set the value related to the column: doencas_agravos_alcoolismo
	 * @param doencasAgravosAlcoolismo the doencas_agravos_alcoolismo value
	 */
	public void setDoencasAgravosAlcoolismo (java.lang.Long doencasAgravosAlcoolismo) {
//        java.lang.Long doencasAgravosAlcoolismoOld = this.doencasAgravosAlcoolismo;
		this.doencasAgravosAlcoolismo = doencasAgravosAlcoolismo;
//        this.getPropertyChangeSupport().firePropertyChange ("doencasAgravosAlcoolismo", doencasAgravosAlcoolismoOld, doencasAgravosAlcoolismo);
	}



	/**
	 * Return the value associated with the column: doencas_agravos_diabetes
	 */
	public java.lang.Long getDoencasAgravosDiabetes () {
		return getPropertyValue(this, doencasAgravosDiabetes, PROP_DOENCAS_AGRAVOS_DIABETES); 
	}

	/**
	 * Set the value related to the column: doencas_agravos_diabetes
	 * @param doencasAgravosDiabetes the doencas_agravos_diabetes value
	 */
	public void setDoencasAgravosDiabetes (java.lang.Long doencasAgravosDiabetes) {
//        java.lang.Long doencasAgravosDiabetesOld = this.doencasAgravosDiabetes;
		this.doencasAgravosDiabetes = doencasAgravosDiabetes;
//        this.getPropertyChangeSupport().firePropertyChange ("doencasAgravosDiabetes", doencasAgravosDiabetesOld, doencasAgravosDiabetes);
	}



	/**
	 * Return the value associated with the column: doencas_agravos_doenca_mental
	 */
	public java.lang.Long getDoencasAgravosDoencaMental () {
		return getPropertyValue(this, doencasAgravosDoencaMental, PROP_DOENCAS_AGRAVOS_DOENCA_MENTAL); 
	}

	/**
	 * Set the value related to the column: doencas_agravos_doenca_mental
	 * @param doencasAgravosDoencaMental the doencas_agravos_doenca_mental value
	 */
	public void setDoencasAgravosDoencaMental (java.lang.Long doencasAgravosDoencaMental) {
//        java.lang.Long doencasAgravosDoencaMentalOld = this.doencasAgravosDoencaMental;
		this.doencasAgravosDoencaMental = doencasAgravosDoencaMental;
//        this.getPropertyChangeSupport().firePropertyChange ("doencasAgravosDoencaMental", doencasAgravosDoencaMentalOld, doencasAgravosDoencaMental);
	}



	/**
	 * Return the value associated with the column: doencas_agravos_tabagismo
	 */
	public java.lang.Long getDoencasAgravosTabagismo () {
		return getPropertyValue(this, doencasAgravosTabagismo, PROP_DOENCAS_AGRAVOS_TABAGISMO); 
	}

	/**
	 * Set the value related to the column: doencas_agravos_tabagismo
	 * @param doencasAgravosTabagismo the doencas_agravos_tabagismo value
	 */
	public void setDoencasAgravosTabagismo (java.lang.Long doencasAgravosTabagismo) {
//        java.lang.Long doencasAgravosTabagismoOld = this.doencasAgravosTabagismo;
		this.doencasAgravosTabagismo = doencasAgravosTabagismo;
//        this.getPropertyChangeSupport().firePropertyChange ("doencasAgravosTabagismo", doencasAgravosTabagismoOld, doencasAgravosTabagismo);
	}



	/**
	 * Return the value associated with the column: doencas_agravos_outro
	 */
	public java.lang.Long getDoencasAgravosOutro () {
		return getPropertyValue(this, doencasAgravosOutro, PROP_DOENCAS_AGRAVOS_OUTRO); 
	}

	/**
	 * Set the value related to the column: doencas_agravos_outro
	 * @param doencasAgravosOutro the doencas_agravos_outro value
	 */
	public void setDoencasAgravosOutro (java.lang.Long doencasAgravosOutro) {
//        java.lang.Long doencasAgravosOutroOld = this.doencasAgravosOutro;
		this.doencasAgravosOutro = doencasAgravosOutro;
//        this.getPropertyChangeSupport().firePropertyChange ("doencasAgravosOutro", doencasAgravosOutroOld, doencasAgravosOutro);
	}



	/**
	 * Return the value associated with the column: doencas_agravos_desc_outros
	 */
	public java.lang.String getDoencasAgravosDescricaoOutros () {
		return getPropertyValue(this, doencasAgravosDescricaoOutros, PROP_DOENCAS_AGRAVOS_DESCRICAO_OUTROS); 
	}

	/**
	 * Set the value related to the column: doencas_agravos_desc_outros
	 * @param doencasAgravosDescricaoOutros the doencas_agravos_desc_outros value
	 */
	public void setDoencasAgravosDescricaoOutros (java.lang.String doencasAgravosDescricaoOutros) {
//        java.lang.String doencasAgravosDescricaoOutrosOld = this.doencasAgravosDescricaoOutros;
		this.doencasAgravosDescricaoOutros = doencasAgravosDescricaoOutros;
//        this.getPropertyChangeSupport().firePropertyChange ("doencasAgravosDescricaoOutros", doencasAgravosDescricaoOutrosOld, doencasAgravosDescricaoOutros);
	}



	/**
	 * Return the value associated with the column: cd_tuberc_sintomatico
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.tuberculose.TuberculoseSintomatico getTuberculoseSintomatico () {
		return getPropertyValue(this, tuberculoseSintomatico, PROP_TUBERCULOSE_SINTOMATICO); 
	}

	/**
	 * Set the value related to the column: cd_tuberc_sintomatico
	 * @param tuberculoseSintomatico the cd_tuberc_sintomatico value
	 */
	public void setTuberculoseSintomatico (br.com.ksisolucoes.vo.prontuario.basico.tuberculose.TuberculoseSintomatico tuberculoseSintomatico) {
//        br.com.ksisolucoes.vo.prontuario.basico.tuberculose.TuberculoseSintomatico tuberculoseSintomaticoOld = this.tuberculoseSintomatico;
		this.tuberculoseSintomatico = tuberculoseSintomatico;
//        this.getPropertyChangeSupport().firePropertyChange ("tuberculoseSintomatico", tuberculoseSintomaticoOld, tuberculoseSintomatico);
	}



	/**
	 * Return the value associated with the column: nr_atendimento
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.Atendimento getAtendimento () {
		return getPropertyValue(this, atendimento, PROP_ATENDIMENTO); 
	}

	/**
	 * Set the value related to the column: nr_atendimento
	 * @param atendimento the nr_atendimento value
	 */
	public void setAtendimento (br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimento) {
//        br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimentoOld = this.atendimento;
		this.atendimento = atendimento;
//        this.getPropertyChangeSupport().firePropertyChange ("atendimento", atendimentoOld, atendimento);
	}



	/**
	 * Return the value associated with the column: cd_usuario_cad
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioCadastro () {
		return getPropertyValue(this, usuarioCadastro, PROP_USUARIO_CADASTRO); 
	}

	/**
	 * Set the value related to the column: cd_usuario_cad
	 * @param usuarioCadastro the cd_usuario_cad value
	 */
	public void setUsuarioCadastro (br.com.ksisolucoes.vo.controle.Usuario usuarioCadastro) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioCadastroOld = this.usuarioCadastro;
		this.usuarioCadastro = usuarioCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCadastro", usuarioCadastroOld, usuarioCadastro);
	}



	/**
	 * Return the value associated with the column: cd_usuario_upd
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioUpdate () {
		return getPropertyValue(this, usuarioUpdate, PROP_USUARIO_UPDATE); 
	}

	/**
	 * Set the value related to the column: cd_usuario_upd
	 * @param usuarioUpdate the cd_usuario_upd value
	 */
	public void setUsuarioUpdate (br.com.ksisolucoes.vo.controle.Usuario usuarioUpdate) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioUpdateOld = this.usuarioUpdate;
		this.usuarioUpdate = usuarioUpdate;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioUpdate", usuarioUpdateOld, usuarioUpdate);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.basico.tuberculose.TuberculoseAcompanhamento)) return false;
		else {
			br.com.ksisolucoes.vo.prontuario.basico.tuberculose.TuberculoseAcompanhamento tuberculoseAcompanhamento = (br.com.ksisolucoes.vo.prontuario.basico.tuberculose.TuberculoseAcompanhamento) obj;
			if (null == this.getCodigo() || null == tuberculoseAcompanhamento.getCodigo()) return false;
			else return (this.getCodigo().equals(tuberculoseAcompanhamento.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}