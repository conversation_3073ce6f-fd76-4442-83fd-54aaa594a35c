package br.com.ksisolucoes.vo.entradas.estoque.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;


public abstract class BaseSubGrupoPK extends BaseRootVO implements Serializable {

	protected int hashCode = Integer.MIN_VALUE;

	public static String PROP_CODIGO = "codigo";
	public static String PROP_CODIGO_GRUPO_PRODUTO = "codigoGrupoProduto";

	private java.lang.Long codigo;
	private java.lang.Long codigoGrupoProduto;


	public BaseSubGrupoPK () {}
	
	public BaseSubGrupoPK (
		java.lang.Long codigo,
		java.lang.Long codigoGrupoProduto) {

		this.setCodigo(codigo);
		this.setCodigoGrupoProduto(codigoGrupoProduto);
	}


	/**
	 * Return the value associated with the column: cod_sub
	 */
	public java.lang.Long getCodigo () {
		return getPropertyValue(this, codigo, PROP_CODIGO); 
	}

	/**
	 * Set the value related to the column: cod_sub
	 * @param codigo the cod_sub value
	 */
	public void setCodigo (java.lang.Long codigo) {
//        java.lang.Long codigoOld = this.codigo;
		this.codigo = codigo;
//        this.getPropertyChangeSupport().firePropertyChange ("codigo", codigoOld, codigo);
	}



	/**
	 * Return the value associated with the column: cod_gru
	 */
	public java.lang.Long getCodigoGrupoProduto () {
		return getPropertyValue(this, codigoGrupoProduto, PROP_CODIGO_GRUPO_PRODUTO); 
	}

	/**
	 * Set the value related to the column: cod_gru
	 * @param codigoGrupoProduto the cod_gru value
	 */
	public void setCodigoGrupoProduto (java.lang.Long codigoGrupoProduto) {
//        java.lang.Long codigoGrupoProdutoOld = this.codigoGrupoProduto;
		this.codigoGrupoProduto = codigoGrupoProduto;
//        this.getPropertyChangeSupport().firePropertyChange ("codigoGrupoProduto", codigoGrupoProdutoOld, codigoGrupoProduto);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.entradas.estoque.SubGrupoPK)) return false;
		else {
			br.com.ksisolucoes.vo.entradas.estoque.SubGrupoPK mObj = (br.com.ksisolucoes.vo.entradas.estoque.SubGrupoPK) obj;
			if (null != this.getCodigo() && null != mObj.getCodigo()) {
				if (!this.getCodigo().equals(mObj.getCodigo())) {
					return false;
				}
			}
			else {
				return false;
			}
			if (null != this.getCodigoGrupoProduto() && null != mObj.getCodigoGrupoProduto()) {
				if (!this.getCodigoGrupoProduto().equals(mObj.getCodigoGrupoProduto())) {
					return false;
				}
			}
			else {
				return false;
			}
			return true;
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			StringBuilder sb = new StringBuilder();
			if (null != this.getCodigo()) {
				sb.append(this.getCodigo().hashCode());
				sb.append(":");
			}
			else {
				return super.hashCode();
			}
			if (null != this.getCodigoGrupoProduto()) {
				sb.append(this.getCodigoGrupoProduto().hashCode());
				sb.append(":");
			}
			else {
				return super.hashCode();
			}
			this.hashCode = sb.toString().hashCode();
		}
		return this.hashCode;
	}

    private java.beans.PropertyChangeSupport propertyChangeSupport;

    protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
        if( this.propertyChangeSupport == null ) {
            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
        }
        return this.propertyChangeSupport;
    }

    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
        propertyChangeSupport.addPropertyChangeListener(l);
    }

    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
		propertyChangeSupport.addPropertyChangeListener(propertyName, listener);
    }

    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
        propertyChangeSupport.removePropertyChangeListener(l);
    }
}