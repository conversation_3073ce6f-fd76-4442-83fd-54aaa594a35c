package br.com.ksisolucoes.vo.formulario.base;

import java.io.Serializable;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;


/**
 * This is an object that contains data related to the formulario_item table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="formulario_item"
 */

public abstract class BaseFormularioItem extends BaseRootVO implements Serializable {

	public static String REF = "FormularioItem";
	public static final String PROP_LABEL_SUFIX = "labelSufix";
	public static final String PROP_OBRIGATORIO = "obrigatorio";
	public static final String PROP_TIPO_CAMPO = "tipoCampo";
	public static final String PROP_SCRIPT = "script";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_TAMANHO = "tamanho";
	public static final String PROP_FORMULARIO = "formulario";
	public static final String PROP_LABEL_PREFIX = "labelPrefix";
	public static final String PROP_PESQUISA = "pesquisa";
	public static final String PROP_VALOR_DEFAULT = "valorDefault";
	public static final String PROP_SEQUENCIA = "sequencia";
	public static final String PROP_CHAVE = "chave";
	public static final String PROP_HABILITADO = "habilitado";
	public static final String PROP_LABEL = "label";
	public static final String PROP_VISIVEL = "visivel";


	// constructors
	public BaseFormularioItem () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseFormularioItem (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseFormularioItem (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.formulario.Formulario formulario) {

		this.setCodigo(codigo);
		this.setFormulario(formulario);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Long sequencia;
	private java.lang.String chave;
	private java.lang.String label;
	private java.lang.String labelPrefix;
	private java.lang.String labelSufix;
	private java.lang.String tipoCampo;
	private java.lang.Double tamanho;
	private java.lang.String obrigatorio;
	private java.lang.String pesquisa;
	private java.lang.String habilitado;
	private java.lang.String visivel;
	private java.lang.String valorDefault;
	private java.lang.String script;

	// many to one
	private br.com.ksisolucoes.vo.formulario.Formulario formulario;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_formulario_item"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: sequencia
	 */
	public java.lang.Long getSequencia () {
		return getPropertyValue(this, sequencia, PROP_SEQUENCIA); 
	}

	/**
	 * Set the value related to the column: sequencia
	 * @param sequencia the sequencia value
	 */
	public void setSequencia (java.lang.Long sequencia) {
//        java.lang.Long sequenciaOld = this.sequencia;
		this.sequencia = sequencia;
//        this.getPropertyChangeSupport().firePropertyChange ("sequencia", sequenciaOld, sequencia);
	}



	/**
	 * Return the value associated with the column: chave
	 */
	public java.lang.String getChave () {
		return getPropertyValue(this, chave, PROP_CHAVE); 
	}

	/**
	 * Set the value related to the column: chave
	 * @param chave the chave value
	 */
	public void setChave (java.lang.String chave) {
//        java.lang.String chaveOld = this.chave;
		this.chave = chave;
//        this.getPropertyChangeSupport().firePropertyChange ("chave", chaveOld, chave);
	}



	/**
	 * Return the value associated with the column: label
	 */
	public java.lang.String getLabel () {
		return getPropertyValue(this, label, PROP_LABEL); 
	}

	/**
	 * Set the value related to the column: label
	 * @param label the label value
	 */
	public void setLabel (java.lang.String label) {
//        java.lang.String labelOld = this.label;
		this.label = label;
//        this.getPropertyChangeSupport().firePropertyChange ("label", labelOld, label);
	}



	/**
	 * Return the value associated with the column: label_prefix
	 */
	public java.lang.String getLabelPrefix () {
		return getPropertyValue(this, labelPrefix, PROP_LABEL_PREFIX); 
	}

	/**
	 * Set the value related to the column: label_prefix
	 * @param labelPrefix the label_prefix value
	 */
	public void setLabelPrefix (java.lang.String labelPrefix) {
//        java.lang.String labelPrefixOld = this.labelPrefix;
		this.labelPrefix = labelPrefix;
//        this.getPropertyChangeSupport().firePropertyChange ("labelPrefix", labelPrefixOld, labelPrefix);
	}



	/**
	 * Return the value associated with the column: label_sufix
	 */
	public java.lang.String getLabelSufix () {
		return getPropertyValue(this, labelSufix, PROP_LABEL_SUFIX); 
	}

	/**
	 * Set the value related to the column: label_sufix
	 * @param labelSufix the label_sufix value
	 */
	public void setLabelSufix (java.lang.String labelSufix) {
//        java.lang.String labelSufixOld = this.labelSufix;
		this.labelSufix = labelSufix;
//        this.getPropertyChangeSupport().firePropertyChange ("labelSufix", labelSufixOld, labelSufix);
	}



	/**
	 * Return the value associated with the column: tp_campo
	 */
	public java.lang.String getTipoCampo () {
		return getPropertyValue(this, tipoCampo, PROP_TIPO_CAMPO); 
	}

	/**
	 * Set the value related to the column: tp_campo
	 * @param tipoCampo the tp_campo value
	 */
	public void setTipoCampo (java.lang.String tipoCampo) {
//        java.lang.String tipoCampoOld = this.tipoCampo;
		this.tipoCampo = tipoCampo;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoCampo", tipoCampoOld, tipoCampo);
	}



	/**
	 * Return the value associated with the column: tamanho
	 */
	public java.lang.Double getTamanho () {
		return getPropertyValue(this, tamanho, PROP_TAMANHO); 
	}

	/**
	 * Set the value related to the column: tamanho
	 * @param tamanho the tamanho value
	 */
	public void setTamanho (java.lang.Double tamanho) {
//        java.lang.Double tamanhoOld = this.tamanho;
		this.tamanho = tamanho;
//        this.getPropertyChangeSupport().firePropertyChange ("tamanho", tamanhoOld, tamanho);
	}



	/**
	 * Return the value associated with the column: obrigatorio
	 */
	public java.lang.String getObrigatorio () {
		return getPropertyValue(this, obrigatorio, PROP_OBRIGATORIO); 
	}

	/**
	 * Set the value related to the column: obrigatorio
	 * @param obrigatorio the obrigatorio value
	 */
	public void setObrigatorio (java.lang.String obrigatorio) {
//        java.lang.String obrigatorioOld = this.obrigatorio;
		this.obrigatorio = obrigatorio;
//        this.getPropertyChangeSupport().firePropertyChange ("obrigatorio", obrigatorioOld, obrigatorio);
	}



	/**
	 * Return the value associated with the column: pesquisa
	 */
	public java.lang.String getPesquisa () {
		return getPropertyValue(this, pesquisa, PROP_PESQUISA); 
	}

	/**
	 * Set the value related to the column: pesquisa
	 * @param pesquisa the pesquisa value
	 */
	public void setPesquisa (java.lang.String pesquisa) {
//        java.lang.String pesquisaOld = this.pesquisa;
		this.pesquisa = pesquisa;
//        this.getPropertyChangeSupport().firePropertyChange ("pesquisa", pesquisaOld, pesquisa);
	}



	/**
	 * Return the value associated with the column: habilitado
	 */
	public java.lang.String getHabilitado () {
		return getPropertyValue(this, habilitado, PROP_HABILITADO); 
	}

	/**
	 * Set the value related to the column: habilitado
	 * @param habilitado the habilitado value
	 */
	public void setHabilitado (java.lang.String habilitado) {
//        java.lang.String habilitadoOld = this.habilitado;
		this.habilitado = habilitado;
//        this.getPropertyChangeSupport().firePropertyChange ("habilitado", habilitadoOld, habilitado);
	}



	/**
	 * Return the value associated with the column: visivel
	 */
	public java.lang.String getVisivel () {
		return getPropertyValue(this, visivel, PROP_VISIVEL); 
	}

	/**
	 * Set the value related to the column: visivel
	 * @param visivel the visivel value
	 */
	public void setVisivel (java.lang.String visivel) {
//        java.lang.String visivelOld = this.visivel;
		this.visivel = visivel;
//        this.getPropertyChangeSupport().firePropertyChange ("visivel", visivelOld, visivel);
	}



	/**
	 * Return the value associated with the column: valor_default
	 */
	public java.lang.String getValorDefault () {
		return getPropertyValue(this, valorDefault, PROP_VALOR_DEFAULT); 
	}

	/**
	 * Set the value related to the column: valor_default
	 * @param valorDefault the valor_default value
	 */
	public void setValorDefault (java.lang.String valorDefault) {
//        java.lang.String valorDefaultOld = this.valorDefault;
		this.valorDefault = valorDefault;
//        this.getPropertyChangeSupport().firePropertyChange ("valorDefault", valorDefaultOld, valorDefault);
	}



	/**
	 * Return the value associated with the column: script
	 */
	public java.lang.String getScript () {
		return getPropertyValue(this, script, PROP_SCRIPT); 
	}

	/**
	 * Set the value related to the column: script
	 * @param script the script value
	 */
	public void setScript (java.lang.String script) {
//        java.lang.String scriptOld = this.script;
		this.script = script;
//        this.getPropertyChangeSupport().firePropertyChange ("script", scriptOld, script);
	}



	/**
	 * Return the value associated with the column: cd_formulario
	 */
	public br.com.ksisolucoes.vo.formulario.Formulario getFormulario () {
		return getPropertyValue(this, formulario, PROP_FORMULARIO); 
	}

	/**
	 * Set the value related to the column: cd_formulario
	 * @param formulario the cd_formulario value
	 */
	public void setFormulario (br.com.ksisolucoes.vo.formulario.Formulario formulario) {
//        br.com.ksisolucoes.vo.formulario.Formulario formularioOld = this.formulario;
		this.formulario = formulario;
//        this.getPropertyChangeSupport().firePropertyChange ("formulario", formularioOld, formulario);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.formulario.FormularioItem)) return false;
		else {
			br.com.ksisolucoes.vo.formulario.FormularioItem formularioItem = (br.com.ksisolucoes.vo.formulario.FormularioItem) obj;
			if (null == this.getCodigo() || null == formularioItem.getCodigo()) return false;
			else return (this.getCodigo().equals(formularioItem.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }

    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}