<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >
<hibernate-mapping package="br.com.ksisolucoes.vo.vigilancia.investigacao">

    <class name="InvestigacaoAgravoCriancaExpostaHiv" table="investigacao_agr_crianca_exposta_hiv">

        <id name="codigo"
            type="java.lang.Long"
            column="cd_invest_agr_crianca_hiv" >
            <generator class="sequence">
                <param name="sequence">seq_investigacao_agr_crianca_exposta_hiv</param>
            </generator>
        </id>

        <version column="version" name="version" type="long"/>

        <property
            name="flagInformacoesComplementares"
            column="flag_informacoes_complementares"
            type="java.lang.String"
            not-null="true"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo"
                name="registroAgravo"
                not-null="true">
            <column name="cd_registro_agravo"/>
        </many-to-one>

        <property
                name="dataInvestigacao"
                column="dt_investigacao"
                type="java.util.Date"
        />

        <!-- CASO Autoctone -->
        <property
                name="casoAutoctone"
                column="caso_autoctone"
                type="java.lang.Long"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.basico.Cidade"
                name="cidadeLocalInfeccao" not-null="false">
            <column name="cd_cidade_infeccao"/>
        </many-to-one>

        <many-to-one
                class="br.com.ksisolucoes.vo.basico.Pais"
                name="paisLocalInfeccao" not-null="false">
            <column name="cd_pais_infeccao"/>
        </many-to-one>

        <property
                name="distritoLocalInfeccao"
                column="str_distrito_infeccao"
                type="java.lang.String"
        />

        <property
                name="bairroLocalInfeccao"
                column="str_bairro_infeccao"
                type="java.lang.String"
        />

        <!-- OBS -->
        <property
                name="observacao"
                column="observacao"
                type="java.lang.String"
        />

        <!-- Encerramento -->
        <property
                name="dataEncerramento"
                column="dt_encerramento"
                type="java.util.Date"
        />
        <many-to-one
                class="br.com.ksisolucoes.vo.controle.Usuario"
                name="usuarioEncerramento" not-null="false">
            <column name="cd_usuario_encerramento"/>
        </many-to-one>



        <!-- Dados epidemiologicos mae nutriz -->

        <property
                name="maeNutrizIdade"
                column="mae_nutriz_idade"
                type="java.lang.Long"
        />

        <property
                name="maeNutrizEscolaridade"
                column="mae_nutriz_escolaridade"
                type="java.lang.Long"
        />

        <property
                name="maeNutrizRacaCor"
                column="mae_nutriz_raca_cor"
                type="java.lang.Long"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo"
                name="maeNutrizOcupacaoCbo" not-null="false">
            <column name="mae_nutriz_ocupacao_cbo"/>
        </many-to-one>

        <property
                name="maeNutrizAntirretroviralGestacao"
                column="mae_nutriz_antirretroviral_gestacao"
                type="java.lang.Long"
        />
        <property
                name="maeNutrizAntirretroviralParto"
                column="mae_nutriz_antirretroviral_parto"
                type="java.lang.Long"
        />


        <!-- Investigacao Crianca Exposta HIV -->
        <property
                name="numeroDeclaracaoNascidoVivo"
                column="nro_declaracao_nascido_vivo"
                type="java.lang.String"
        />
        <property
                name="tipoParto"
                column="tipo_parto"
                type="java.lang.Long"
        />
        <many-to-one
                class="br.com.ksisolucoes.vo.basico.Empresa"
                name="unidadeHospitalLocalNascimento" not-null="false">
            <column name="unidade_hospital"/>
        </many-to-one>

        <property
                name="aleitamentoMaterno"
                column="aleitamento_materno"
                type="java.lang.Long"
        />
        <property
                name="aleitamentoCruzado"
                column="aleitamento_cruzado"
                type="java.lang.Long"
        />
        <property
                name="profilaxiaAntirretroviralOral"
                column="profilaxia_antirretroviral_oral"
                type="java.lang.Long"
        />
        <property
                name="tempoUsoProfilaxiaOral"
                column="tempo_uso_profilaxia_oral"
                type="java.lang.Long"
        />

        <!-- Dados Lab Crianca -->
        <property
                name="testeDeteccaoAcidoNucleico1"
                column="teste_deteccao_acido_nucleico_1"
                type="java.lang.Long"
        />
        <property
                name="dataTesteAcidoNucleico1"
                column="dt_teste_acido_nucleico_1"
                type="java.util.Date"
        />

        <property
                name="testeDeteccaoAcidoNucleico2"
                column="teste_deteccao_acido_nucleico_2"
                type="java.lang.Long"
        />
        <property
                name="dataTesteAcidoNucleico2"
                column="dt_teste_acido_nucleico_2"
                type="java.util.Date"
        />

        <property
                name="testeDeteccaoAcidoNucleico3"
                column="teste_deteccao_acido_nucleico_3"
                type="java.lang.Long"
        />
        <property
                name="dataTesteAcidoNucleico3"
                column="dt_teste_acido_nucleico_3"
                type="java.util.Date"
        />

        <property
                name="testeTriagemAntiHiv"
                column="teste_triagem_anti_hiv"
                type="java.lang.Long"
        />
        <property
                name="dataTesteTriagemAntiHiv"
                column="dt_teste_triagem_anti_hiv"
                type="java.util.Date"
        />

        <property
                name="testeConfirmatorioAntiHiv"
                column="teste_confirmatorio_anti_hiv"
                type="java.lang.Long"
        />
        <property
                name="dataTesteConfirmatorioAntiHiv"
                column="dt_teste_confirmatorio_anti_hiv"
                type="java.util.Date"
        />


        <property
                name="testeRapido1"
                column="teste_rapido_1"
                type="java.lang.Long"
        />
        <property
                name="testeRapido2"
                column="teste_rapido_2"
                type="java.lang.Long"
        />
        <property
                name="testeRapido3"
                column="teste_rapido_3"
                type="java.lang.Long"
        />
        <property
                name="dataTestesRapidos"
                column="dt_testes_rapidos"
                type="java.util.Date"
        />

        <!-- Evolucao caso -->
        <property
                name="evolucaoCaso"
                column="evolucao_caso"
                type="java.lang.Long"
        />
    </class>
</hibernate-mapping>