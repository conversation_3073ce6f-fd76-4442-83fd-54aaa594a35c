<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.integracao.mobile"  >
	<class 
		name="IntegracaoMobile"
		table="integracao_mobile"
	>
		<id
			name="codigo"
			type="java.lang.Long"
			column="cd_integracao"
		>
			<generator class="assigned"/>
		</id> <version column="version" name="version" type="long" />

		<many-to-one
				class="br.com.ksisolucoes.vo.cadsus.Profissional"
				name="profissional"
				column="cd_profissional"
				not-null="true"
		/>

		<many-to-one
				class="br.com.ksisolucoes.vo.controle.Usuario"
				name="usuario"
				column="cd_usuario"
		/>

		<many-to-one
				class="br.com.ksisolucoes.vo.basico.Empresa"
				name="empresa"
				column="empresa"
		/>

		<property
			name="tipoIntegracao"
			column="tp_integracao"
			type="java.lang.Long"
		/>

		<property
			name="dataIntegracao"
			column="dt_integracao"
			type="java.util.Date"
		/>

		<property
				name="dataProcessamento"
				column="dt_processamento"
				type="java.util.Date"
		/>

		<property
				name="dataResposta"
				column="dt_resposta"
				type="java.util.Date"
		/>

		<property
				name="status"
				column="status"
				type="java.lang.Long"
		/>

		<property
				name="mensagemRetorno"
				column="mensagem_retorno"
				type="java.lang.String"
		/>

		<property
				name="dataCancelamento"
				column="dt_cancelamento"
				type="java.util.Date"
		/>

		<property
				name="versaoMobile"
				column="versao_mobile"
				type="java.lang.String"
		/>

	</class>
</hibernate-mapping>