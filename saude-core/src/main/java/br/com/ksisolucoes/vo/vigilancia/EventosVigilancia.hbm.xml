<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.vigilancia"  >
    <class name="EventosVigilancia" table="eventos_vigilancia">
        <id
            column="cd_eventos_vigilancia"
            name="codigo"
            type="java.lang.Long"
        >
            <generator class="assigned" />
        </id> 
        <version column="version" name="version" type="long" />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.vigilancia.Estabelecimento"
            column="cd_estabelecimento"
            name="estabelecimento"
            not-null="true"
        />

        <property
            column="nome"
            name="nome"
            not-null="true"
            type="java.lang.String"
            length="80"
        />

        <property
            column="ds_tipo"
            name="descricaoTipo"
            not-null="true"
            type="java.lang.String"
            length="80"
        />

        <property
            column="local"
            name="local"
            not-null="true"
            type="java.lang.String"
            length="80"
        />
        
        <property
            column="publico_dia"
            name="publicoDia"
            not-null="false"
            type="java.lang.Long"
        />
        
        <property
            column="publico_alvo"
            name="publicoAlvo"
            not-null="false"
            type="java.lang.String"
            length="80"
        />
        
        <property
            column="qtd_servico_alimentacao"
            name="quantidadeServicoAlimentacao"
            not-null="false"
            type="java.lang.Long"
        />
        
        <property
            column="abastecimento_agua"
            name="abastecimentoAgua"
            not-null="false"
            type="java.lang.Long"
        />
        
        <property
            column="abastecimento_agua_outros"
            name="abastecimentoAguaOutros"
            not-null="false"
            type="java.lang.String"
            length="50"
        />
        
        <property
            column="agua_corrente"
            name="aguaCorrente"
            not-null="false"
            type="java.lang.Long"
        />
        
        <property
            column="outros_servicos"
            name="outrosServicos"
            not-null="false"
            type="java.lang.Long"
        />
        
        <property
            column="ds_outros_servicos"
            name="descricaoOutrosServicos"
            not-null="false"
            type="java.lang.String"
            length="50"
        />
        
        <property
            column="ds_instalacoes_sanitarias"
            name="descricaoInstalacoesSanitarias"
            not-null="false"
            type="java.lang.String"
            length="500"
        />
        
        <property
            name="dataCadastro"
            column="dt_cadastro"
            type="timestamp"
            not-null="true"
        />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.controle.Usuario"
            column="cd_usuario"
            name="usuario"
            not-null="true"
        />
        
        <property
            name="dataInicial"
            column="data_inicial"
            type="java.util.Date"
            not-null="true"
        />
        
        <property
            name="dataFinal"
            column="data_final"
            type="java.util.Date"
            not-null="true"
        />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco"
            column="cd_vigilancia_endereco"
            name="vigilanciaEndereco"
            not-null="false"
        />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia"
            name="requerimentoVigilancia"
            column="cd_req_vigilancia"
            not-null="false"
        />

        <property
            name="situacao"
            column="situacao"
            type="java.lang.Long"
            not-null="false"
        />

        <property
            column="ds_observacao_destaque"
            name="descricaoObservacaoDestaque"
            type="java.lang.String"
            not-null="false"
        />

        <property
                column="nro_alvara"
                name="numeroAlvara"
                type="java.lang.Long"
                not-null="true"
        />
    </class>
</hibernate-mapping>
