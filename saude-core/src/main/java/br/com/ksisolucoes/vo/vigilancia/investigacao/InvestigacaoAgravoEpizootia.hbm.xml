<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >
<hibernate-mapping package="br.com.ksisolucoes.vo.vigilancia.investigacao">

    <class name="InvestigacaoAgravoEpizootia" table="investigacao_agr_epizootia">

        <id name="codigo"
            type="java.lang.Long"
            column="cd_investigacao_agr_epizootia" >
            <generator class="sequence">
                <param name="sequence">seq_investigacao_agr_epizootia</param>
            </generator>
        </id>

        <version column="version" name="version" type="long"/>

        <property
                name="flagInformacoesComplementares"
                column="flag_informacoes_complementares"
                type="java.lang.String"
                not-null="true"
        />
        <many-to-one
                class="br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo"
                name="registroAgravo"
                not-null="true"
                column="cd_registro_agravo"
        />

        <!-- DADOS DE OCORRENCIA -->

        <property
                name="fonteInformacao"
                column="fonte_informacao"
                type="java.lang.String"
                length="50"
        />
        <property
                name="telefoneFonteInformacao"
                column="telefone_fonte_informacao"
                type="java.lang.String"
                length="15"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.basico.Cidade"
                name="municipioOcorrencia"
                column="cd_municipio_ocorrencia"
                not-null="false"
        />
        <property
                name="distrito"
                column="distrito"
                type="java.lang.String"
                length="20"
        />
        <property
                name="bairro"
                column="bairro"
                type="java.lang.String"
                length="20"
        />
        <property
                name="logradouro"
                column="logradouro"
                type="java.lang.String"
                length="50"
        />
        <property
                name="numero"
                column="numero"
                type="java.lang.String"
                length="5"
        />
        <property
                name="complemento"
                column="complemento"
                type="java.lang.String"
                length="50"
        />
        <property
                name="geocampo1"
                column="geocampo_1"
                type="java.lang.String"
                length="50"
        />
        <property
                name="geocampo2"
                column="geocampo_2"
                type="java.lang.String"
                length="50"
        />
        <property
                name="pontoReferencia"
                column="ponto_referencia"
                type="java.lang.String"
                length="50"
        />
        <property
                name="cep"
                column="cep"
                type="java.lang.String"
                length="15"
        />
        <property
                name="telefone"
                column="telefone"
                type="java.lang.String"
                length="15"
        />
        <property
                name="zona"
                column="zona"
                type="java.lang.Long"
        />
        <property
                name="ambiente"
                column="ambiente"
                type="java.lang.Long"
        />
        <property
                name="ambienteOutro"
                column="ambiente_outro"
                type="java.lang.String"
                length="50"
        />


        <property
                name="coletaMaterial"
                column="coleta_material"
                type="java.lang.Long"
        />
        <property
                name="dataColeta"
                column="dt_coleta"
                type="java.util.Date"
        />


        <property
                name="coletaMaterialFigado"
                column="coleta_material_figado"
                type="java.lang.Long"
        />
        <property
                name="coletaMaterialRim"
                column="coleta_material_rim"
                type="java.lang.Long"
        />
        <property
                name="coletaMaterialBaco"
                column="coleta_material_baco"
                type="java.lang.Long"
        />
        <property
                name="coletaMaterialCerebro"
                column="coleta_material_cerebro"
                type="java.lang.Long"
        />
        <property
                name="coletaMaterialCoracao"
                column="coleta_material_coracao"
                type="java.lang.Long"
        />
        <property
                name="coletaMaterialFezes"
                column="coleta_material_fezes"
                type="java.lang.Long"
        />
        <property
                name="coletaMaterialSoro"
                column="coleta_material_soro"
                type="java.lang.Long"
        />
        <property
                name="coletaMaterialSangueTotal"
                column="coleta_material_sangue_total"
                type="java.lang.Long"
        />
        <property
                name="coletaMaterialOutro"
                column="coleta_material_outro"
                type="java.lang.String"
                length="50"
        />

        <property
                name="animaisAcometidosOpcao1"
                column="animais_acometidos_opcao_1"
                type="java.lang.Long"
        />
        <property
                name="animaisAcometidosOpcao1Doentes"
                column="animais_acometidos_opcao_1_doentes"
                type="java.lang.String"
                length="5"
        />
        <property
                name="animaisAcometidosOpcao1Mortos"
                column="animais_acometidos_opcao_1_mortos"
                type="java.lang.String"
                length="5"
        />
        <property
                name="animaisAcometidosOpcao1Especificar"
                column="animais_acometidos_opcao_1_especificar"
                type="java.lang.String"
                length="50"
        />

        <property
                name="animaisAcometidosOpcao2"
                column="animais_acometidos_opcao_2"
                type="java.lang.Long"
        />
        <property
                name="animaisAcometidosOpcao2Doentes"
                column="animais_acometidos_opcao_2_doentes"
                type="java.lang.String"
                length="5"
        />
        <property
                name="animaisAcometidosOpcao2Mortos"
                column="animais_acometidos_opcao_2_mortos"
                type="java.lang.String"
                length="5"
        />
        <property
                name="animaisAcometidosOpcao2Especificar"
                column="animais_acometidos_opcao_2_especificar"
                type="java.lang.String"
                length="50"
        />

        <property
                name="suspeitaDiagnostica1"
                column="suspeita_diagnostica_1"
                type="java.lang.Long"
        />
        <property
                name="suspeitaDiagnostica1Especificar"
                column="suspeita_diagnostica_1_especificar"
                type="java.lang.String"
                length="50"
        />

        <property
                name="suspeitaDiagnostica2"
                column="suspeita_diagnostica_2"
                type="java.lang.Long"
        />
        <property
                name="suspeitaDiagnostica2Especificar"
                column="suspeita_diagnostica_2_especificar"
                type="java.lang.String"
                length="50"
        />

        <property
                name="suspeitaDiagnostica3"
                column="suspeita_diagnostica_3"
                type="java.lang.Long"
        />
        <property
                name="suspeitaDiagnostica3Especificar"
                column="suspeita_diagnostica_3_especificar"
                type="java.lang.String"
                length="50"
        />

        <property
                name="resultadoLaboralRaiva"
                column="resultado_laboral_raiva"
                type="java.lang.Long"
        />
        <property
                name="resultadoLaboralEncefaliteEquina"
                column="resultado_laboral_encefalite_equina"
                type="java.lang.Long"
        />
        <property
                name="resultadoLaboralFebreNilo"
                column="resultado_laboral_febre_nilo"
                type="java.lang.Long"
        />
        <property
                name="resultadoLaboralEncefaliteEspongiformeBovina"
                column="resultado_laboral_encenfalite_espongiforme_bovina"
                type="java.lang.Long"
        />
        <property
                name="resultadoLaboralFebreAmarela"
                column="resultado_laboral_febre_amarela"
                type="java.lang.Long"
        />
        <property
                name="resultadoLaboralInfluenzaAviaria"
                column="resultado_laboral_influenza_aviaria"
                type="java.lang.Long"
        />
        <property
                name="resultadoLaboralEspecificar"
                column="resultado_laboral_especificar"
                type="java.lang.String"
                length="50"
        />

        <!-- OBS -->
        <property
                name="observacao"
                column="observacao"
                type="java.lang.String"
                length="5000"
        />

        <!-- ENCERRAMENTO -->
        <property
                name="dataEncerramento"
                column="dt_encerramento"
                type="java.util.Date"
        />
        <many-to-one
                class="br.com.ksisolucoes.vo.controle.Usuario"
                name="usuarioEncerramento"
                not-null="false"
                column="cd_usuario_encerramento"
        />

    </class>
</hibernate-mapping>