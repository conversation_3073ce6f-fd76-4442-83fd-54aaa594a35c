package br.com.ksisolucoes.vo.agendamento.tfd.base;

import java.io.Serializable;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;


/**
 * This is an object that contains data related to the exame_anatomopatologico table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="exame_anatomopatologico"
 */

public abstract class BaseExameAnatomopatologico extends BaseRootVO implements Serializable {

	public static String REF = "ExameAnatomopatologico";
	public static final String PROP_REQUISICAO_ENVIO_EXAME = "requisicaoEnvioExame";
	public static final String PROP_PESO = "peso";
	public static final String PROP_EXAME_SOLICITADO = "exameSolicitado";
	public static final String PROP_DADO_CLINICO = "dadoClinico";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_ALTURA = "altura";
	public static final String PROP_DATA_RETIRADA = "dataRetirada";
	public static final String PROP_RESULTADO_EXAME = "resultadoExame";
	public static final String PROP_MATERIA_EXAMINAR = "materiaExaminar";
	public static final String PROP_LOCAL_CORPO_RETIRADA = "localCorpoRetirada";


	// constructors
	public BaseExameAnatomopatologico () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseExameAnatomopatologico (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseExameAnatomopatologico (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.agendamento.tfd.RequisicaoEnvioExame requisicaoEnvioExame) {

		this.setCodigo(codigo);
		this.setRequisicaoEnvioExame(requisicaoEnvioExame);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Long peso;
	private java.lang.Long altura;
	private java.lang.String dadoClinico;
	private java.lang.String resultadoExame;
	private java.lang.String materiaExaminar;
	private java.lang.String localCorpoRetirada;
	private java.util.Date dataRetirada;
	private java.lang.String exameSolicitado;

	// many to one
	private br.com.ksisolucoes.vo.agendamento.tfd.RequisicaoEnvioExame requisicaoEnvioExame;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_ex_anatomopatologico"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: peso
	 */
	public java.lang.Long getPeso () {
		return getPropertyValue(this, peso, PROP_PESO); 
	}

	/**
	 * Set the value related to the column: peso
	 * @param peso the peso value
	 */
	public void setPeso (java.lang.Long peso) {
//        java.lang.Long pesoOld = this.peso;
		this.peso = peso;
//        this.getPropertyChangeSupport().firePropertyChange ("peso", pesoOld, peso);
	}



	/**
	 * Return the value associated with the column: altura
	 */
	public java.lang.Long getAltura () {
		return getPropertyValue(this, altura, PROP_ALTURA); 
	}

	/**
	 * Set the value related to the column: altura
	 * @param altura the altura value
	 */
	public void setAltura (java.lang.Long altura) {
//        java.lang.Long alturaOld = this.altura;
		this.altura = altura;
//        this.getPropertyChangeSupport().firePropertyChange ("altura", alturaOld, altura);
	}



	/**
	 * Return the value associated with the column: dado_clinico
	 */
	public java.lang.String getDadoClinico () {
		return getPropertyValue(this, dadoClinico, PROP_DADO_CLINICO); 
	}

	/**
	 * Set the value related to the column: dado_clinico
	 * @param dadoClinico the dado_clinico value
	 */
	public void setDadoClinico (java.lang.String dadoClinico) {
//        java.lang.String dadoClinicoOld = this.dadoClinico;
		this.dadoClinico = dadoClinico;
//        this.getPropertyChangeSupport().firePropertyChange ("dadoClinico", dadoClinicoOld, dadoClinico);
	}



	/**
	 * Return the value associated with the column: resultado_exame
	 */
	public java.lang.String getResultadoExame () {
		return getPropertyValue(this, resultadoExame, PROP_RESULTADO_EXAME); 
	}

	/**
	 * Set the value related to the column: resultado_exame
	 * @param resultadoExame the resultado_exame value
	 */
	public void setResultadoExame (java.lang.String resultadoExame) {
//        java.lang.String resultadoExameOld = this.resultadoExame;
		this.resultadoExame = resultadoExame;
//        this.getPropertyChangeSupport().firePropertyChange ("resultadoExame", resultadoExameOld, resultadoExame);
	}



	/**
	 * Return the value associated with the column: materia_examinar
	 */
	public java.lang.String getMateriaExaminar () {
		return getPropertyValue(this, materiaExaminar, PROP_MATERIA_EXAMINAR); 
	}

	/**
	 * Set the value related to the column: materia_examinar
	 * @param materiaExaminar the materia_examinar value
	 */
	public void setMateriaExaminar (java.lang.String materiaExaminar) {
//        java.lang.String materiaExaminarOld = this.materiaExaminar;
		this.materiaExaminar = materiaExaminar;
//        this.getPropertyChangeSupport().firePropertyChange ("materiaExaminar", materiaExaminarOld, materiaExaminar);
	}



	/**
	 * Return the value associated with the column: local_corpo_retirada
	 */
	public java.lang.String getLocalCorpoRetirada () {
		return getPropertyValue(this, localCorpoRetirada, PROP_LOCAL_CORPO_RETIRADA); 
	}

	/**
	 * Set the value related to the column: local_corpo_retirada
	 * @param localCorpoRetirada the local_corpo_retirada value
	 */
	public void setLocalCorpoRetirada (java.lang.String localCorpoRetirada) {
//        java.lang.String localCorpoRetiradaOld = this.localCorpoRetirada;
		this.localCorpoRetirada = localCorpoRetirada;
//        this.getPropertyChangeSupport().firePropertyChange ("localCorpoRetirada", localCorpoRetiradaOld, localCorpoRetirada);
	}



	/**
	 * Return the value associated with the column: dt_retirada
	 */
	public java.util.Date getDataRetirada () {
		return getPropertyValue(this, dataRetirada, PROP_DATA_RETIRADA); 
	}

	/**
	 * Set the value related to the column: dt_retirada
	 * @param dataRetirada the dt_retirada value
	 */
	public void setDataRetirada (java.util.Date dataRetirada) {
//        java.util.Date dataRetiradaOld = this.dataRetirada;
		this.dataRetirada = dataRetirada;
//        this.getPropertyChangeSupport().firePropertyChange ("dataRetirada", dataRetiradaOld, dataRetirada);
	}



	/**
	 * Return the value associated with the column: exame_solicitado
	 */
	public java.lang.String getExameSolicitado () {
		return getPropertyValue(this, exameSolicitado, PROP_EXAME_SOLICITADO); 
	}

	/**
	 * Set the value related to the column: exame_solicitado
	 * @param exameSolicitado the exame_solicitado value
	 */
	public void setExameSolicitado (java.lang.String exameSolicitado) {
//        java.lang.String exameSolicitadoOld = this.exameSolicitado;
		this.exameSolicitado = exameSolicitado;
//        this.getPropertyChangeSupport().firePropertyChange ("exameSolicitado", exameSolicitadoOld, exameSolicitado);
	}



	/**
	 * Return the value associated with the column: cd_req_envio
	 */
	public br.com.ksisolucoes.vo.agendamento.tfd.RequisicaoEnvioExame getRequisicaoEnvioExame () {
		return getPropertyValue(this, requisicaoEnvioExame, PROP_REQUISICAO_ENVIO_EXAME); 
	}

	/**
	 * Set the value related to the column: cd_req_envio
	 * @param requisicaoEnvioExame the cd_req_envio value
	 */
	public void setRequisicaoEnvioExame (br.com.ksisolucoes.vo.agendamento.tfd.RequisicaoEnvioExame requisicaoEnvioExame) {
//        br.com.ksisolucoes.vo.agendamento.tfd.RequisicaoEnvioExame requisicaoEnvioExameOld = this.requisicaoEnvioExame;
		this.requisicaoEnvioExame = requisicaoEnvioExame;
//        this.getPropertyChangeSupport().firePropertyChange ("requisicaoEnvioExame", requisicaoEnvioExameOld, requisicaoEnvioExame);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.agendamento.tfd.ExameAnatomopatologico)) return false;
		else {
			br.com.ksisolucoes.vo.agendamento.tfd.ExameAnatomopatologico exameAnatomopatologico = (br.com.ksisolucoes.vo.agendamento.tfd.ExameAnatomopatologico) obj;
			if (null == this.getCodigo() || null == exameAnatomopatologico.getCodigo()) return false;
			else return (this.getCodigo().equals(exameAnatomopatologico.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }

    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}