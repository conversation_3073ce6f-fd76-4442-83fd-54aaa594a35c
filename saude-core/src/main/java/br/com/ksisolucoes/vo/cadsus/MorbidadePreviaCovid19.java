package br.com.ksisolucoes.vo.cadsus;

import java.io.Serializable;

import br.com.ksisolucoes.vo.cadsus.base.BaseMorbidadePreviaCovid19;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class MorbidadePreviaCovid19 extends BaseMorbidadePreviaCovid19 implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public MorbidadePreviaCovid19 () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public MorbidadePreviaCovid19 (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public MorbidadePreviaCovid19 (
		java.lang.Long codigo,
		java.lang.String descricao) {

		super (
			codigo,
			descricao);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}