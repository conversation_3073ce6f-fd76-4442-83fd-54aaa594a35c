<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.siab"  >
	<class name="SiabPma2c" table="siab_pma2c" >

        <id
            column="cd_siab_pma2"
            name="codigo"
            type="java.lang.Long"
            >
            <generator class="assigned" />
        </id> <version column="version" name="version" type="long" />
        
        <many-to-one
            name="equipeProfissional"
            class="br.com.ksisolucoes.vo.basico.EquipeProfissional"
            not-null="false"
        >
            <column name="cd_equipe_profissional"/>
        </many-to-one>
        
        <property
            column="mes"
            name="mes"
            not-null="true"
            type="java.lang.Long"
            />
        
        <property
            column="ano"
            name="ano"
            not-null="true"
            type="java.lang.Long"
            />
        
        <property
            column="dt_usuario"
            name="dataUsuario"
            not-null="true"
            type="java.util.Date"
            />
        
        <property
            column="status"
            name="status"
            not-null="true"
            type="java.lang.Long"
            />
            
        <many-to-one
                class="br.com.ksisolucoes.vo.controle.Usuario"
                name="usuario"
                not-null="true"
                column="cd_usuario"
            />
            
        <property
            column="id_modelo"
            name="idModelo"
            not-null="false"
            type="java.lang.String"
            length="1"
            />
            
        <property
            column="CON_AGE"
            name="tipoConsultaDemandaAgendada"
            not-null="false"
            type="java.lang.Long"
            />
            
        <property
            column="CON_IME"
            name="tipoConsultaDemandaImediata"
            not-null="false"
            type="java.lang.Long"
            />
            
        <property
            column="CON_CON"
            name="tipoConsultaCuidadoContinuado"
            not-null="false"
            type="java.lang.Long"
            />
            
        <property
            column="CON_UGE"
            name="tipoConsultaUrgenciaObservacao"
            not-null="false"
            type="java.lang.Long"
            />
            
        <property
            column="AP_ALCO"
            name="tipoAtendimentoUsuarioAlcool"
            not-null="false"
            type="java.lang.Long"
            />
            
        <property
            column="AP_DROG"
            name="tipoAtendimentoUsuarioDrogas"
            not-null="false"
            type="java.lang.Long"
            />
            
        <property
            column="AP_SAUM"
            name="tipoAtendimentoSaudeMental"
            not-null="false"
            type="java.lang.Long"
            />
            
        <property
            column="SB_CONS"
            name="atendimentoDentistaPrimeiraConsultaOdontologicaProgramatica"
            not-null="false"
            type="java.lang.Long"
            />
            
        <property
            column="SB_ESCO"
            name="atendimentoDentistaEscovacaoDentalSupervisionada"
            not-null="false"
            type="java.lang.Long"
            />
            
        <property
            column="SB_TRAT"
            name="atendimentoDentistaTratamentoConcluido"
            not-null="false"
            type="java.lang.Long"
            />
            
        <property
            column="SB_URGE"
            name="atendimentoDentistaUrgencia"
            not-null="false"
            type="java.lang.Long"
            />
            
        <property
            column="SB_GEST"
            name="atendimentoDentistaGestantes"
            not-null="false"
            type="java.lang.Long"
            />
            
        <property
            column="SB_PROT"
            name="atendimentoDentistaInstalacoesProtesesDentarias"
            not-null="false"
            type="java.lang.Long"
            />
            
        <property
            column="SB_ENCA"
            name="encaminhamentoSaudeBucalAtencaoSecundaria"
            not-null="false"
            type="java.lang.Long"
            />
            
        <property
            column="SB_MUCO"
            name="marcadorSaudeBucalDiagnosticoAlteracaoMucosa"
            not-null="false"
            type="java.lang.Long"
            />
            
        <property
            column="SIGAB"
            name="sigab"
            not-null="false"
            type="java.lang.String"
            length="1"
            />
            
        </class>
</hibernate-mapping>
