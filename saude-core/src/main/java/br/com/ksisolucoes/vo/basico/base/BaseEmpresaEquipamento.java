package br.com.ksisolucoes.vo.basico.base;

import java.io.Serializable;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;


/**
 * This is an object that contains data related to the empresa_equipamento table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="empresa_equipamento"
 */

public abstract class BaseEmpresaEquipamento extends BaseRootVO implements Serializable {

	public static String REF = "EmpresaEquipamento";
	public static final String PROP_ID = "id";
	public static final String PROP_SUS = "sus";
	public static final String PROP_QUANTIDADE_USO = "quantidadeUso";
	public static final String PROP_QUANTIDADE_EXISTENTE = "quantidadeExistente";
	public static final String PROP_DATA_ATUALIZACAO = "dataAtualizacao";


	// constructors
	public BaseEmpresaEquipamento () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseEmpresaEquipamento (br.com.ksisolucoes.vo.basico.EmpresaEquipamentoPK id) {
		this.setId(id);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private br.com.ksisolucoes.vo.basico.EmpresaEquipamentoPK id;

	// fields
	private java.lang.Long quantidadeExistente;
	private java.lang.Long quantidadeUso;
	private java.lang.String sus;
	private java.util.Date dataAtualizacao;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     */
	public br.com.ksisolucoes.vo.basico.EmpresaEquipamentoPK getId () {
	    return getPropertyValue(this,  id, "id" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param id the new ID
	 */
	public void setId (br.com.ksisolucoes.vo.basico.EmpresaEquipamentoPK id) {
		this.id = id;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: qtd_existente
	 */
	public java.lang.Long getQuantidadeExistente () {
		return getPropertyValue(this, quantidadeExistente, PROP_QUANTIDADE_EXISTENTE); 
	}

	/**
	 * Set the value related to the column: qtd_existente
	 * @param quantidadeExistente the qtd_existente value
	 */
	public void setQuantidadeExistente (java.lang.Long quantidadeExistente) {
//        java.lang.Long quantidadeExistenteOld = this.quantidadeExistente;
		this.quantidadeExistente = quantidadeExistente;
//        this.getPropertyChangeSupport().firePropertyChange ("quantidadeExistente", quantidadeExistenteOld, quantidadeExistente);
	}



	/**
	 * Return the value associated with the column: qtd_uso
	 */
	public java.lang.Long getQuantidadeUso () {
		return getPropertyValue(this, quantidadeUso, PROP_QUANTIDADE_USO); 
	}

	/**
	 * Set the value related to the column: qtd_uso
	 * @param quantidadeUso the qtd_uso value
	 */
	public void setQuantidadeUso (java.lang.Long quantidadeUso) {
//        java.lang.Long quantidadeUsoOld = this.quantidadeUso;
		this.quantidadeUso = quantidadeUso;
//        this.getPropertyChangeSupport().firePropertyChange ("quantidadeUso", quantidadeUsoOld, quantidadeUso);
	}



	/**
	 * Return the value associated with the column: sus
	 */
	public java.lang.String getSus () {
		return getPropertyValue(this, sus, PROP_SUS); 
	}

	/**
	 * Set the value related to the column: sus
	 * @param sus the sus value
	 */
	public void setSus (java.lang.String sus) {
//        java.lang.String susOld = this.sus;
		this.sus = sus;
//        this.getPropertyChangeSupport().firePropertyChange ("sus", susOld, sus);
	}



	/**
	 * Return the value associated with the column: dt_atualizacao
	 */
	public java.util.Date getDataAtualizacao () {
		return getPropertyValue(this, dataAtualizacao, PROP_DATA_ATUALIZACAO); 
	}

	/**
	 * Set the value related to the column: dt_atualizacao
	 * @param dataAtualizacao the dt_atualizacao value
	 */
	public void setDataAtualizacao (java.util.Date dataAtualizacao) {
//        java.util.Date dataAtualizacaoOld = this.dataAtualizacao;
		this.dataAtualizacao = dataAtualizacao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataAtualizacao", dataAtualizacaoOld, dataAtualizacao);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.basico.EmpresaEquipamento)) return false;
		else {
			br.com.ksisolucoes.vo.basico.EmpresaEquipamento empresaEquipamento = (br.com.ksisolucoes.vo.basico.EmpresaEquipamento) obj;
			if (null == this.getId() || null == empresaEquipamento.getId()) return false;
			else return (this.getId().equals(empresaEquipamento.getId()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getId()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getId().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }

    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}