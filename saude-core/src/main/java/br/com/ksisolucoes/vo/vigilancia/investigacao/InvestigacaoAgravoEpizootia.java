package br.com.ksisolucoes.vo.vigilancia.investigacao;

import java.io.Serializable;

import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo;
import br.com.ksisolucoes.vo.vigilancia.investigacao.base.BaseInvestigacaoAgravoEpizootia;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class InvestigacaoAgravoEpizootia extends BaseInvestigacaoAgravoEpizootia implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public InvestigacaoAgravoEpizootia () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public InvestigacaoAgravoEpizootia (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public InvestigacaoAgravoEpizootia (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravo,
		java.lang.String flagInformacoesComplementares) {

		super (
			codigo,
			registroAgravo,
			flagInformacoesComplementares);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

	public static InvestigacaoAgravoEpizootia buscaPorRegistroAgravo(RegistroAgravo registroAgravo) {
		InvestigacaoAgravoEpizootia investigacao =
				LoadManager.getInstance(InvestigacaoAgravoEpizootia.class)
						.addProperties(new HQLProperties(InvestigacaoAgravoEpizootia.class).getProperties())
						.addProperties(new HQLProperties(
								RegistroAgravo.class,
								VOUtils.montarPath(InvestigacaoAgravoEpizootia.PROP_REGISTRO_AGRAVO)).getProperties())
						.addParameter(new QueryCustom.QueryCustomParameter(
								VOUtils.montarPath(InvestigacaoAgravoEpizootia.PROP_REGISTRO_AGRAVO, RegistroAgravo.PROP_CODIGO),
								registroAgravo.getCodigo()))
						.start().getVO();
		return investigacao;
	}
}