package br.com.ksisolucoes.vo.cadsus;

import java.io.Serializable;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.cadsus.base.BasePlanejamentoVisita;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;

public class PlanejamentoVisita extends BasePlanejamentoVisita implements CodigoManager {

    private static final long serialVersionUID = 1L;

    /*[CONSTRUCTOR MARKER BEGIN]*/
	public PlanejamentoVisita () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public PlanejamentoVisita (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public PlanejamentoVisita (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.cadsus.Profissional profissional,
		br.com.ksisolucoes.vo.basico.Empresa empresa,
		br.com.ksisolucoes.vo.basico.EquipeMicroArea equipeMicroArea,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		java.util.Date dataCadastro,
		java.util.Date dataVisitaInicio,
		java.util.Date dataVisitaFim,
		java.util.Date dataUsuario,
		java.lang.Long status) {

		super (
			codigo,
			profissional,
			empresa,
			equipeMicroArea,
			usuario,
			dataCadastro,
			dataVisitaInicio,
			dataVisitaFim,
			dataUsuario,
			status);
	}

    /*[CONSTRUCTOR MARKER END]*/
    public enum Status implements IEnum<Status> {

        PENDENTE(0L, Bundle.getStringApplication("rotulo_pendente")),
        CONCLUIDO(1L, Bundle.getStringApplication("rotulo_concluido")),;

        private Long value;
        private String descricao;

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

        private Status(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static Status valueOf(Long value) {
            for (Status status : Status.values()) {
                if (status.value().equals(value)) {
                    return status;
                }
            }
            return null;
        }
    }

    @Override
    public void setCodigoManager(Serializable key) {
        this.setCodigo((java.lang.Long) key);
    }

    @Override
    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}
