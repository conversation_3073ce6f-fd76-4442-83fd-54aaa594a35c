package br.com.ksisolucoes.vo.controle.base;

import java.io.Serializable;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;


/**
 * This is an object that contains data related to the permissoes table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="permissoes"
 */

public abstract class BasePermissao extends BaseRootVO implements Serializable {

	public static String REF = "Permissao";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_NUMERO_ACESSO = "numeroAcesso";
	public static final String PROP_DESCRICAO = "descricao";
	public static final String PROP_PROGRAMA = "programa";


	// constructors
	public BasePermissao () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BasePermissao (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BasePermissao (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.controle.Programa programa,
		java.lang.Integer numeroAcesso,
		java.lang.String descricao) {

		this.setCodigo(codigo);
		this.setPrograma(programa);
		this.setNumeroAcesso(numeroAcesso);
		this.setDescricao(descricao);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Integer numeroAcesso;
	private java.lang.String descricao;

	// many to one
	private br.com.ksisolucoes.vo.controle.Programa programa;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_permissao"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: nr_acesso
	 */
	public java.lang.Integer getNumeroAcesso () {
		return getPropertyValue(this, numeroAcesso, PROP_NUMERO_ACESSO); 
	}

	/**
	 * Set the value related to the column: nr_acesso
	 * @param numeroAcesso the nr_acesso value
	 */
	public void setNumeroAcesso (java.lang.Integer numeroAcesso) {
//        java.lang.Integer numeroAcessoOld = this.numeroAcesso;
		this.numeroAcesso = numeroAcesso;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroAcesso", numeroAcessoOld, numeroAcesso);
	}



	/**
	 * Return the value associated with the column: ds_permissao
	 */
	public java.lang.String getDescricao () {
		return getPropertyValue(this, descricao, PROP_DESCRICAO); 
	}

	/**
	 * Set the value related to the column: ds_permissao
	 * @param descricao the ds_permissao value
	 */
	public void setDescricao (java.lang.String descricao) {
//        java.lang.String descricaoOld = this.descricao;
		this.descricao = descricao;
//        this.getPropertyChangeSupport().firePropertyChange ("descricao", descricaoOld, descricao);
	}



	/**
	 * Return the value associated with the column: cd_programa
	 */
	public br.com.ksisolucoes.vo.controle.Programa getPrograma () {
		return getPropertyValue(this, programa, PROP_PROGRAMA); 
	}

	/**
	 * Set the value related to the column: cd_programa
	 * @param programa the cd_programa value
	 */
	public void setPrograma (br.com.ksisolucoes.vo.controle.Programa programa) {
//        br.com.ksisolucoes.vo.controle.Programa programaOld = this.programa;
		this.programa = programa;
//        this.getPropertyChangeSupport().firePropertyChange ("programa", programaOld, programa);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.controle.Permissao)) return false;
		else {
			br.com.ksisolucoes.vo.controle.Permissao permissao = (br.com.ksisolucoes.vo.controle.Permissao) obj;
			if (null == this.getCodigo() || null == permissao.getCodigo()) return false;
			else return (this.getCodigo().equals(permissao.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }

    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}