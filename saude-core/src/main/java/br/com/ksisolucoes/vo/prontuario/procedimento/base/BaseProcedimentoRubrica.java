package br.com.ksisolucoes.vo.prontuario.procedimento.base;

import java.io.Serializable;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;


/**
 * This is an object that contains data related to the procedimento_rubrica table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="procedimento_rubrica"
 */

public abstract class BaseProcedimentoRubrica extends BaseRootVO implements Serializable {

	public static String REF = "ProcedimentoRubrica";
	public static final String PROP_DATA_COMPETENCIA = "dataCompetencia";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_DESCRICAO = "descricao";


	// constructors
	public BaseProcedimentoRubrica () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseProcedimentoRubrica (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseProcedimentoRubrica (
		java.lang.Long codigo,
		java.lang.String descricao,
		java.util.Date dataCompetencia) {

		this.setCodigo(codigo);
		this.setDescricao(descricao);
		this.setDataCompetencia(dataCompetencia);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String descricao;
	private java.util.Date dataCompetencia;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_rubrica"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: ds_rubrica
	 */
	public java.lang.String getDescricao () {
		return getPropertyValue(this, descricao, PROP_DESCRICAO); 
	}

	/**
	 * Set the value related to the column: ds_rubrica
	 * @param descricao the ds_rubrica value
	 */
	public void setDescricao (java.lang.String descricao) {
//        java.lang.String descricaoOld = this.descricao;
		this.descricao = descricao;
//        this.getPropertyChangeSupport().firePropertyChange ("descricao", descricaoOld, descricao);
	}



	/**
	 * Return the value associated with the column: dt_competencia
	 */
	public java.util.Date getDataCompetencia () {
		return getPropertyValue(this, dataCompetencia, PROP_DATA_COMPETENCIA); 
	}

	/**
	 * Set the value related to the column: dt_competencia
	 * @param dataCompetencia the dt_competencia value
	 */
	public void setDataCompetencia (java.util.Date dataCompetencia) {
//        java.util.Date dataCompetenciaOld = this.dataCompetencia;
		this.dataCompetencia = dataCompetencia;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCompetencia", dataCompetenciaOld, dataCompetencia);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoRubrica)) return false;
		else {
			br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoRubrica procedimentoRubrica = (br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoRubrica) obj;
			if (null == this.getCodigo() || null == procedimentoRubrica.getCodigo()) return false;
			else return (this.getCodigo().equals(procedimentoRubrica.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }

    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}