package br.com.ksisolucoes.vo.entradas.estoque.base;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;

import java.io.Serializable;


/**
 * This is an object that contains data related to the grupo_estoque table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="grupo_estoque"
 */

public abstract class BaseGrupoEstoque extends BaseRootVO implements Serializable {

	public static String REF = "GrupoEstoque";
	public static final String PROP_ESTOQUE_RESERVADO_DEVOLUCAO = "estoqueReservadoDevolucao";
	public static final String PROP_ESTOQUE_DEVOLUCAO = "estoqueDevolucao";
	public static final String PROP_ESTOQUE_NAO_CONFORME = "estoqueNaoConforme";
	public static final String PROP_ESTOQUE_RESERVADO = "estoqueReservado";
	public static final String PROP_FABRICANTE = "fabricante";
	public static final String PROP_ID = "Id";
	public static final String PROP_NUMERO_ULTIMO_MOVIMENTO = "numeroUltimoMovimento";
	public static final String PROP_DATA_ULTIMA_ENTRADA = "dataUltimaEntrada";
	public static final String PROP_LABORATORIO_FABRICANTE = "laboratorioFabricante";
	public static final String PROP_DATA_VALIDADE = "dataValidade";
	public static final String PROP_ESTOQUE_ENCOMENDADO = "estoqueEncomendado";
	public static final String PROP_RO_DEPOSITO = "roDeposito";
	public static final String PROP_ESTOQUE_FISICO = "estoqueFisico";


	// constructors
	public BaseGrupoEstoque () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseGrupoEstoque (br.com.ksisolucoes.vo.entradas.estoque.GrupoEstoquePK id) {
		this.setId(id);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseGrupoEstoque (
		br.com.ksisolucoes.vo.entradas.estoque.GrupoEstoquePK id,
		java.lang.Double estoqueEncomendado,
		java.lang.Double estoqueFisico) {

		this.setId(id);
		this.setEstoqueEncomendado(estoqueEncomendado);
		this.setEstoqueFisico(estoqueFisico);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private br.com.ksisolucoes.vo.entradas.estoque.GrupoEstoquePK id;

	// fields
	private java.lang.String laboratorioFabricante;
	private java.lang.Double estoqueEncomendado;
	private java.lang.Double estoqueReservado;
	private java.lang.Double estoqueDevolucao;
	private java.lang.Double estoqueReservadoDevolucao;
	private java.lang.Double estoqueFisico;
	private java.lang.Double estoqueNaoConforme;
	private java.util.Date dataUltimaEntrada;
	private java.util.Date dataValidade;
	private java.lang.Long numeroUltimoMovimento;

	// many to one
	private br.com.ksisolucoes.vo.entradas.estoque.Deposito roDeposito;
	private br.com.ksisolucoes.vo.entradas.estoque.Fabricante fabricante;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     */
	public br.com.ksisolucoes.vo.entradas.estoque.GrupoEstoquePK getId () {
	    return getPropertyValue(this,  id, "id" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param id the new ID
	 */
	public void setId (br.com.ksisolucoes.vo.entradas.estoque.GrupoEstoquePK id) {
		this.id = id;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: laboratorio_fabricante
	 */
	public java.lang.String getLaboratorioFabricante () {
		return getPropertyValue(this, laboratorioFabricante, PROP_LABORATORIO_FABRICANTE); 
	}

	/**
	 * Set the value related to the column: laboratorio_fabricante
	 * @param laboratorioFabricante the laboratorio_fabricante value
	 */
	public void setLaboratorioFabricante (java.lang.String laboratorioFabricante) {
//        java.lang.String laboratorioFabricanteOld = this.laboratorioFabricante;
		this.laboratorioFabricante = laboratorioFabricante;
//        this.getPropertyChangeSupport().firePropertyChange ("laboratorioFabricante", laboratorioFabricanteOld, laboratorioFabricante);
	}



	/**
	 * Return the value associated with the column: estoque_encomendado
	 */
	public java.lang.Double getEstoqueEncomendado () {
		return getPropertyValue(this, estoqueEncomendado, PROP_ESTOQUE_ENCOMENDADO); 
	}

	/**
	 * Set the value related to the column: estoque_encomendado
	 * @param estoqueEncomendado the estoque_encomendado value
	 */
	public void setEstoqueEncomendado (java.lang.Double estoqueEncomendado) {
//        java.lang.Double estoqueEncomendadoOld = this.estoqueEncomendado;
		this.estoqueEncomendado = estoqueEncomendado;
//        this.getPropertyChangeSupport().firePropertyChange ("estoqueEncomendado", estoqueEncomendadoOld, estoqueEncomendado);
	}



	/**
	 * Return the value associated with the column: estoque_reservado
	 */
	public java.lang.Double getEstoqueReservado () {
		return getPropertyValue(this, estoqueReservado, PROP_ESTOQUE_RESERVADO); 
	}

	/**
	 * Set the value related to the column: estoque_reservado
	 * @param estoqueReservado the estoque_reservado value
	 */
	public void setEstoqueReservado (java.lang.Double estoqueReservado) {
//        java.lang.Double estoqueReservadoOld = this.estoqueReservado;
		this.estoqueReservado = estoqueReservado;
//        this.getPropertyChangeSupport().firePropertyChange ("estoqueReservado", estoqueReservadoOld, estoqueReservado);
	}



	/**
	 * Return the value associated with the column: estoque_devolucao
	 */
	public java.lang.Double getEstoqueDevolucao () {
		return getPropertyValue(this, estoqueDevolucao, PROP_ESTOQUE_DEVOLUCAO); 
	}

	/**
	 * Set the value related to the column: estoque_devolucao
	 * @param estoqueDevolucao the estoque_devolucao value
	 */
	public void setEstoqueDevolucao (java.lang.Double estoqueDevolucao) {
//        java.lang.Double estoqueDevolucaoOld = this.estoqueDevolucao;
		this.estoqueDevolucao = estoqueDevolucao;
//        this.getPropertyChangeSupport().firePropertyChange ("estoqueDevolucao", estoqueDevolucaoOld, estoqueDevolucao);
	}



	/**
	 * Return the value associated with the column: estoque_reversado_devolucao
	 */
	public java.lang.Double getEstoqueReservadoDevolucao () {
		return getPropertyValue(this, estoqueReservadoDevolucao, PROP_ESTOQUE_RESERVADO_DEVOLUCAO); 
	}

	/**
	 * Set the value related to the column: estoque_reversado_devolucao
	 * @param estoqueReservadoDevolucao the estoque_reversado_devolucao value
	 */
	public void setEstoqueReservadoDevolucao (java.lang.Double estoqueReservadoDevolucao) {
//        java.lang.Double estoqueReservadoDevolucaoOld = this.estoqueReservadoDevolucao;
		this.estoqueReservadoDevolucao = estoqueReservadoDevolucao;
//        this.getPropertyChangeSupport().firePropertyChange ("estoqueReservadoDevolucao", estoqueReservadoDevolucaoOld, estoqueReservadoDevolucao);
	}



	/**
	 * Return the value associated with the column: estoque_fisico
	 */
	public java.lang.Double getEstoqueFisico () {
		return getPropertyValue(this, estoqueFisico, PROP_ESTOQUE_FISICO); 
	}

	/**
	 * Set the value related to the column: estoque_fisico
	 * @param estoqueFisico the estoque_fisico value
	 */
	public void setEstoqueFisico (java.lang.Double estoqueFisico) {
//        java.lang.Double estoqueFisicoOld = this.estoqueFisico;
		this.estoqueFisico = estoqueFisico;
//        this.getPropertyChangeSupport().firePropertyChange ("estoqueFisico", estoqueFisicoOld, estoqueFisico);
	}



	/**
	 * Return the value associated with the column: estoque_nao_conforme
	 */
	public java.lang.Double getEstoqueNaoConforme () {
		return getPropertyValue(this, estoqueNaoConforme, PROP_ESTOQUE_NAO_CONFORME); 
	}

	/**
	 * Set the value related to the column: estoque_nao_conforme
	 * @param estoqueNaoConforme the estoque_nao_conforme value
	 */
	public void setEstoqueNaoConforme (java.lang.Double estoqueNaoConforme) {
//        java.lang.Double estoqueNaoConformeOld = this.estoqueNaoConforme;
		this.estoqueNaoConforme = estoqueNaoConforme;
//        this.getPropertyChangeSupport().firePropertyChange ("estoqueNaoConforme", estoqueNaoConformeOld, estoqueNaoConforme);
	}



	/**
	 * Return the value associated with the column: dt_ult_ent
	 */
	public java.util.Date getDataUltimaEntrada () {
		return getPropertyValue(this, dataUltimaEntrada, PROP_DATA_ULTIMA_ENTRADA); 
	}

	/**
	 * Set the value related to the column: dt_ult_ent
	 * @param dataUltimaEntrada the dt_ult_ent value
	 */
	public void setDataUltimaEntrada (java.util.Date dataUltimaEntrada) {
//        java.util.Date dataUltimaEntradaOld = this.dataUltimaEntrada;
		this.dataUltimaEntrada = dataUltimaEntrada;
//        this.getPropertyChangeSupport().firePropertyChange ("dataUltimaEntrada", dataUltimaEntradaOld, dataUltimaEntrada);
	}



	/**
	 * Return the value associated with the column: dt_validade
	 */
	public java.util.Date getDataValidade () {
		return getPropertyValue(this, dataValidade, PROP_DATA_VALIDADE); 
	}

	/**
	 * Set the value related to the column: dt_validade
	 * @param dataValidade the dt_validade value
	 */
	public void setDataValidade (java.util.Date dataValidade) {
//        java.util.Date dataValidadeOld = this.dataValidade;
		this.dataValidade = dataValidade;
//        this.getPropertyChangeSupport().firePropertyChange ("dataValidade", dataValidadeOld, dataValidade);
	}



	/**
	 * Return the value associated with the column: num_ultimo_lancto
	 */
	public java.lang.Long getNumeroUltimoMovimento () {
		return getPropertyValue(this, numeroUltimoMovimento, PROP_NUMERO_ULTIMO_MOVIMENTO); 
	}

	/**
	 * Set the value related to the column: num_ultimo_lancto
	 * @param numeroUltimoMovimento the num_ultimo_lancto value
	 */
	public void setNumeroUltimoMovimento (java.lang.Long numeroUltimoMovimento) {
//        java.lang.Long numeroUltimoMovimentoOld = this.numeroUltimoMovimento;
		this.numeroUltimoMovimento = numeroUltimoMovimento;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroUltimoMovimento", numeroUltimoMovimentoOld, numeroUltimoMovimento);
	}



	/**
	 * Return the value associated with the column: cod_deposito
	 */
	public br.com.ksisolucoes.vo.entradas.estoque.Deposito getRoDeposito () {
		return getPropertyValue(this, roDeposito, PROP_RO_DEPOSITO); 
	}

	/**
	 * Set the value related to the column: cod_deposito
	 * @param roDeposito the cod_deposito value
	 */
	public void setRoDeposito (br.com.ksisolucoes.vo.entradas.estoque.Deposito roDeposito) {
//        br.com.ksisolucoes.vo.entradas.estoque.Deposito roDepositoOld = this.roDeposito;
		this.roDeposito = roDeposito;
//        this.getPropertyChangeSupport().firePropertyChange ("roDeposito", roDepositoOld, roDeposito);
	}



	/**
	 * Return the value associated with the column: cd_fabricante
	 */
	public br.com.ksisolucoes.vo.entradas.estoque.Fabricante getFabricante () {
		return getPropertyValue(this, fabricante, PROP_FABRICANTE); 
	}

	/**
	 * Set the value related to the column: cd_fabricante
	 * @param fabricante the cd_fabricante value
	 */
	public void setFabricante (br.com.ksisolucoes.vo.entradas.estoque.Fabricante fabricante) {
//        br.com.ksisolucoes.vo.entradas.estoque.Fabricante fabricanteOld = this.fabricante;
		this.fabricante = fabricante;
//        this.getPropertyChangeSupport().firePropertyChange ("fabricante", fabricanteOld, fabricante);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.entradas.estoque.GrupoEstoque)) return false;
		else {
			br.com.ksisolucoes.vo.entradas.estoque.GrupoEstoque grupoEstoque = (br.com.ksisolucoes.vo.entradas.estoque.GrupoEstoque) obj;
			if (null == this.getId() || null == grupoEstoque.getId()) return false;
			else return (this.getId().equals(grupoEstoque.getId()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getId()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getId().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}