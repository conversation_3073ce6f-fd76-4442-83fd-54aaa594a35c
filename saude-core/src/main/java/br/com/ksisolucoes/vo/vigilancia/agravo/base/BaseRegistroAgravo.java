package br.com.ksisolucoes.vo.vigilancia.agravo.base;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;

import java.io.Serializable;


/**
 * This is an object that contains data related to the registro_agravo table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="registro_agravo"
 */

public abstract class BaseRegistroAgravo extends BaseRootVO implements Serializable {

	public static String REF = "RegistroAgravo";
	public static final String PROP_PROFISSIONAL_INVESTIGACAO = "profissionalInvestigacao";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_DATA_CADASTRO = "dataCadastro";
	public static final String PROP_EMPRESA = "empresa";
	public static final String PROP_MOTIVO_CANCELAMENTO = "motivoCancelamento";
	public static final String PROP_ENDERECO = "endereco";
	public static final String PROP_DATA_CANCELAMENTO = "dataCancelamento";
	public static final String PROP_DESCRICAO_PROFISSIONAL_INTEGRACAO = "descricaoProfissionalIntegracao";
	public static final String PROP_ESCOLARIDADE = "escolaridade";
	public static final String PROP_ATENDIMENTO = "atendimento";
	public static final String PROP_DATA_INTEGRACAO = "dataIntegracao";
	public static final String PROP_DATA_CONCLUSAO = "dataConclusao";
	public static final String PROP_CID = "cid";
	public static final String PROP_PROFISSIONAL = "profissional";
	public static final String PROP_IDADE_GESTACIONAL = "idadeGestacional";
	public static final String PROP_USUARIO_CADSUS = "usuarioCadsus";
	public static final String PROP_LATITUDE = "latitude";
	public static final String PROP_ORIGEM = "origem";
	public static final String PROP_FLAG_SINALIZACAO_CONFIRMACAO = "flagSinalizacaoConfirmacao";
	public static final String PROP_DESCRICAO_EMPRESA_INTEGRACAO = "descricaoEmpresaIntegracao";
	public static final String PROP_CODIGO_REGISTRO_AGRAVO_INTEGRACAO = "codigoRegistroAgravoIntegracao";
	public static final String PROP_DATA_PRIMEIROS_SINTOMAS = "dataPrimeirosSintomas";
	public static final String PROP_USUARIO_CANCELAMENTO = "usuarioCancelamento";
	public static final String PROP_GESTANTE = "gestante";
	public static final String PROP_USUARIO_CADASTRO = "usuarioCadastro";
	public static final String PROP_DATA_ENCERRAMENTO = "dataEncerramento";
	public static final String PROP_CODIGO_NOTIFICACAO = "codigoNotificacao";
	public static final String PROP_STATUS = "status";
	public static final String PROP_DATA_REGISTRO = "dataRegistro";
	public static final String PROP_UNIDADE_PROFISSIONAL_INVESTIGACAO = "unidadeProfissionalInvestigacao";
	public static final String PROP_LONGITUDE = "longitude";
	public static final String PROP_OBSERVACAO = "observacao";
	public static final String PROP_ESCOLARIDADE_EQUIVALENCIA = "escolaridadeEquivalencia";

	// constructors
	public BaseRegistroAgravo () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseRegistroAgravo (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseRegistroAgravo (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus,
		br.com.ksisolucoes.vo.prontuario.basico.Cid cid,
		br.com.ksisolucoes.vo.cadsus.Profissional profissional,
		br.com.ksisolucoes.vo.cadsus.Profissional profissionalInvestigacao,
		br.com.ksisolucoes.vo.basico.Empresa empresa,
		br.com.ksisolucoes.vo.basico.Empresa unidadeProfissionalInvestigacao,
		br.com.ksisolucoes.vo.controle.Usuario usuarioCadastro,
		java.lang.Long gestante,
		java.util.Date dataCadastro,
		java.util.Date dataRegistro,
		java.lang.Long status) {

		this.setCodigo(codigo);
		this.setUsuarioCadsus(usuarioCadsus);
		this.setCid(cid);
		this.setProfissional(profissional);
		this.setProfissionalInvestigacao(profissionalInvestigacao);
		this.setEmpresa(empresa);
		this.setUnidadeProfissionalInvestigacao(unidadeProfissionalInvestigacao);
		this.setUsuarioCadastro(usuarioCadastro);
		this.setGestante(gestante);
		this.setDataCadastro(dataCadastro);
		this.setDataRegistro(dataRegistro);
		this.setStatus(status);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Long gestante;
	private java.util.Date dataCadastro;
	private java.lang.String observacao;
	private java.util.Date dataRegistro;
	private java.util.Date dataEncerramento;
	private java.util.Date dataConclusao;
	private java.lang.Long status;
	private java.util.Date dataCancelamento;
	private java.lang.String motivoCancelamento;
	private java.lang.Long origem;
	private java.lang.Long codigoRegistroAgravoIntegracao;
	private java.lang.String descricaoEmpresaIntegracao;
	private java.lang.String descricaoProfissionalIntegracao;
	private java.util.Date dataIntegracao;
	private java.util.Date dataPrimeirosSintomas;
	private java.lang.Long flagSinalizacaoConfirmacao;
	private java.lang.Double latitude;
	private java.lang.Double longitude;
	private java.lang.Long codigoNotificacao;
	private java.lang.Long escolaridade;
	private java.lang.Long idadeGestacional;
	private java.lang.Long escolaridadeEquivalencia;

	// many to one
	private br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimento;
	private br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus;
	private br.com.ksisolucoes.vo.prontuario.basico.Cid cid;
	private br.com.ksisolucoes.vo.cadsus.Profissional profissional;
	private br.com.ksisolucoes.vo.cadsus.Profissional profissionalInvestigacao;
	private br.com.ksisolucoes.vo.basico.Empresa empresa;
	private br.com.ksisolucoes.vo.basico.Empresa unidadeProfissionalInvestigacao;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioCadastro;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamento;
	private br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus endereco;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_registro_agravo"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: gestante
	 */
	public java.lang.Long getGestante () {
		return getPropertyValue(this, gestante, PROP_GESTANTE);
	}

	/**
	 * Set the value related to the column: gestante
	 * @param gestante the gestante value
	 */
	public void setGestante (java.lang.Long gestante) {
//        java.lang.Long gestanteOld = this.gestante;
		this.gestante = gestante;
//        this.getPropertyChangeSupport().firePropertyChange ("gestante", gestanteOld, gestante);
	}



	/**
	 * Return the value associated with the column: dt_cadastro
	 */
	public java.util.Date getDataCadastro () {
		return getPropertyValue(this, dataCadastro, PROP_DATA_CADASTRO);
	}

	/**
	 * Set the value related to the column: dt_cadastro
	 * @param dataCadastro the dt_cadastro value
	 */
	public void setDataCadastro (java.util.Date dataCadastro) {
//        java.util.Date dataCadastroOld = this.dataCadastro;
		this.dataCadastro = dataCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCadastro", dataCadastroOld, dataCadastro);
	}



	/**
	 * Return the value associated with the column: observacao
	 */
	public java.lang.String getObservacao () {
		return getPropertyValue(this, observacao, PROP_OBSERVACAO);
	}

	/**
	 * Set the value related to the column: observacao
	 * @param observacao the observacao value
	 */
	public void setObservacao (java.lang.String observacao) {
//        java.lang.String observacaoOld = this.observacao;
		this.observacao = observacao;
//        this.getPropertyChangeSupport().firePropertyChange ("observacao", observacaoOld, observacao);
	}



	/**
	 * Return the value associated with the column: dt_registro
	 */
	public java.util.Date getDataRegistro () {
		return getPropertyValue(this, dataRegistro, PROP_DATA_REGISTRO);
	}

	/**
	 * Set the value related to the column: dt_registro
	 * @param dataRegistro the dt_registro value
	 */
	public void setDataRegistro (java.util.Date dataRegistro) {
//        java.util.Date dataRegistroOld = this.dataRegistro;
		this.dataRegistro = dataRegistro;
//        this.getPropertyChangeSupport().firePropertyChange ("dataRegistro", dataRegistroOld, dataRegistro);
	}



	/**
	 * Return the value associated with the column: dt_encerramento
	 */
	public java.util.Date getDataEncerramento () {
		return getPropertyValue(this, dataEncerramento, PROP_DATA_ENCERRAMENTO);
	}

	/**
	 * Set the value related to the column: dt_encerramento
	 * @param dataEncerramento the dt_encerramento value
	 */
	public void setDataEncerramento (java.util.Date dataEncerramento) {
//        java.util.Date dataEncerramentoOld = this.dataEncerramento;
		this.dataEncerramento = dataEncerramento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataEncerramento", dataEncerramentoOld, dataEncerramento);
	}



	/**
	 * Return the value associated with the column: dt_conclusao
	 */
	public java.util.Date getDataConclusao () {
		return getPropertyValue(this, dataConclusao, PROP_DATA_CONCLUSAO);
	}

	/**
	 * Set the value related to the column: dt_conclusao
	 * @param dataConclusao the dt_conclusao value
	 */
	public void setDataConclusao (java.util.Date dataConclusao) {
//        java.util.Date dataConclusaoOld = this.dataConclusao;
		this.dataConclusao = dataConclusao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataConclusao", dataConclusaoOld, dataConclusao);
	}



	/**
	 * Return the value associated with the column: status
	 */
	public java.lang.Long getStatus () {
		return getPropertyValue(this, status, PROP_STATUS);
	}

	/**
	 * Set the value related to the column: status
	 * @param status the status value
	 */
	public void setStatus (java.lang.Long status) {
//        java.lang.Long statusOld = this.status;
		this.status = status;
//        this.getPropertyChangeSupport().firePropertyChange ("status", statusOld, status);
	}



	/**
	 * Return the value associated with the column: dt_cancelamento
	 */
	public java.util.Date getDataCancelamento () {
		return getPropertyValue(this, dataCancelamento, PROP_DATA_CANCELAMENTO);
	}

	/**
	 * Set the value related to the column: dt_cancelamento
	 * @param dataCancelamento the dt_cancelamento value
	 */
	public void setDataCancelamento (java.util.Date dataCancelamento) {
//        java.util.Date dataCancelamentoOld = this.dataCancelamento;
		this.dataCancelamento = dataCancelamento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCancelamento", dataCancelamentoOld, dataCancelamento);
	}



	/**
	 * Return the value associated with the column: mot_cancelamento
	 */
	public java.lang.String getMotivoCancelamento () {
		return getPropertyValue(this, motivoCancelamento, PROP_MOTIVO_CANCELAMENTO);
	}

	/**
	 * Set the value related to the column: mot_cancelamento
	 * @param motivoCancelamento the mot_cancelamento value
	 */
	public void setMotivoCancelamento (java.lang.String motivoCancelamento) {
//        java.lang.String motivoCancelamentoOld = this.motivoCancelamento;
		this.motivoCancelamento = motivoCancelamento;
//        this.getPropertyChangeSupport().firePropertyChange ("motivoCancelamento", motivoCancelamentoOld, motivoCancelamento);
	}



	/**
	 * Return the value associated with the column: origem
	 */
	public java.lang.Long getOrigem () {
		return getPropertyValue(this, origem, PROP_ORIGEM);
	}

	/**
	 * Set the value related to the column: origem
	 * @param origem the origem value
	 */
	public void setOrigem (java.lang.Long origem) {
//        java.lang.Long origemOld = this.origem;
		this.origem = origem;
//        this.getPropertyChangeSupport().firePropertyChange ("origem", origemOld, origem);
	}



	/**
	 * Return the value associated with the column: cd_registro_agravo_integracao
	 */
	public java.lang.Long getCodigoRegistroAgravoIntegracao () {
		return getPropertyValue(this, codigoRegistroAgravoIntegracao, PROP_CODIGO_REGISTRO_AGRAVO_INTEGRACAO);
	}

	/**
	 * Set the value related to the column: cd_registro_agravo_integracao
	 * @param codigoRegistroAgravoIntegracao the cd_registro_agravo_integracao value
	 */
	public void setCodigoRegistroAgravoIntegracao (java.lang.Long codigoRegistroAgravoIntegracao) {
//        java.lang.Long codigoRegistroAgravoIntegracaoOld = this.codigoRegistroAgravoIntegracao;
		this.codigoRegistroAgravoIntegracao = codigoRegistroAgravoIntegracao;
//        this.getPropertyChangeSupport().firePropertyChange ("codigoRegistroAgravoIntegracao", codigoRegistroAgravoIntegracaoOld, codigoRegistroAgravoIntegracao);
	}



	/**
	 * Return the value associated with the column: ds_empresa_integracao
	 */
	public java.lang.String getDescricaoEmpresaIntegracao () {
		return getPropertyValue(this, descricaoEmpresaIntegracao, PROP_DESCRICAO_EMPRESA_INTEGRACAO);
	}

	/**
	 * Set the value related to the column: ds_empresa_integracao
	 * @param descricaoEmpresaIntegracao the ds_empresa_integracao value
	 */
	public void setDescricaoEmpresaIntegracao (java.lang.String descricaoEmpresaIntegracao) {
//        java.lang.String descricaoEmpresaIntegracaoOld = this.descricaoEmpresaIntegracao;
		this.descricaoEmpresaIntegracao = descricaoEmpresaIntegracao;
//        this.getPropertyChangeSupport().firePropertyChange ("descricaoEmpresaIntegracao", descricaoEmpresaIntegracaoOld, descricaoEmpresaIntegracao);
	}



	/**
	 * Return the value associated with the column: ds_profissional_integracao
	 */
	public java.lang.String getDescricaoProfissionalIntegracao () {
		return getPropertyValue(this, descricaoProfissionalIntegracao, PROP_DESCRICAO_PROFISSIONAL_INTEGRACAO);
	}

	/**
	 * Set the value related to the column: ds_profissional_integracao
	 * @param descricaoProfissionalIntegracao the ds_profissional_integracao value
	 */
	public void setDescricaoProfissionalIntegracao (java.lang.String descricaoProfissionalIntegracao) {
//        java.lang.String descricaoProfissionalIntegracaoOld = this.descricaoProfissionalIntegracao;
		this.descricaoProfissionalIntegracao = descricaoProfissionalIntegracao;
//        this.getPropertyChangeSupport().firePropertyChange ("descricaoProfissionalIntegracao", descricaoProfissionalIntegracaoOld, descricaoProfissionalIntegracao);
	}



	/**
	 * Return the value associated with the column: dt_integracao
	 */
	public java.util.Date getDataIntegracao () {
		return getPropertyValue(this, dataIntegracao, PROP_DATA_INTEGRACAO);
	}

	/**
	 * Set the value related to the column: dt_integracao
	 * @param dataIntegracao the dt_integracao value
	 */
	public void setDataIntegracao (java.util.Date dataIntegracao) {
//        java.util.Date dataIntegracaoOld = this.dataIntegracao;
		this.dataIntegracao = dataIntegracao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataIntegracao", dataIntegracaoOld, dataIntegracao);
	}



	/**
	 * Return the value associated with the column: dt_primeiros_sintomas
	 */
	public java.util.Date getDataPrimeirosSintomas () {
		return getPropertyValue(this, dataPrimeirosSintomas, PROP_DATA_PRIMEIROS_SINTOMAS);
	}

	/**
	 * Set the value related to the column: dt_primeiros_sintomas
	 * @param dataPrimeirosSintomas the dt_primeiros_sintomas value
	 */
	public void setDataPrimeirosSintomas (java.util.Date dataPrimeirosSintomas) {
//        java.util.Date dataPrimeirosSintomasOld = this.dataPrimeirosSintomas;
		this.dataPrimeirosSintomas = dataPrimeirosSintomas;
//        this.getPropertyChangeSupport().firePropertyChange ("dataPrimeirosSintomas", dataPrimeirosSintomasOld, dataPrimeirosSintomas);
	}



	/**
	 * Return the value associated with the column: flag_sinalizacao_confirmacao
	 */
	public java.lang.Long getFlagSinalizacaoConfirmacao () {
		return getPropertyValue(this, flagSinalizacaoConfirmacao, PROP_FLAG_SINALIZACAO_CONFIRMACAO);
	}

	/**
	 * Set the value related to the column: flag_sinalizacao_confirmacao
	 * @param flagSinalizacaoConfirmacao the flag_sinalizacao_confirmacao value
	 */
	public void setFlagSinalizacaoConfirmacao (java.lang.Long flagSinalizacaoConfirmacao) {
//        java.lang.Long flagSinalizacaoConfirmacaoOld = this.flagSinalizacaoConfirmacao;
		this.flagSinalizacaoConfirmacao = flagSinalizacaoConfirmacao;
//        this.getPropertyChangeSupport().firePropertyChange ("flagSinalizacaoConfirmacao", flagSinalizacaoConfirmacaoOld, flagSinalizacaoConfirmacao);
	}



	/**
	 * Return the value associated with the column: latitude
	 */
	public java.lang.Double getLatitude () {
		return getPropertyValue(this, latitude, PROP_LATITUDE);
	}

	/**
	 * Set the value related to the column: latitude
	 * @param latitude the latitude value
	 */
	public void setLatitude (java.lang.Double latitude) {
//        java.lang.Double latitudeOld = this.latitude;
		this.latitude = latitude;
//        this.getPropertyChangeSupport().firePropertyChange ("latitude", latitudeOld, latitude);
	}



	/**
	 * Return the value associated with the column: longitude
	 */
	public java.lang.Double getLongitude () {
		return getPropertyValue(this, longitude, PROP_LONGITUDE);
	}

	/**
	 * Set the value related to the column: longitude
	 * @param longitude the longitude value
	 */
	public void setLongitude (java.lang.Double longitude) {
//        java.lang.Double longitudeOld = this.longitude;
		this.longitude = longitude;
//        this.getPropertyChangeSupport().firePropertyChange ("longitude", longitudeOld, longitude);
	}



	/**
	 * Return the value associated with the column: cod_notificacao
	 */
	public java.lang.Long getCodigoNotificacao () {
		return getPropertyValue(this, codigoNotificacao, PROP_CODIGO_NOTIFICACAO);
	}

	/**
	 * Set the value related to the column: cod_notificacao
	 * @param codigoNotificacao the cod_notificacao value
	 */
	public void setCodigoNotificacao (java.lang.Long codigoNotificacao) {
//        java.lang.Long codigoNotificacaoOld = this.codigoNotificacao;
		this.codigoNotificacao = codigoNotificacao;
//        this.getPropertyChangeSupport().firePropertyChange ("codigoNotificacao", codigoNotificacaoOld, codigoNotificacao);
	}



	/**
	 * Return the value associated with the column: escolaridade
	 */
	public java.lang.Long getEscolaridade () {
		return getPropertyValue(this, escolaridade, PROP_ESCOLARIDADE);
	}

	/**
	 * Set the value related to the column: escolaridade
	 * @param escolaridade the escolaridade value
	 */
	public void setEscolaridade (java.lang.Long escolaridade) {
//        java.lang.Long escolaridadeOld = this.escolaridade;
		this.escolaridade = escolaridade;
//        this.getPropertyChangeSupport().firePropertyChange ("escolaridade", escolaridadeOld, escolaridade);
	}



	/**
	 * Return the value associated with the column: idade_gestacional
	 */
	public java.lang.Long getIdadeGestacional () {
		return getPropertyValue(this, idadeGestacional, PROP_IDADE_GESTACIONAL);
	}

	/**
	 * Set the value related to the column: idade_gestacional
	 * @param idadeGestacional the idade_gestacional value
	 */
	public void setIdadeGestacional (java.lang.Long idadeGestacional) {
//        java.lang.Long idadeGestacionalOld = this.idadeGestacional;
		this.idadeGestacional = idadeGestacional;
//        this.getPropertyChangeSupport().firePropertyChange ("idadeGestacional", idadeGestacionalOld, idadeGestacional);
	}



	/**
	 * Return the value associated with the column: escolaridade_equivalencia
	 */
	public java.lang.Long getEscolaridadeEquivalencia () {
		return getPropertyValue(this, escolaridadeEquivalencia, PROP_ESCOLARIDADE_EQUIVALENCIA);
	}

	/**
	 * Set the value related to the column: escolaridade_equivalencia
	 * @param escolaridadeEquivalencia the escolaridade_equivalencia value
	 */
	public void setEscolaridadeEquivalencia (java.lang.Long escolaridadeEquivalencia) {
//        java.lang.Long escolaridadeEquivalenciaOld = this.escolaridadeEquivalencia;
		this.escolaridadeEquivalencia = escolaridadeEquivalencia;
//        this.getPropertyChangeSupport().firePropertyChange ("escolaridadeEquivalencia", escolaridadeEquivalenciaOld, escolaridadeEquivalencia);
	}



	/**
	 * Return the value associated with the column: nr_atendimento
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.Atendimento getAtendimento () {
		return getPropertyValue(this, atendimento, PROP_ATENDIMENTO);
	}

	/**
	 * Set the value related to the column: nr_atendimento
	 * @param atendimento the nr_atendimento value
	 */
	public void setAtendimento (br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimento) {
//        br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimentoOld = this.atendimento;
		this.atendimento = atendimento;
//        this.getPropertyChangeSupport().firePropertyChange ("atendimento", atendimentoOld, atendimento);
	}



	/**
	 * Return the value associated with the column: cd_usu_cadsus
	 */
	public br.com.ksisolucoes.vo.cadsus.UsuarioCadsus getUsuarioCadsus () {
		return getPropertyValue(this, usuarioCadsus, PROP_USUARIO_CADSUS);
	}

	/**
	 * Set the value related to the column: cd_usu_cadsus
	 * @param usuarioCadsus the cd_usu_cadsus value
	 */
	public void setUsuarioCadsus (br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus) {
//        br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsusOld = this.usuarioCadsus;
		this.usuarioCadsus = usuarioCadsus;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCadsus", usuarioCadsusOld, usuarioCadsus);
	}



	/**
	 * Return the value associated with the column: cd_cid
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.Cid getCid () {
		return getPropertyValue(this, cid, PROP_CID);
	}

	/**
	 * Set the value related to the column: cd_cid
	 * @param cid the cd_cid value
	 */
	public void setCid (br.com.ksisolucoes.vo.prontuario.basico.Cid cid) {
//        br.com.ksisolucoes.vo.prontuario.basico.Cid cidOld = this.cid;
		this.cid = cid;
//        this.getPropertyChangeSupport().firePropertyChange ("cid", cidOld, cid);
	}



	/**
	 * Return the value associated with the column: cd_profissional
	 */
	public br.com.ksisolucoes.vo.cadsus.Profissional getProfissional () {
		return getPropertyValue(this, profissional, PROP_PROFISSIONAL);
	}

	/**
	 * Set the value related to the column: cd_profissional
	 * @param profissional the cd_profissional value
	 */
	public void setProfissional (br.com.ksisolucoes.vo.cadsus.Profissional profissional) {
//        br.com.ksisolucoes.vo.cadsus.Profissional profissionalOld = this.profissional;
		this.profissional = profissional;
//        this.getPropertyChangeSupport().firePropertyChange ("profissional", profissionalOld, profissional);
	}



	/**
	 * Return the value associated with the column: cd_profissional_investigacao
	 */
	public br.com.ksisolucoes.vo.cadsus.Profissional getProfissionalInvestigacao () {
		return getPropertyValue(this, profissionalInvestigacao, PROP_PROFISSIONAL_INVESTIGACAO);
	}

	/**
	 * Set the value related to the column: cd_profissional_investigacao
	 * @param profissionalInvestigacao the cd_profissional_investigacao value
	 */
	public void setProfissionalInvestigacao (br.com.ksisolucoes.vo.cadsus.Profissional profissionalInvestigacao) {
//        br.com.ksisolucoes.vo.cadsus.Profissional profissionalInvestigacaoOld = this.profissionalInvestigacao;
		this.profissionalInvestigacao = profissionalInvestigacao;
//        this.getPropertyChangeSupport().firePropertyChange ("profissionalInvestigacao", profissionalInvestigacaoOld, profissionalInvestigacao);
	}



	/**
	 * Return the value associated with the column: empresa
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getEmpresa () {
		return getPropertyValue(this, empresa, PROP_EMPRESA);
	}

	/**
	 * Set the value related to the column: empresa
	 * @param empresa the empresa value
	 */
	public void setEmpresa (br.com.ksisolucoes.vo.basico.Empresa empresa) {
//        br.com.ksisolucoes.vo.basico.Empresa empresaOld = this.empresa;
		this.empresa = empresa;
//        this.getPropertyChangeSupport().firePropertyChange ("empresa", empresaOld, empresa);
	}



	/**
	 * Return the value associated with the column: cd_unidade_profissional_investigacao
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getUnidadeProfissionalInvestigacao () {
		return getPropertyValue(this, unidadeProfissionalInvestigacao, PROP_UNIDADE_PROFISSIONAL_INVESTIGACAO);
	}

	/**
	 * Set the value related to the column: cd_unidade_profissional_investigacao
	 * @param unidadeProfissionalInvestigacao the cd_unidade_profissional_investigacao value
	 */
	public void setUnidadeProfissionalInvestigacao (br.com.ksisolucoes.vo.basico.Empresa unidadeProfissionalInvestigacao) {
//        br.com.ksisolucoes.vo.basico.Empresa unidadeProfissionalInvestigacaoOld = this.unidadeProfissionalInvestigacao;
		this.unidadeProfissionalInvestigacao = unidadeProfissionalInvestigacao;
//        this.getPropertyChangeSupport().firePropertyChange ("unidadeProfissionalInvestigacao", unidadeProfissionalInvestigacaoOld, unidadeProfissionalInvestigacao);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioCadastro () {
		return getPropertyValue(this, usuarioCadastro, PROP_USUARIO_CADASTRO);
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuarioCadastro the cd_usuario value
	 */
	public void setUsuarioCadastro (br.com.ksisolucoes.vo.controle.Usuario usuarioCadastro) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioCadastroOld = this.usuarioCadastro;
		this.usuarioCadastro = usuarioCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCadastro", usuarioCadastroOld, usuarioCadastro);
	}



	/**
	 * Return the value associated with the column: cd_usuario_can
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioCancelamento () {
		return getPropertyValue(this, usuarioCancelamento, PROP_USUARIO_CANCELAMENTO);
	}

	/**
	 * Set the value related to the column: cd_usuario_can
	 * @param usuarioCancelamento the cd_usuario_can value
	 */
	public void setUsuarioCancelamento (br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamento) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamentoOld = this.usuarioCancelamento;
		this.usuarioCancelamento = usuarioCancelamento;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCancelamento", usuarioCancelamentoOld, usuarioCancelamento);
	}



	/**
	 * Return the value associated with the column: cd_endereco
	 */
	public br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus getEndereco () {
		return getPropertyValue(this, endereco, PROP_ENDERECO);
	}

	/**
	 * Set the value related to the column: cd_endereco
	 * @param endereco the cd_endereco value
	 */
	public void setEndereco (br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus endereco) {
//        br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus enderecoOld = this.endereco;
		this.endereco = endereco;
//        this.getPropertyChangeSupport().firePropertyChange ("endereco", enderecoOld, endereco);
	}



	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo)) return false;
		else {
			br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravo = (br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo) obj;
			if (null == this.getCodigo() || null == registroAgravo.getCodigo()) return false;
			else return (this.getCodigo().equals(registroAgravo.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }

    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}