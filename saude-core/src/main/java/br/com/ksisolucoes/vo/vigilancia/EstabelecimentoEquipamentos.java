package br.com.ksisolucoes.vo.vigilancia;

import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.vigilancia.base.BaseEstabelecimentoEquipamentos;

import java.io.Serializable;



public class EstabelecimentoEquipamentos extends BaseEstabelecimentoEquipamentos implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public EstabelecimentoEquipamentos () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public EstabelecimentoEquipamentos (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public EstabelecimentoEquipamentos (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.Estabelecimento estabelecimento,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		java.util.Date dataCadastro) {

		super (
			codigo,
			estabelecimento,
			usuario,
			dataCadastro);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}