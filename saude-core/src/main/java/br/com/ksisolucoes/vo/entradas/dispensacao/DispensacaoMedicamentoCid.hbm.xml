<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.entradas.dispensacao"  >
    <class name="DispensacaoMedicamentoCid" table="dispensacao_medicamento_cid">
        
         <id
            name="codigo"
            type="java.lang.Long" 
            column="cd_dis_med_cid"
        >
             <generator class="sequence">
                 <param name="sequence">seq_dispensacao_medicamento_cid</param>
             </generator>
        </id> 
        
        <version column="version" name="version" type="long" />
        
        <many-to-one
            class="DispensacaoMedicamento"
            name="dispensacaoMedicamento"
            column="nr_dispensacao"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.prontuario.basico.Cid"
                name="cid"
                column="cd_cid"
        />

    </class>
</hibernate-mapping>
