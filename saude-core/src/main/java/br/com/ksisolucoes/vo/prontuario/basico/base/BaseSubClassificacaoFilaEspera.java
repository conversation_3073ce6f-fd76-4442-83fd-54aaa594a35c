package br.com.ksisolucoes.vo.prontuario.basico.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the subclassificacao_fila_espera table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="subclassificacao_fila_espera"
 */

public abstract class BaseSubClassificacaoFilaEspera extends BaseRootVO implements Serializable {

	public static String REF = "SubClassificacaoFilaEspera";
	public static final String PROP_DESCRICAO = "descricao";
	public static final String PROP_USUARIO = "usuario";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_DATA_CADASTRO = "dataCadastro";
	public static final String PROP_VALOR = "valor";
	public static final String PROP_CLASSIFICACAO_RISCO = "classificacaoRisco";


	// constructors
	public BaseSubClassificacaoFilaEspera () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseSubClassificacaoFilaEspera (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseSubClassificacaoFilaEspera (
		java.lang.Long codigo,
		java.lang.String descricao,
		java.lang.Long valor) {

		this.setCodigo(codigo);
		this.setDescricao(descricao);
		this.setValor(valor);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String descricao;
	private java.lang.Long valor;
	private java.util.Date dataCadastro;

	// many to one
	private br.com.ksisolucoes.vo.controle.Usuario usuario;
	private br.com.ksisolucoes.vo.prontuario.basico.ClassificacaoRisco classificacaoRisco;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_subclassificacao_fila_espera"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: descricao
	 */
	public java.lang.String getDescricao () {
		return getPropertyValue(this, descricao, PROP_DESCRICAO); 
	}

	/**
	 * Set the value related to the column: descricao
	 * @param descricao the descricao value
	 */
	public void setDescricao (java.lang.String descricao) {
//        java.lang.String descricaoOld = this.descricao;
		this.descricao = descricao;
//        this.getPropertyChangeSupport().firePropertyChange ("descricao", descricaoOld, descricao);
	}



	/**
	 * Return the value associated with the column: valor
	 */
	public java.lang.Long getValor () {
		return getPropertyValue(this, valor, PROP_VALOR); 
	}

	/**
	 * Set the value related to the column: valor
	 * @param valor the valor value
	 */
	public void setValor (java.lang.Long valor) {
//        java.lang.Long valorOld = this.valor;
		this.valor = valor;
//        this.getPropertyChangeSupport().firePropertyChange ("valor", valorOld, valor);
	}



	/**
	 * Return the value associated with the column: dt_cadastro
	 */
	public java.util.Date getDataCadastro () {
		return getPropertyValue(this, dataCadastro, PROP_DATA_CADASTRO); 
	}

	/**
	 * Set the value related to the column: dt_cadastro
	 * @param dataCadastro the dt_cadastro value
	 */
	public void setDataCadastro (java.util.Date dataCadastro) {
//        java.util.Date dataCadastroOld = this.dataCadastro;
		this.dataCadastro = dataCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCadastro", dataCadastroOld, dataCadastro);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuario the cd_usuario value
	 */
	public void setUsuario (br.com.ksisolucoes.vo.controle.Usuario usuario) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}



	/**
	 * Return the value associated with the column: cd_classificacao_risco
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.ClassificacaoRisco getClassificacaoRisco () {
		return getPropertyValue(this, classificacaoRisco, PROP_CLASSIFICACAO_RISCO); 
	}

	/**
	 * Set the value related to the column: cd_classificacao_risco
	 * @param classificacaoRisco the cd_classificacao_risco value
	 */
	public void setClassificacaoRisco (br.com.ksisolucoes.vo.prontuario.basico.ClassificacaoRisco classificacaoRisco) {
//        br.com.ksisolucoes.vo.prontuario.basico.ClassificacaoRisco classificacaoRiscoOld = this.classificacaoRisco;
		this.classificacaoRisco = classificacaoRisco;
//        this.getPropertyChangeSupport().firePropertyChange ("classificacaoRisco", classificacaoRiscoOld, classificacaoRisco);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.basico.SubClassificacaoFilaEspera)) return false;
		else {
			br.com.ksisolucoes.vo.prontuario.basico.SubClassificacaoFilaEspera subClassificacaoFilaEspera = (br.com.ksisolucoes.vo.prontuario.basico.SubClassificacaoFilaEspera) obj;
			if (null == this.getCodigo() || null == subClassificacaoFilaEspera.getCodigo()) return false;
			else return (this.getCodigo().equals(subClassificacaoFilaEspera.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}