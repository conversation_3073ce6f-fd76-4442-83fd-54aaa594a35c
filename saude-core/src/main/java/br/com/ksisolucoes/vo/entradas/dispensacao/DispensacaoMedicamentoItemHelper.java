/*
 * DispensacaoMedicamentoItemHelper.java
 *
 * Created on 18 de Setembro de 2006, 09:48
 *
 * To change this template, choose Tools | Template Manager
 * and open the template in the editor.
 */
package br.com.ksisolucoes.vo.entradas.dispensacao;

import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.*;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.parametrogem.IParameterModuleContainer;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.entradas.estoque.ProdutoHelper;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupo;
import br.com.ksisolucoes.vo.prontuario.basico.TipoReceita;
import org.apache.commons.lang.StringUtils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> Graciano
 */
public class DispensacaoMedicamentoItemHelper {

    private static final Long VALIDADE_INSULINA_PADRAO = 42L;
    private static final Long CODIGO_AMPOLA = 33L;
    private static final Long CODIGO_FRASCO = 28L;


    /**
     * Saldo do item de dispensao.<br>
     * QuantidadePrescrita - QuantidadeDispensada
     *
     * @return
     */
    public static Double getSaldo(DispensacaoMedicamentoItem dispensacaoMedicamentoItem) {
//        if ( dispensacaoMedicamentoItem.getId() != null &&
//                isTipoReceitaBasico(dispensacaoMedicamentoItem) && DispensacaoMedicamentoItem.TIPO_RECEITA_CONTINUA_SIM.equals(dispensacaoMedicamentoItem.getId().getDispensacaoMedicamento().getTipoReceitaContinua()) ){
//            return 0D;
//        }else{
        return getSaldo(dispensacaoMedicamentoItem.getQuantidadePrescrita(), dispensacaoMedicamentoItem.getCoalesceQuantidadeDispensada());
//        }
    }

    /**
     * Saldo do item de dispensao.<br>
     * QuantidadePrescrita - QuantidadeDispensada
     *
     * @return
     */
    public static Double getSaldo(Double quantidadePrescrita, Double quantidadeDispensada) {
        return Coalesce.asDouble(quantidadePrescrita) - Coalesce.asDouble(quantidadeDispensada);
    }

    /**
     * Total dispensado do item de dispensao.<br>
     * ProdutoPreco * QuantidadeDispensada
     *
     * @return
     */
    public static Double getTotalDispensado(Double produtoPreco, Double quantidadeDispensada) {
        return Coalesce.asDouble(produtoPreco) * Coalesce.asDouble(quantidadeDispensada);
    }

    /**
     * Retorna a data limite para controle de saldo. Esta data estipula a limite
     * para validade do saldo. Aps esta data, NO deve-se gerar saldo
     * remanescente para as dispensaes.
     *
     * @param dispensacaoMedicamentoItem
     * @return
     */
    public static Date getDataControleSaldo(DispensacaoMedicamentoItem dispensacaoMedicamentoItem) {
        return getDataControleSaldo(dispensacaoMedicamentoItem.getDispensacaoMedicamento().getDataDispensacao(),
                new Double(dispensacaoMedicamentoItem.getCoalesceQuantidadeDispensada() / Coalesce.asDouble(dispensacaoMedicamentoItem.getPosologia(), 1D)).intValue());
    }

    /**
     * Retorna a data limite para controle de saldo. Esta data estipula a limite
     * para validade do saldo. Aps esta data, NO deve-se gerar saldo
     * remanescente para as dispensaes.
     *
     * @param dataReceita
     * @param validadeSaldo
     * @return
     */
    public static Date getDataControleSaldo(Date dataReceita, int validadeSaldo) {
        return Data.addDias(dataReceita, validadeSaldo);
    }

    /**
     * Retorna a validade da receita.
     *
     * @param dataReceita
     * @param validadeReceita
     * @return
     */
    public static Date getDataValidadeReceita(Date dataReceita, int validadeReceita) {
        return Data.addDias(dataReceita, validadeReceita);
    }

    /**
     * Retorna a validade da receita.
     *
     * @param dataReceita
     * @param validadeReceita
     * @return
     */
    public static Date getDataValidadeReceita(Date dataReceita, Long validadeReceita) {
        return Data.addDias(dataReceita, validadeReceita.intValue());
    }

    /**
     * Retorna a data para a prxima dispensao com base no item informado.
     *
     * @param dispensacaoMedicamentoItem
     * @param usuarioCadsusDestino
     * @param dataDispensacao
     * @return
     */
    public static Date getDataProximaDispensacao(DispensacaoMedicamentoItem dispensacaoMedicamentoItem, UsuarioCadsus usuarioCadsusDestino, Date dataDispensacao) throws ValidacaoException {
        return getDataProximaDispensacao(dispensacaoMedicamentoItem, usuarioCadsusDestino, dataDispensacao, dispensacaoMedicamentoItem.getQuantidadeDispensada(), false);
    }

    /**
     * Retorna a data para a prxima dispensao com base no item informado.
     *
     * @param dispensacaoMedicamentoItem
     * @param usuarioCadsusDestino
     * @param dataDispensacao
     * @return
     */
    public static Date getDataProximaDispensacao(DispensacaoMedicamentoItem dispensacaoMedicamentoItem, UsuarioCadsus usuarioCadsusDestino, Date dataDispensacao, boolean devolucao) throws ValidacaoException {
        return getDataProximaDispensacao(dispensacaoMedicamentoItem, usuarioCadsusDestino, dataDispensacao, dispensacaoMedicamentoItem.getQuantidadeDispensada(), devolucao);
    }

    public static Date getDataProximaDispensacao(DispensacaoMedicamentoItem dispensacaoMedicamentoItem, UsuarioCadsus usuarioCadsusDestino, Date dataDispensacao, Double qtdadeDisp, boolean devolucao) throws ValidacaoException {
        try {
            if (dispensacaoMedicamentoItem.getQuantidadeDevolvida() != null) {
                qtdadeDisp -= dispensacaoMedicamentoItem.getQuantidadeDevolvida();
            }
            Date maxDataProximaDispensacao = null;
            List<DispensacaoMedicamentoItem> listaDispensacao = LoadManager.getInstance(DispensacaoMedicamentoItem.class)
                    .addProperty(DispensacaoMedicamentoItem.PROP_QUANTIDADE_DEVOLVIDA)
                    .addProperty(DispensacaoMedicamentoItem.PROP_QUANTIDADE_DISPENSADA)
                    .addProperty(DispensacaoMedicamentoItem.PROP_DATA_PROXIMA_DISPENSACAO)
                    .addParameter(new QueryCustom.QueryCustomParameter(DispensacaoMedicamentoItem.PROP_USUARIO_CADSUS_DESTINO, usuarioCadsusDestino))
                    .addParameter(new QueryCustom.QueryCustomParameter(DispensacaoMedicamentoItem.PROP_PRODUTO, dispensacaoMedicamentoItem.getProduto()))
                    .addParameter(new QueryCustom.QueryCustomParameter(DispensacaoMedicamentoItem.PROP_DATA_PROXIMA_DISPENSACAO, BuilderQueryCustom.QueryParameter.IS_NOT_NULL))
                    .addSorter(new QueryCustom.QueryCustomSorter(DispensacaoMedicamentoItem.PROP_DATA_PROXIMA_DISPENSACAO, BuilderQueryCustom.QuerySorter.DECRESCENTE))
                    .start().getList();

            for (DispensacaoMedicamentoItem medicamentoItem : listaDispensacao) {
                if (Coalesce.asDouble(medicamentoItem.getQuantidadeDevolvida(), 0D) < medicamentoItem.getQuantidadeDispensada()) {
                    maxDataProximaDispensacao = medicamentoItem.getDataProximaDispensacao();
                    break;
                }
            }

            maxDataProximaDispensacao = validaDevolucaoMedicamento(dispensacaoMedicamentoItem, maxDataProximaDispensacao, usuarioCadsusDestino);

            Date dataBaseProximaDispensacao = null;
            if (maxDataProximaDispensacao == null || dataDispensacao.after(maxDataProximaDispensacao)) {
                dataBaseProximaDispensacao = dataDispensacao;
            } else {
                Date dataBase = Data.addDias(dataDispensacao, ((Long) BOFactory.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("diasToleranciaParaDispensacao")).intValue());
                if (dataBase.compareTo(maxDataProximaDispensacao) >= 0 || devolucao) {
                    dataBaseProximaDispensacao = maxDataProximaDispensacao;
                } else {
                    List<LiberacaoReceita> liberacoes = LoadManager.getInstance(LiberacaoReceita.class)
                            .addParameter(new QueryCustom.QueryCustomParameter(LiberacaoReceita.PROP_PRODUTO, dispensacaoMedicamentoItem.getProduto()))
                            .addParameter(new QueryCustom.QueryCustomParameter(LiberacaoReceita.PROP_USUARIO_CADSUS, usuarioCadsusDestino))
                            .addParameter(new QueryCustom.QueryCustomParameter(LiberacaoReceita.PROP_STATUS, LiberacaoReceita.STATUS_ABERTO))
                            .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(LiberacaoReceita.PROP_EMPRESA, Empresa.PROP_CODIGO), SessaoAplicacaoImp.getInstance().getCodigoEmpresa()))
                            .start().getList();
                    if (liberacoes.isEmpty() && dispensacaoMedicamentoItem.getQuantidadeDispensada() > dispensacaoMedicamentoItem.getQuantidadePrescrita()) {
                        throw new ValidacaoException(Bundle.getStringApplication("msg_Medicamento_Ja_Dispensado_Paciente_Data_Proxima_Dispensacao",
                                dispensacaoMedicamentoItem.getProduto().getDescricaoFormatado(), new SimpleDateFormat("dd/MM/yyyy").format(maxDataProximaDispensacao)));
                    }

                    final Long tipoLiberacao = !liberacoes.isEmpty() ? liberacoes.get(0).getTipoLiberacao() : LiberacaoReceita.LIBERACAO_NORMAL;

                    if (LiberacaoReceita.LIBERACAO_ACERTO.equals(tipoLiberacao)) {
                        dataBaseProximaDispensacao = dataDispensacao;
                    } else {
                        dataBaseProximaDispensacao = maxDataProximaDispensacao;
                    }
                }
            }

            Long quantidadeDias = getQuantidadeDias(dispensacaoMedicamentoItem.getProduto(), qtdadeDisp, Coalesce.asDouble(dispensacaoMedicamentoItem.getPosologia()), dispensacaoMedicamentoItem.getProduto().getQtdadeMgMl(), dispensacaoMedicamentoItem.getTipoUso());
            if (DispensacaoMedicamentoItem.DISPENSACAO_SEM_CONTROLE.equals(dispensacaoMedicamentoItem.getProduto().getFlagDispensacaoEspecial()) || devolucao) {
                return Data.addDias(dataDispensacao, quantidadeDias.intValue());
            } else {
                return Data.addDias(dataBaseProximaDispensacao, quantidadeDias.intValue()-1);
            }
        } catch (DAOException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
        return null;
    }

    private static Date validaDevolucaoMedicamento(DispensacaoMedicamentoItem dispensacaoMedicamentoItemSendoDevolvida, Date maxDataProximaDispensacao, UsuarioCadsus usuarioCadsusDestino) {
        if (maxDataProximaDispensacao != null) {
            if (maxDataProximaDispensacao.equals(dispensacaoMedicamentoItemSendoDevolvida.getDataProximaDispensacao())) {
                List<DispensacaoMedicamentoItem> listaDispensacao = LoadManager.getInstance(DispensacaoMedicamentoItem.class)
                        .addParameter(new QueryCustom.QueryCustomParameter(DispensacaoMedicamentoItem.PROP_USUARIO_CADSUS_DESTINO, usuarioCadsusDestino))
                        .addParameter(new QueryCustom.QueryCustomParameter(DispensacaoMedicamentoItem.PROP_PRODUTO, dispensacaoMedicamentoItemSendoDevolvida.getProduto()))
                        .addParameter(new QueryCustom.QueryCustomParameter(DispensacaoMedicamentoItem.PROP_CODIGO, BuilderQueryCustom.QueryParameter.MENOR, dispensacaoMedicamentoItemSendoDevolvida.getCodigo()))
                        .addSorter(new QueryCustom.QueryCustomSorter(DispensacaoMedicamentoItem.PROP_DATA_PROXIMA_DISPENSACAO, BuilderQueryCustom.QuerySorter.DECRESCENTE))
                        .start().getList();

                for (DispensacaoMedicamentoItem medicamentoItem : listaDispensacao) {
                    if (Coalesce.asDouble(medicamentoItem.getQuantidadeDevolvida(), 0D) < medicamentoItem.getQuantidadeDispensada()) {
                        maxDataProximaDispensacao = medicamentoItem.getDataProximaDispensacao();
                        break;
                    }
                }
            }
        }
        return maxDataProximaDispensacao;
    }


    public static Long getQuantidadeDiasAntibioticos(DispensacaoMedicamentoItem dispensacaoMedicamentoItem, TipoReceita tipoReceita) {
        Double qtdadeDia = dispensacaoMedicamentoItem.getPosologia();
        Long qtdPrescrita = getQuantidadeDias(dispensacaoMedicamentoItem.getProduto(), Coalesce.asDouble(dispensacaoMedicamentoItem.getQuantidadePrescrita()), qtdadeDia, dispensacaoMedicamentoItem.getProduto().getQtdadeMgMl(), dispensacaoMedicamentoItem.getTipoUso());
        if (Coalesce.asLong(qtdPrescrita) > tipoReceita.getDiasValidadeAntibiotico()) {
            return tipoReceita.getDiasMaximoValidadeAntibiotico();
        } else {
            return tipoReceita.getDiasValidadeAntibiotico();
        }
    }

    public static Long getQuantidadeDias(DispensacaoMedicamentoItem dispensacaoMedicamentoItem) {
        Double qtdadeDia = dispensacaoMedicamentoItem.getPosologia();
        Double qtdadeDisp = null;
        if (dispensacaoMedicamentoItem.getQuantidadeDispensar() == 0D) {
            qtdadeDisp = dispensacaoMedicamentoItem.getCoalesceQuantidadeDispensada();
        } else {
            qtdadeDisp = dispensacaoMedicamentoItem.getQuantidadeDispensar();
        }
        return getQuantidadeDias(dispensacaoMedicamentoItem.getProduto(), qtdadeDisp, qtdadeDia, dispensacaoMedicamentoItem.getProduto().getQtdadeMgMl(), dispensacaoMedicamentoItem.getTipoUso());
    }

    public static Long getQuantidadeDias(Produto produto, Double qtdadeDisp, Double qtdadeDia) {
        Double qtdadeMgMlDose = produto.getQtdadeMgMl();
        return getQuantidadeDias(produto, qtdadeDisp, qtdadeDia, qtdadeMgMlDose);
    }

    public static Long getQuantidadeDias(Produto produto, Double qtdadeDisp, Double qtdadeDia, Double qtdadeMgMlDose) {
        return getQuantidadeDias(produto, qtdadeDisp, qtdadeDia, qtdadeMgMlDose, DispensacaoMedicamentoItem.TipoUso.DIA.value());
    }

    public static Long getQuantidadeDias(Produto produto, Double qtdadeDisp, Double qtdadeDia, Double qtdadeMgMlDose, Long tipoUso) {
        Long quantidadeDias = 0L;
        List<Produto> itensInsulina = carregarMedicamentoInsulina(produto);

        if (Coalesce.asDouble(qtdadeMgMlDose) > 0D) {
            if (DispensacaoMedicamentoItem.DISPENSACAO_GOTAS.equals(produto.getFlagDispensacaoEspecial())) {
                quantidadeDias = new Double(qtdadeDisp * qtdadeMgMlDose / (qtdadeDia / 20)).longValue();
            } else if (DispensacaoMedicamentoItem.DISPENSACAO_UI.equals(produto.getFlagDispensacaoEspecial())) {
                quantidadeDias = new Double(qtdadeDisp * qtdadeMgMlDose / (qtdadeDia / 100)).longValue();

                if (isDataDeVencimentoInsulinaFrascoEAmpolaPadraoAtiva() && quantidadeDias > VALIDADE_INSULINA_PADRAO && !itensInsulina.isEmpty() &&
                        (produto.getUnidade().getCodigo().equals(CODIGO_AMPOLA) || produto.getUnidade().getCodigo().equals(CODIGO_FRASCO))) {
                    quantidadeDias = VALIDADE_INSULINA_PADRAO;
                    return quantidadeDias;
                }

            } else {
                quantidadeDias = new Double(qtdadeDisp * qtdadeMgMlDose / qtdadeDia).longValue();
            }
        } else if (DispensacaoMedicamentoItem.DISPENSACAO_SEM_CONTROLE.equals(produto.getFlagDispensacaoEspecial())) {
            try {
                quantidadeDias = ((Long) BOFactory.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("diasToleranciaParaDispensacao")) + 1L;
            } catch (DAOException ex) {
                Loggable.log.error(ex.getMessage(), ex);
            }
        } else if (qtdadeDia != null && qtdadeDia > 0D) {
            quantidadeDias = new Double(Coalesce.asDouble(qtdadeDisp) / Coalesce.asDouble(qtdadeDia)).longValue();
        }

        if (!DispensacaoMedicamentoItem.DISPENSACAO_SEM_CONTROLE.equals(produto.getFlagDispensacaoEspecial())) {
            if (DispensacaoMedicamentoItem.TipoUso.MES.value().equals(tipoUso)) {
                return quantidadeDias * 30;
            } else if (DispensacaoMedicamentoItem.TipoUso.SEMANA.value().equals(tipoUso)) {
                return quantidadeDias * 7;
            } else if (DispensacaoMedicamentoItem.TipoUso.TRIMESTRE.value().equals(tipoUso)) {
                return quantidadeDias * 90;
            } else if (DispensacaoMedicamentoItem.TipoUso.ANUAL.value().equals(tipoUso)) {
                return quantidadeDias * 365;
            }
        }
        return quantidadeDias;
    }

    private static List<Produto> carregarMedicamentoInsulina(Produto produto) {
        return LoadManager.getInstance(Produto.class)
                .addParameter(new QueryCustom.QueryCustomParameter(Produto.PROP_CODIGO, produto.getCodigo()))
                .addParameter(new QueryCustom.QueryCustomParameter(Produto.PROP_DESCRICAO, BuilderQueryCustom.QueryParameter.LIKE, "INSULINA"))
                .start().getList();
    }

    public static boolean isDataDeVencimentoInsulinaFrascoEAmpolaPadraoAtiva() {
        String dataDeValidadeInsulinaPadraoGemAtivo = RepositoryComponentDefault.SIM;
        try {
            dataDeValidadeInsulinaPadraoGemAtivo = BOFactory.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("habilitaVencimentoInsulinaFrascoEAmpola42Dias");
        } catch (DAOException e) {
            Loggable.log.error(e);
        }
        return RepositoryComponentDefault.SIM.equals(dataDeValidadeInsulinaPadraoGemAtivo);
    }

    public static Long getQuantidadePrescrita(Produto produto, Double qtdadeDia, Long validadeReceita) {
        Double qtdadeMgMlDose = produto.getQtdadeMgMl();
        Long quantidadePrescrita = 0L;
        Double quantidade = 0D;

        if (Coalesce.asDouble(qtdadeMgMlDose) > 0D) {
            if (DispensacaoMedicamentoItem.DISPENSACAO_GOTAS.equals(produto.getFlagDispensacaoEspecial())) {
                quantidade = new Double(((qtdadeDia / 20) * validadeReceita / qtdadeMgMlDose));
            } else if (DispensacaoMedicamentoItem.DISPENSACAO_UI.equals(produto.getFlagDispensacaoEspecial())) {
                quantidade = new Double(Valor.round(((qtdadeDia / 100) * validadeReceita / qtdadeMgMlDose), 0));
            } else {
                quantidade = new Double(Valor.round((qtdadeDia * validadeReceita / qtdadeMgMlDose), 0));
            }
        } else {
            quantidade = new Double(Valor.round((qtdadeDia * validadeReceita), 0));
        }

        quantidadePrescrita = quantidade.longValue();

        if ((quantidade % quantidadePrescrita) > 0) {
            quantidadePrescrita += 1;
        }

        return quantidadePrescrita;
    }

    public static Long getQuantidadePrescrita(Produto produto, Double qtdadeDia, Long diasTratamento, Long intervalo) {

        Double qtdadeMgMlDose = Coalesce.asDouble(produto.getQtdadeMgMl());
        Double quantidade = 0D;
        Double quantidadeIntervalo = diasTratamento * (intervalo != null && intervalo > 0 ? 24D / intervalo.doubleValue() : 1D);

        if (qtdadeMgMlDose > 0) {
            if (DispensacaoMedicamentoItem.DISPENSACAO_GOTAS.equals(produto.getFlagDispensacaoEspecial())) {
                quantidade = new Double((qtdadeDia / 20) * quantidadeIntervalo / qtdadeMgMlDose);
            } else if (DispensacaoMedicamentoItem.DISPENSACAO_UI.equals(produto.getFlagDispensacaoEspecial())) {
                quantidade = new Double((qtdadeDia / 100) * quantidadeIntervalo / qtdadeMgMlDose);
            } else {
                quantidade = new Double(qtdadeDia * quantidadeIntervalo / qtdadeMgMlDose);
            }
        } else {
            quantidade = new Double(qtdadeDia * quantidadeIntervalo);
        }

        Long quantidadePrescrita = quantidade.longValue();

        if (quantidade > quantidadePrescrita) {
            quantidadePrescrita += 1;
        }

        return quantidadePrescrita;
    }

    /**
     * Retorna os dias restantes para a prxima dispensao com base no item
     * informado.
     *
     * @param dispensacaoMedicamentoItem
     * @return
     */
    public static Long getDiasProximaDispensacao(DispensacaoMedicamentoItem dispensacaoMedicamentoItem) {
        Date proximaData = dispensacaoMedicamentoItem.getDataProximaDispensacao();
        if (proximaData != null) {
            if (Data.getDataAtual().before(proximaData)) {
                return Data.getDiasDiferenca(Data.getDataAtual(), proximaData);
            } else {
                return 0L;
            }
        }
        return null;
    }

//    /**
//     * Retorna a data para a prxima dispensao.
//     *
//     *
//     * @param dataValidadeReceita
//     * @param validadeRestanteSaldo
//     * @return
//     */
//    public static Date getDataProximaDispensacao( Date dataValidadeReceita, int validadeRestanteSaldo ) {
//        return Data.removeDias( dataValidadeReceita, validadeRestanteSaldo );
//    }

    /**
     * Descrio do status passado por parmetro.
     *
     * @param status status do item
     * @return Descrio do status
     */
    public static String getDescricaoStatus(Long status) {
        return getDescricaoStatus(status, false);
    }

    /**
     * Descrio do status passado por parmetro.
     *
     * @param status status do item
     * @param abrev  se a descricao abreviada ou nao
     * @return Descrio do status
     */
    public static String getDescricaoStatus(Long status, boolean abrev) {
        if (DispensacaoMedicamentoItem.STATUS_NORMAL.equals(status)) {
            if (!abrev) {
                return Bundle.getStringApplication("rotulo_normal");
            } else {
                return Bundle.getStringApplication("rotulo_normal_abv");
            }
        } else if (DispensacaoMedicamentoItem.STATUS_SEM_ESTOQUE.equals(status)) {
            if (!abrev) {
                return Bundle.getStringApplication("rotulo_sem_estoque");
            } else {
                return Bundle.getStringApplication("rotulo_sem_estoque_abv");
            }
        } else if (DispensacaoMedicamentoItem.STATUS_DISPENSADO_PARCIALMENTE.equals(status)) {
            return Bundle.getStringApplication("rotulo_dispensado_parcialmente");
        } else if (DispensacaoMedicamentoItem.STATUS_NAO_DISPENSADO.equals(status)) {
            return Bundle.getStringApplication("rotulo_nao_dispensado");
        } else {
            return "";
        }
    }

    /**
     * Verifica se o tipo de receita do tido basico
     */
    public static boolean isTipoReceitaBasico(DispensacaoMedicamentoItem dispensacaoMedicamentoItem) {
        return TipoReceita.RECEITA_BASICA.equals(dispensacaoMedicamentoItem.getDispensacaoMedicamento().getTipoReceita());
    }

    public static boolean isDentroPrazoValidade(DispensacaoMedicamentoItem dispensacaoMedicamentoItem) {
        if (dispensacaoMedicamentoItem.getDataValidadeReceita().getTime() == Data.adjustRangeHour(Data.getDataAtual()).getDataInicial().getTime()) {
            return true;
        }
        return dispensacaoMedicamentoItem.getDataValidadeReceita().after(Data.adjustRangeHour(Data.getDataAtual()).getDataInicial());
    }

    public static String getDescricaoPosologiaFormatado(Double posologia, Long tipoUso, String flagDispensacaoEspecial) {
        if (StringUtils.isNotBlank(flagDispensacaoEspecial) && !DispensacaoMedicamentoItem.DISPENSACAO_SEM_CONTROLE.equals(flagDispensacaoEspecial)) {
            if (posologia != null) {
                String possologia = Coalesce.asString(posologia.toString());
                if (DispensacaoMedicamentoItem.TipoUso.SEMANA.value().equals(tipoUso)) {
                    return possologia + "/" + getDescricaoUnidadePosologia(flagDispensacaoEspecial) + "/" + DispensacaoMedicamentoItem.TipoUso.SEMANA.descricao();
                } else if (DispensacaoMedicamentoItem.TipoUso.MES.value().equals(tipoUso)) {
                    return possologia + "/" + getDescricaoUnidadePosologia(flagDispensacaoEspecial) + "/" + DispensacaoMedicamentoItem.TipoUso.MES.descricao();
                } else if (DispensacaoMedicamentoItem.TipoUso.DIA.value().equals(tipoUso)) {
                    return possologia + "/" + getDescricaoUnidadePosologia(flagDispensacaoEspecial) + "/" + DispensacaoMedicamentoItem.TipoUso.DIA.descricao();
                } else if (DispensacaoMedicamentoItem.TipoUso.TRIMESTRE.value().equals(tipoUso)) {
                    return possologia + "/" + getDescricaoUnidadePosologia(flagDispensacaoEspecial) + "/" + DispensacaoMedicamentoItem.TipoUso.TRIMESTRE.descricao();
                }
                return possologia + "/" + getDescricaoUnidadePosologia(flagDispensacaoEspecial) + "/" + DispensacaoMedicamentoItem.TipoUso.ANUAL.descricao();
            }
        }
        return "";
    }

    public static String getDescricaoUnidadePosologia(Produto produto) {
        return getDescricaoUnidadePosologia(produto.getFlagDispensacaoEspecial());
    }

    public static String getDescricaoUnidadePosologia(String flagDispensacaoEspecial) {
        if (DispensacaoMedicamentoItem.DISPENSACAO_GOTAS.equals(flagDispensacaoEspecial)) {
            return Bundle.getStringApplication("rotulo_gotas");
        } else if (DispensacaoMedicamentoItem.DISPENSACAO_UI.equals(flagDispensacaoEspecial)) {
            return Bundle.getStringApplication("rotulo_ui");
        } else if (DispensacaoMedicamentoItem.DISPENSACAO_ML.equals(flagDispensacaoEspecial)) {
            return Bundle.getStringApplication("rotulo_ml");
        } else if (DispensacaoMedicamentoItem.DISPENSACAO_MCG.equals(flagDispensacaoEspecial)) {
            return Bundle.getStringApplication("rotulo_mcg");
        } else if (DispensacaoMedicamentoItem.DISPENSACAO_SEM_CONTROLE.equals(flagDispensacaoEspecial)) {
            return Bundle.getStringApplication("rotulo_sem_controle");
        } else {
            return Bundle.getStringApplication("rotulo_unidade");
        }
    }

    /**
     * Para medicamentos, conforme parâmetro
     * dataProximaDispensacaoMenosDiasTolerancia, retorna a data da próxima
     * dispensação descontada dos dias de tolerância. Caso a data descontada
     * seja anterior à última data de dispensação, retorna a própria data da
     * última dispensação acrescida em 1 dia.
     *
     * @param dispensacaoMedicamentoItem
     * @return
     */
    public static Date calculaDataProximaDispensacao(DispensacaoMedicamentoItem dispensacaoMedicamentoItem) {
        Date proximaDispensacao = dispensacaoMedicamentoItem.getDataProximaDispensacao();

        try {
            IParameterModuleContainer materiais = BOFactory.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS);

            Long dataProximaDispensacaoMenosDiasTolerancia = materiais.getParametro("dataProximaDispensacaoMenosDiasTolerancia");
            Long diasToleranciaParaPermissao = materiais.getParametro("diasToleranciaParaDispensacao");

            proximaDispensacao = calculaDataProximaDispensacao(
                    dispensacaoMedicamentoItem.getProduto().getSubGrupo(),
                    dispensacaoMedicamentoItem.getDispensacaoMedicamento().getDataDispensacao(),
                    dispensacaoMedicamentoItem.getDataProximaDispensacao(),
                    dispensacaoMedicamentoItem.getDataUltimaDispensacao(),
                    dataProximaDispensacaoMenosDiasTolerancia,
                    diasToleranciaParaPermissao
            );
        } catch (DAOException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }

        return proximaDispensacao;
    }

    public static Date calculaDataProximaDispensacao(SubGrupo subGrupo, Date dataDispensacao, Date dataProximaDispensacao, Date dataUltimaDispensacao, Long dataProximaDispensacaoMenosDiasToleranciaParamGem, Long diasToleranciaParaDispensacaoParamGem) {
        Date proximaDispensacao = dataProximaDispensacao;
        if (RepositoryComponentDefault.SIM_LONG.equals(dataProximaDispensacaoMenosDiasToleranciaParamGem)) {
            if (dataProximaDispensacao != null && ProdutoHelper.isMedicamento(subGrupo)) {
                proximaDispensacao = Data.removeDias(dataProximaDispensacao, diasToleranciaParaDispensacaoParamGem.intValue());
                Date ultimaDispensacao = Coalesce.asDate(dataUltimaDispensacao, dataDispensacao);

                if (dataProximaDispensacao.before(ultimaDispensacao)) {
                    proximaDispensacao = Data.addDias(ultimaDispensacao, 1);
                }
            }
        }

        return proximaDispensacao;
    }

}
