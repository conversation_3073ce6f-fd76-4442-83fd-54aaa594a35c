package br.com.ksisolucoes.vo.integracao.mobile.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the integracao_mobile_item table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="integracao_mobile_item"
 */

public abstract class BaseIntegracaoMobileItem extends BaseRootVO implements Serializable {

	public static String REF = "IntegracaoMobileItem";
	public static final String PROP_STATUS = "status";
	public static final String PROP_NOME_RECURSO = "nomeRecurso";
	public static final String PROP_INTEGRACAO_MOBILE = "integracaoMobile";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_MSG_RECURSO = "msgRecurso";
	public static final String PROP_GERENCIADOR_ARQUIVO = "gerenciadorArquivo";


	// constructors
	public BaseIntegracaoMobileItem () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseIntegracaoMobileItem (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseIntegracaoMobileItem (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.integracao.mobile.IntegracaoMobile integracaoMobile,
		br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo gerenciadorArquivo) {

		this.setCodigo(codigo);
		this.setIntegracaoMobile(integracaoMobile);
		this.setGerenciadorArquivo(gerenciadorArquivo);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String nomeRecurso;
	private java.lang.String msgRecurso;
	private java.lang.Long status;

	// many to one
	private br.com.ksisolucoes.vo.integracao.mobile.IntegracaoMobile integracaoMobile;
	private br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo gerenciadorArquivo;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_integracao_item"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: nome_recurso
	 */
	public java.lang.String getNomeRecurso () {
		return getPropertyValue(this, nomeRecurso, PROP_NOME_RECURSO); 
	}

	/**
	 * Set the value related to the column: nome_recurso
	 * @param nomeRecurso the nome_recurso value
	 */
	public void setNomeRecurso (java.lang.String nomeRecurso) {
//        java.lang.String nomeRecursoOld = this.nomeRecurso;
		this.nomeRecurso = nomeRecurso;
//        this.getPropertyChangeSupport().firePropertyChange ("nomeRecurso", nomeRecursoOld, nomeRecurso);
	}



	/**
	 * Return the value associated with the column: msg_recurso
	 */
	public java.lang.String getMsgRecurso () {
		return getPropertyValue(this, msgRecurso, PROP_MSG_RECURSO); 
	}

	/**
	 * Set the value related to the column: msg_recurso
	 * @param msgRecurso the msg_recurso value
	 */
	public void setMsgRecurso (java.lang.String msgRecurso) {
//        java.lang.String msgRecursoOld = this.msgRecurso;
		this.msgRecurso = msgRecurso;
//        this.getPropertyChangeSupport().firePropertyChange ("msgRecurso", msgRecursoOld, msgRecurso);
	}



	/**
	 * Return the value associated with the column: status
	 */
	public java.lang.Long getStatus () {
		return getPropertyValue(this, status, PROP_STATUS); 
	}

	/**
	 * Set the value related to the column: status
	 * @param status the status value
	 */
	public void setStatus (java.lang.Long status) {
//        java.lang.Long statusOld = this.status;
		this.status = status;
//        this.getPropertyChangeSupport().firePropertyChange ("status", statusOld, status);
	}



	/**
	 * Return the value associated with the column: cd_integracao
	 */
	public br.com.ksisolucoes.vo.integracao.mobile.IntegracaoMobile getIntegracaoMobile () {
		return getPropertyValue(this, integracaoMobile, PROP_INTEGRACAO_MOBILE); 
	}

	/**
	 * Set the value related to the column: cd_integracao
	 * @param integracaoMobile the cd_integracao value
	 */
	public void setIntegracaoMobile (br.com.ksisolucoes.vo.integracao.mobile.IntegracaoMobile integracaoMobile) {
//        br.com.ksisolucoes.vo.integracao.mobile.IntegracaoMobile integracaoMobileOld = this.integracaoMobile;
		this.integracaoMobile = integracaoMobile;
//        this.getPropertyChangeSupport().firePropertyChange ("integracaoMobile", integracaoMobileOld, integracaoMobile);
	}



	/**
	 * Return the value associated with the column: cd_gerenciador_arquivo
	 */
	public br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo getGerenciadorArquivo () {
		return getPropertyValue(this, gerenciadorArquivo, PROP_GERENCIADOR_ARQUIVO); 
	}

	/**
	 * Set the value related to the column: cd_gerenciador_arquivo
	 * @param gerenciadorArquivo the cd_gerenciador_arquivo value
	 */
	public void setGerenciadorArquivo (br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo gerenciadorArquivo) {
//        br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo gerenciadorArquivoOld = this.gerenciadorArquivo;
		this.gerenciadorArquivo = gerenciadorArquivo;
//        this.getPropertyChangeSupport().firePropertyChange ("gerenciadorArquivo", gerenciadorArquivoOld, gerenciadorArquivo);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.integracao.mobile.IntegracaoMobileItem)) return false;
		else {
			br.com.ksisolucoes.vo.integracao.mobile.IntegracaoMobileItem integracaoMobileItem = (br.com.ksisolucoes.vo.integracao.mobile.IntegracaoMobileItem) obj;
			if (null == this.getCodigo() || null == integracaoMobileItem.getCodigo()) return false;
			else return (this.getCodigo().equals(integracaoMobileItem.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}