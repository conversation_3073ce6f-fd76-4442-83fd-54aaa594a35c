package br.com.ksisolucoes.vo.vacina.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the produto_vacina table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="produto_vacina"
 */

public abstract class BaseProdutoVacina extends BaseRootVO implements Serializable {

	public static String REF = "ProdutoVacina";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_QUANTIDADE_DOSE = "quantidadeDose";
	public static final String PROP_TIPO_VACINA = "tipoVacina";
	public static final String PROP_PRODUTO = "produto";
	public static final String PROP_CODIGO_PNI = "codigoPni";


	// constructors
	public BaseProdutoVacina () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseProdutoVacina (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseProdutoVacina (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.entradas.estoque.Produto produto,
		br.com.ksisolucoes.vo.vacina.TipoVacina tipoVacina,
		java.lang.Long quantidadeDose) {

		this.setCodigo(codigo);
		this.setProduto(produto);
		this.setTipoVacina(tipoVacina);
		this.setQuantidadeDose(quantidadeDose);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Long quantidadeDose;
	private java.lang.Long codigoPni;

	// many to one
	private br.com.ksisolucoes.vo.entradas.estoque.Produto produto;
	private br.com.ksisolucoes.vo.vacina.TipoVacina tipoVacina;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_produto_vacina"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: qt_dose
	 */
	public java.lang.Long getQuantidadeDose () {
		return getPropertyValue(this, quantidadeDose, PROP_QUANTIDADE_DOSE); 
	}

	/**
	 * Set the value related to the column: qt_dose
	 * @param quantidadeDose the qt_dose value
	 */
	public void setQuantidadeDose (java.lang.Long quantidadeDose) {
//        java.lang.Long quantidadeDoseOld = this.quantidadeDose;
		this.quantidadeDose = quantidadeDose;
//        this.getPropertyChangeSupport().firePropertyChange ("quantidadeDose", quantidadeDoseOld, quantidadeDose);
	}



	/**
	 * Return the value associated with the column: cod_pni
	 */
	public java.lang.Long getCodigoPni () {
		return getPropertyValue(this, codigoPni, PROP_CODIGO_PNI); 
	}

	/**
	 * Set the value related to the column: cod_pni
	 * @param codigoPni the cod_pni value
	 */
	public void setCodigoPni (java.lang.Long codigoPni) {
//        java.lang.Long codigoPniOld = this.codigoPni;
		this.codigoPni = codigoPni;
//        this.getPropertyChangeSupport().firePropertyChange ("codigoPni", codigoPniOld, codigoPni);
	}



	/**
	 * Return the value associated with the column: cod_pro
	 */
	public br.com.ksisolucoes.vo.entradas.estoque.Produto getProduto () {
		return getPropertyValue(this, produto, PROP_PRODUTO); 
	}

	/**
	 * Set the value related to the column: cod_pro
	 * @param produto the cod_pro value
	 */
	public void setProduto (br.com.ksisolucoes.vo.entradas.estoque.Produto produto) {
//        br.com.ksisolucoes.vo.entradas.estoque.Produto produtoOld = this.produto;
		this.produto = produto;
//        this.getPropertyChangeSupport().firePropertyChange ("produto", produtoOld, produto);
	}



	/**
	 * Return the value associated with the column: cd_vacina
	 */
	public br.com.ksisolucoes.vo.vacina.TipoVacina getTipoVacina () {
		return getPropertyValue(this, tipoVacina, PROP_TIPO_VACINA); 
	}

	/**
	 * Set the value related to the column: cd_vacina
	 * @param tipoVacina the cd_vacina value
	 */
	public void setTipoVacina (br.com.ksisolucoes.vo.vacina.TipoVacina tipoVacina) {
//        br.com.ksisolucoes.vo.vacina.TipoVacina tipoVacinaOld = this.tipoVacina;
		this.tipoVacina = tipoVacina;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoVacina", tipoVacinaOld, tipoVacina);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.vacina.ProdutoVacina)) return false;
		else {
			br.com.ksisolucoes.vo.vacina.ProdutoVacina produtoVacina = (br.com.ksisolucoes.vo.vacina.ProdutoVacina) obj;
			if (null == this.getCodigo() || null == produtoVacina.getCodigo()) return false;
			else return (this.getCodigo().equals(produtoVacina.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}