<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.vigilancia"  >
    <class name="SolicitacaoAgendamentoCVA" table="solicitacao_agendamento_cva">
        <id
            column="cd_sol_ag_cva"
            name="codigo"
            type="java.lang.Long"
        >
            <generator class="assigned" />
        </id> 
        <version column="version" name="version" type="long" />

        <many-to-one
            class="br.com.ksisolucoes.vo.basico.Empresa"
            column="empresa"
            name="empresa"
            not-null="true"
        />
                
        <property
            column="responsavel"
            name="responsavel"
            not-null="true"
            type="java.lang.String"
        />
                
        <property
            column="dt_solicitacao"
            name="dataSolicitacao"
            not-null="true"
            type="timestamp"
        />
                
        <property
            column="dt_cadastro"
            name="dataCadastro"
            not-null="true"
            type="timestamp"
        />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.controle.Usuario"
            column="cd_usuario"
            not-null="true"
            name="usuario"
        />
        
        <property
            column="dt_usuario"
            name="dataUsuario"
            not-null="true"
            type="timestamp"
        />
         
        <property
            column="flag_urgente"
            name="flagUrgente"
            not-null="true"
            type="java.lang.Long"
        />
         
        <property
            column="telefone1"
            name="telefone1"
            not-null="false"
            type="java.lang.String"
        />
        
        <property
            column="telefone2"
            name="telefone2"
            not-null="false"
            type="java.lang.String"
        />
        
        <property
            column="telefone3"
            name="telefone3"
            not-null="false"
            type="java.lang.String"
        />
        
        <property
            column="status"
            name="status"
            not-null="true"
            type="java.lang.Long"
        />
                
        <many-to-one
            class="br.com.ksisolucoes.vo.vigilancia.AtividadeVeterinaria"
            column="cd_atividade_veterinaria"
            name="atividadeVeterinaria"
            not-null="false"
        />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.basico.Empresa"
            column="empresa_agendamento"
            name="empresaAgendamento"
            not-null="false"
        />
        
        <many-to-one
            name="profissionalAgendamento"
            class="br.com.ksisolucoes.vo.cadsus.Profissional"
            column="cd_prof_agendamento"
            not-null="false"
        />
         
        <property
            column="dt_agendamento"
            name="dataAgendamento"
            not-null="true"
            type="timestamp"
        />
        
        <property
            column="ds_motivo_can"
            name="motivo"
            not-null="false"
            type="string"
        />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.controle.Usuario"
            column="cd_usu_can"
            not-null="true"
            name="usuarioCancelamento"
        />

        <property
            column="dt_cancelamento"
            name="dataCancelamento"
            not-null="false"
            type="timestamp"
        />
        
        <property
            column="ds_observacao"
            name="observacao"
            not-null="false"
            type="java.lang.String"
        />                
    </class>
</hibernate-mapping>
