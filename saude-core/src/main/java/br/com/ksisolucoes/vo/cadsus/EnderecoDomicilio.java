package br.com.ksisolucoes.vo.cadsus;

import br.com.celk.integracao.IntegracaoRest;
import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.cadsus.base.BaseEnderecoDomicilio;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.interfaces.PesquisaObjectInterface;

import java.io.Serializable;


@IntegracaoRest
public class EnderecoDomicilio extends BaseEnderecoDomicilio implements CodigoManager, PesquisaObjectInterface {
    private static final long serialVersionUID = 1L;

    /*[CONSTRUCTOR MARKER BEGIN]*/
    public EnderecoDomicilio() {
        super();
    }

    /**
     * Constructor for primary key
     */
    public EnderecoDomicilio(java.lang.Long codigo) {
        super(codigo);
    }

    /**
     * Constructor for required fields
     */
    public EnderecoDomicilio(
            java.lang.Long codigo,
            br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus enderecoUsuarioCadsus,
            br.com.ksisolucoes.vo.controle.Usuario usuario) {

        super(
                codigo,
                enderecoUsuarioCadsus,
                usuario);
    }

    /*[CONSTRUCTOR MARKER END]*/

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    public void setCodigoManager(Serializable srlzbl) {
        setCodigo((Long) srlzbl);
    }

    public String getDescricaoVO() {
        StringBuilder descricao = new StringBuilder();
        if (getEnderecoUsuarioCadsus() == null) {
            return "Endereço não informado";
        }

        String tipoLogradouro = null;
        if (getEnderecoUsuarioCadsus().getTipoLogradouro() != null) {
            tipoLogradouro = getEnderecoUsuarioCadsus().getTipoLogradouro().getDescricao();
        }

        // Tipo de logradouro
        String safeTipo = Coalesce.asString(tipoLogradouro);
        if (!safeTipo.isEmpty()) {
            descricao.append(safeTipo);
        }

        // Logradouro
        String safeLogradouro = Coalesce.asString(getEnderecoUsuarioCadsus().getLogradouro());
        if (!safeLogradouro.isEmpty()) {
            if (descricao.length() > 0) {
                descricao.append(" ");
            }
            descricao.append(safeLogradouro);
        }

        // Número
        String safeNumero = Coalesce.asString(getEnderecoUsuarioCadsus().getNumeroLogradouro());
        if (!safeNumero.isEmpty()) {
            descricao.append(", Nr: ").append(safeNumero);
        }

        // Bairro
        String safeBairro = Coalesce.asString(getEnderecoUsuarioCadsus().getBairro());
        if (!safeBairro.isEmpty()) {
            descricao.append(", ").append(safeBairro);
        }

        return descricao.length() > 0 ? descricao.toString() : "Endereço não informado";

    }

    public String getIdentificador() {
        return getCodigo().toString();
    }

    public String getCaminhoImagem(Long somaCalculoCoelho) {
        NivelGravidade nivelGravidade = NivelGravidade.valeuOf(somaCalculoCoelho);
        if (nivelGravidade != null) {
            return nivelGravidade.caminhoImagem();
        }
        return "";
    }

    public enum NivelGravidade {
        CINZA(0L, "images/css/icons32/ball-grey.png"),
        VERDE(1L, "images/css/icons32/ball-green.png"),
        AMARELO(2L, "images/css/icons32/ball-yellow.png"),
        VERMELHO(3L, "images/css/icons32/ball-red.png"),
        ;
        private Long nivel;
        private String caminhoImagem;

        private NivelGravidade(Long nivel, String caminhoImagem) {
            this.nivel = nivel;
            this.caminhoImagem = caminhoImagem;
        }

        public Long value() {
            return nivel;
        }

        public String caminhoImagem() {
            return caminhoImagem;
        }

        public static NivelGravidade valeuOf(Long value) {
            for (NivelGravidade nivelGravidade : NivelGravidade.values()) {
                if (nivelGravidade.value().equals(value)) {
                    return nivelGravidade;
                }
            }
            return null;
        }
    }

    public enum RiscoCoelho implements IEnum {
        SEM_RISCO(0L, Bundle.getStringApplication("rotulo_sem_risco_definido")),
        RISCO_MENOR(1L, Bundle.getStringApplication("rotulo_risco_menor")),
        RISCO_MEDIO(2L, Bundle.getStringApplication("rotulo_risco_medio")),
        RISCO_MAXIMO(3L, Bundle.getStringApplication("rotulo_risco_maximo")),
        ;

        private final Long value;
        private final String descricao;

        private RiscoCoelho(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static RiscoCoelho valueOf(Long value) {
            for (RiscoCoelho riscoCoelho : RiscoCoelho.values()) {
                if (riscoCoelho.value().equals(value)) {
                    return riscoCoelho;
                }
            }
            return null;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }
    }

    @Override
    public String getNisResponsavel() {
        return lgpdFilterNumberAsText(super.getNisResponsavel());
    }

}