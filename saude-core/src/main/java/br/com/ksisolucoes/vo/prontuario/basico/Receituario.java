package br.com.ksisolucoes.vo.prontuario.basico;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.prontuario.basico.base.BaseReceituario;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.io.Serializable;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown=true)
public class Receituario extends BaseReceituario implements CodigoManager {

    private static final long serialVersionUID = 1L;

    public static final String PROP_DESCRICAO_RECEITA_CONTINUA = "descricaoReceitaContinua";
    public static final String PROP_SITUACAO_FORMATADO = "situacaoFormatado";

    public static final Long PRIMEIRO_TURNO = 1L;
    public static final Long SEGUNDO_TURNO = 2L;
    public static final Long TERCEIRO_TURNO = 4L;

    public enum Situacao implements IEnum<Situacao> {

        PRESCRITO(0L, Bundle.getStringApplication("rotulo_prescrito")),
        NAO_DISPENSADO(1L, Bundle.getStringApplication("rotulo_nao_dispensado")),
        CANCELADO(2L, Bundle.getStringApplication("rotulo_cancelado")),;

        private Long value;
        private String descricao;

        private Situacao(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

        public static Situacao valueOf(Long codigo) {
            for (Situacao situacao : Situacao.values()) {
                if (situacao.value.equals(codigo)) {
                    return situacao;
                }
            }
            return null;
        }
    }

    public enum ImpressaoPrescricao implements IEnum<ImpressaoPrescricao> {

        PENDENTE(0L, Bundle.getStringApplication("rotulo_nao_impresso")),
        IMPRESSO(1L, Bundle.getStringApplication("rotulo_impresso")),;

        private Long value;
        private String descricao;

        private ImpressaoPrescricao(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

        @Override
        public String toString() {
            return descricao;
        }

        public static ImpressaoPrescricao valueOf(Long codigo) {
            for (ImpressaoPrescricao ip : ImpressaoPrescricao.values()) {
                if (ip.value.equals(codigo)) {
                    return ip;
                }
            }
            return null;
        }

    }

    private List<ReceituarioItem> receituarioItemList;
    private String flag;
    private List<Cuidados> cuidados;
    /*[CONSTRUCTOR MARKER BEGIN]*/

	public Receituario () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public Receituario (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public Receituario (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.basico.Empresa empresa,
		br.com.ksisolucoes.vo.cadsus.Profissional profissional,
		br.com.ksisolucoes.vo.prontuario.basico.TipoReceita tipoReceita,
		java.util.Date dataCadastro,
		java.lang.String receitaContinua,
		java.util.Date dataUsuario,
		java.lang.Long situacao) {

		super (
			codigo,
			empresa,
			profissional,
			tipoReceita,
			dataCadastro,
			receitaContinua,
			dataUsuario,
			situacao);
	}

    /*[CONSTRUCTOR MARKER END]*/
    public void setCodigoManager(Serializable key) {
        this.setCodigo((Long) key);
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    public List<ReceituarioItem> getReceituarioItemList() {
        return receituarioItemList;
    }

    public void setReceituarioItemList(List<ReceituarioItem> receituarioItemList) {
        this.receituarioItemList = receituarioItemList;
    }

    public List<Cuidados> getCuidados() {
        return cuidados;
    }

    public void setCuidadosList(List<Cuidados> cuidados) {
        this.cuidados = cuidados;
    }

    public String getFlag() {
        return flag;
    }

    public void setFlag(String flag) {
        this.flag = flag;
    }

    public String getDescricaoReceitaContinua() {
        if (RepositoryComponentDefault.SIM.equals(this.getReceitaContinua())) {
            return Bundle.getStringApplication("rotulo_sim");
        } else if (RepositoryComponentDefault.NAO.equals(this.getReceitaContinua())) {
            return Bundle.getStringApplication("rotulo_nao");
        } else {
            return "";
        }
    }

    public String getSituacaoFormatado() {
        Situacao situacao = Situacao.valueOf(this.getSituacao());
        return situacao != null ? situacao.descricao() : Bundle.getStringApplication("deconhecido");
    }

    public Cid getCidReceituarioAtendimento() {
        Cid cid = getCid();
        if (cid == null && getAtendimento() != null) {
            cid = getAtendimento().getCidPrincipal();
        }
        return cid;
    }

    public String getDataHoraCadastro(){
        return Data.formatarDataHora(getDataCadastro());
    }

    public void setCodigoBarras (Long codigo, Long versionPrescricao) {
        if (versionPrescricao == null) {
            versionPrescricao = 1L;
        }
        String versao = versionPrescricao.toString();
        while (versao.length() < 3) {
            versao = "0" + versao;
        }
        setCodigoBarras(codigo.toString() + versao);
    }
}
