package br.com.ksisolucoes.vo.vigilancia;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.LoadInterceptor;
import br.com.ksisolucoes.dao.HQLHelper;
import java.text.SimpleDateFormat;

/**
 *
 * <AUTHOR>
 */
public class LoadInterceptorNotExistsSolicitacaoAgendamentoCVAOcorrencia implements LoadInterceptor {

    public LoadInterceptorNotExistsSolicitacaoAgendamentoCVAOcorrencia() {
    }

    @Override
    public void customHQL(HQLHelper hql, String alias) {
        HQLHelper hqlExists = new HQLHelper();
        hqlExists.addToSelect("1");
        hqlExists.addToFrom("SolicitacaoAgendamentoCVAOcorrencia t1 left join t1.solicitacaoAgendamentoCVA t2");
        hqlExists.addToWhereWhithAnd("t2.codigo = " + alias + ".codigo");
        hqlExists.addToWhereWhithAnd("cast(t1.dataOcorrencia as date) = '" + new SimpleDateFormat("yyyy-MM-dd").format(DataUtil.getDataAtual()) + "'");

        hql.addToWhereWhithAnd("not exists (" + hqlExists.getQuery() + ")");
    }
}
