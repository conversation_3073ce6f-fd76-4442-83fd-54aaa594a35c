package br.com.ksisolucoes.vo.vacina.pni;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import java.io.Serializable;

import br.com.ksisolucoes.vo.vacina.pni.base.BasePniProcesso;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class PniProcesso extends BasePniProcesso implements CodigoManager {
	private static final long serialVersionUID = 1L;
        
    public enum Status implements IEnum {
        STATUS_GERANDO_PNI(0L, Bundle.getStringApplication("rotulo_gerando_pni")),
        STATUS_PNI_GERADO(1L, Bundle.getStringApplication("rotulo_pni_gerado")),
        STATUS_GERANDO_ARQUIVO(2L, Bundle.getStringApplication("rotulo_gerando_arquivo")),
        STATUS_ARQUIVO_GERADO(3L, Bundle.getStringApplication("rotulo_arquivo_gerado")),
        STATUS_ERRO(4L, Bundle.getStringApplication("rotulo_erro")),
        STATUS_SEM_REGISTROS(5L, Bundle.getStringApplication("rotulo_sem_registros")),
        STATUS_CANCELADO(6L, Bundle.getStringApplication("rotulo_cancelado")),
        ;

        private Long value;
        private String descricao;

        private Status(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static Status valeuOf(Long value) {
            for (Status status : Status.values()) {
                if (status.value().equals(value)) {
                    return status;
                }
            }
            return null;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

    }

/*[CONSTRUCTOR MARKER BEGIN]*/
	public PniProcesso () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public PniProcesso (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public PniProcesso (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamento,
		java.util.Date dataGeracao) {

		super (
			codigo,
			usuario,
			usuarioCancelamento,
			dataGeracao);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
    
    public String getDescricaoMes(){
        return DataUtil.getDescricaoMes(getMes());
    }
    
    public String getDescricaoStatus(){
        Status status = Status.valeuOf(getStatus());
        if (status != null && status.descricao != null) {
            return status.descricao();
        }
        return "";
    }
}