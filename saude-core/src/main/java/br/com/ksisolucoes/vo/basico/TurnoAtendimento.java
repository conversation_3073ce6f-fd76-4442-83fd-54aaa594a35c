package br.com.ksisolucoes.vo.basico;

import java.io.Serializable;

import br.com.ksisolucoes.vo.basico.base.BaseTurnoAtendimento;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.interfaces.PesquisaObjectInterface;



public class TurnoAtendimento extends BaseTurnoAtendimento implements CodigoManager, PesquisaObjectInterface {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public TurnoAtendimento () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public TurnoAtendimento (java.lang.Long codigo) {
		super(codigo);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    @Override
    public String getDescricaoVO() {
        return getDescricao();
    }

    @Override
    public String getIdentificador() {
        return getCodigo().toString();
    }
}