package br.com.ksisolucoes.vo.entradas.estoque.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the movimento_estoque table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="movimento_estoque"
 */

public abstract class BaseMovimentoEstoque extends BaseRootVO implements Serializable {

	public static String REF = "MovimentoEstoque";
	public static final String PROP_USUARIO = "usuario";
	public static final String PROP_ESTOQUE_FISICO_GERAL = "estoqueFisicoGeral";
	public static final String PROP_ESTOQUE_FISICO = "estoqueFisico";
	public static final String PROP_ESTOQUE_FISICO_ANTERIOR_GRUPO = "estoqueFisicoAnteriorGrupo";
	public static final String PROP_ESTOQUE_DISPONIVEL = "estoqueDisponivel";
	public static final String PROP_RO_NUMERO_LANCAMENTO = "roNumeroLancamento";
	public static final String PROP_DATA_CADASTRO = "dataCadastro";
	public static final String PROP_NOME_PESSOA = "nomePessoa";
	public static final String PROP_ITEM_DOCUMENTO = "itemDocumento";
	public static final String PROP_GRUPO_ESTOQUE = "grupoEstoque";
	public static final String PROP_QUANTIDADE = "quantidade";
	public static final String PROP_PESSOA = "pessoa";
	public static final String PROP_PRECO_CUSTO = "precoCusto";
	public static final String PROP_ID_RECIBO = "idRecibo";
	public static final String PROP_USUARIO_CADSUS = "usuarioCadsus";
	public static final String PROP_OBSERVACAO = "observacao";
	public static final String PROP_CLASSIFICACAO_CONTABIL = "classificacaoContabil";
	public static final String PROP_DATA_LANCAMENTO = "dataLancamento";
	public static final String PROP_RO_EMPRESA = "roEmpresa";
	public static final String PROP_NOTA_FISCAL_ENTRADA = "notaFiscalEntrada";
	public static final String PROP_JUSTIFICATIVA_LOTE_POSTERIOR = "justificativaLotePosterior";
	public static final String PROP_SERIE = "serie";
	public static final String PROP_ESTOQUE_DISPONIVEL_GERAL = "estoqueDisponivelGeral";
	public static final String PROP_SINCRONIZACAO_HORUS_PROCESSO = "sincronizacaoHorusProcesso";
	public static final String PROP_PRODUTO = "produto";
	public static final String PROP_ESTOQUE_FISICO_GRUPO = "estoqueFisicoGrupo";
	public static final String PROP_PRECO_MEDIO = "precoMedio";
	public static final String PROP_PRECO_UNITARIO = "precoUnitario";
	public static final String PROP_DATA_PORTARIA = "dataPortaria";
	public static final String PROP_PRECO_CREDIARIO = "precoCrediario";
	public static final String PROP_DEPOSITO = "deposito";
	public static final String PROP_FABRICANTE = "fabricante";
	public static final String PROP_EMPRESA_DESTINO = "empresaDestino";
	public static final String PROP_CENTRO_CUSTO = "centroCusto";
	public static final String PROP_LOCALIZACAO_ESTRUTURA = "localizacaoEstrutura";
	public static final String PROP_NUMERO_DOCUMENTO = "numeroDocumento";
	public static final String PROP_ESTOQUE_DISPONIVEL_GRUPO = "estoqueDisponivelGrupo";
	public static final String PROP_REGISTRO_ITEM_NOTA_FISCAL = "registroItemNotaFiscal";
	public static final String PROP_ESTOQUE_FISICO_ANTERIOR = "estoqueFisicoAnterior";
	public static final String PROP_LABORATORIO_FABRICANTE = "laboratorioFabricante";
	public static final String PROP_TIPO_DOCUMENTO = "tipoDocumento";
	public static final String PROP_DATA_USUARIO = "dataUsuario";
	public static final String PROP_AUTO_INTIMACAO = "autoIntimacao";
	public static final String PROP_SUB_GRUPO = "subGrupo";
	public static final String PROP_PROFISSIONAL = "profissional";
	public static final String PROP_ID = "id";
	public static final String PROP_ESTOQUE_FISICO_ANTERIOR_GERAL = "estoqueFisicoAnteriorGeral";
	public static final String PROP_VALOR_PRODUTO = "valorProduto";
	public static final String PROP_DISPENSACAO_MEDICAMENTO_ITEM = "dispensacaoMedicamentoItem";


	// constructors
	public BaseMovimentoEstoque () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseMovimentoEstoque (br.com.ksisolucoes.vo.entradas.estoque.MovimentoEstoquePK id) {
		this.setId(id);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseMovimentoEstoque (
		br.com.ksisolucoes.vo.entradas.estoque.MovimentoEstoquePK id,
		br.com.ksisolucoes.vo.entradas.estoque.SubGrupo subGrupo,
		br.com.ksisolucoes.vo.entradas.estoque.LocalizacaoEstrutura localizacaoEstrutura,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		br.com.ksisolucoes.vo.entradas.estoque.Produto produto,
		br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento tipoDocumento,
		java.lang.Double quantidade,
		java.lang.Double estoqueFisicoAnterior,
		java.lang.Double precoMedio,
		java.lang.Double precoUnitario,
		java.util.Date dataLancamento,
		java.lang.Double precoCusto,
		java.util.Date dataPortaria,
		java.util.Date dataCadastro,
		java.lang.Double estoqueFisico,
		java.util.Date dataUsuario) {

		this.setId(id);
		this.setSubGrupo(subGrupo);
		this.setLocalizacaoEstrutura(localizacaoEstrutura);
		this.setUsuario(usuario);
		this.setProduto(produto);
		this.setTipoDocumento(tipoDocumento);
		this.setQuantidade(quantidade);
		this.setEstoqueFisicoAnterior(estoqueFisicoAnterior);
		this.setPrecoMedio(precoMedio);
		this.setPrecoUnitario(precoUnitario);
		this.setDataLancamento(dataLancamento);
		this.setPrecoCusto(precoCusto);
		this.setDataPortaria(dataPortaria);
		this.setDataCadastro(dataCadastro);
		this.setEstoqueFisico(estoqueFisico);
		this.setDataUsuario(dataUsuario);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private br.com.ksisolucoes.vo.entradas.estoque.MovimentoEstoquePK id;

	// fields
	private java.lang.String nomePessoa;
	private java.lang.Long itemDocumento;
	private java.lang.Double quantidade;
	private java.lang.String numeroDocumento;
	private java.lang.Double estoqueFisicoAnterior;
	private java.lang.String serie;
	private java.lang.Double precoMedio;
	private java.lang.Double precoUnitario;
	private java.lang.String observacao;
	private java.util.Date dataLancamento;
	private java.lang.Double precoCusto;
	private java.util.Date dataPortaria;
	private java.util.Date dataCadastro;
	private java.lang.Double estoqueFisico;
	private java.lang.Double precoCrediario;
	private java.util.Date dataUsuario;
	private java.lang.String grupoEstoque;
	private java.lang.Double estoqueDisponivelGrupo;
	private java.lang.Double estoqueFisicoGrupo;
	private java.lang.Double estoqueFisicoAnteriorGrupo;
	private java.lang.Double estoqueDisponivelGeral;
	private java.lang.Double estoqueFisicoGeral;
	private java.lang.Double estoqueFisicoAnteriorGeral;
	private java.lang.Double estoqueDisponivel;
	private java.lang.Long notaFiscalEntrada;
	private java.lang.String justificativaLotePosterior;
	private java.lang.String laboratorioFabricante;
	private java.lang.Long roNumeroLancamento;
	private java.lang.Double valorProduto;
	private java.lang.String autoIntimacao;
	private java.lang.Long idRecibo;

	// many to one
	private br.com.ksisolucoes.vo.entradas.estoque.CentroCusto centroCusto;
	private br.com.ksisolucoes.vo.entradas.recebimento.RegistroItemNotaFiscal registroItemNotaFiscal;
	private br.com.ksisolucoes.vo.entradas.estoque.SubGrupo subGrupo;
	private br.com.ksisolucoes.vo.entradas.estoque.ClassificacaoContabil classificacaoContabil;
	private br.com.ksisolucoes.vo.materiais.horus.SincronizacaoHorusProcesso sincronizacaoHorusProcesso;
	private br.com.ksisolucoes.vo.entradas.estoque.LocalizacaoEstrutura localizacaoEstrutura;
	private br.com.ksisolucoes.vo.entradas.estoque.Fabricante fabricante;
	private br.com.ksisolucoes.vo.basico.Empresa roEmpresa;
	private br.com.ksisolucoes.vo.controle.Usuario usuario;
	private br.com.ksisolucoes.vo.entradas.estoque.Produto produto;
	private br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento tipoDocumento;
	private br.com.ksisolucoes.vo.basico.Empresa empresaDestino;
	private br.com.ksisolucoes.vo.cadsus.Profissional profissional;
	private br.com.ksisolucoes.vo.entradas.estoque.Deposito deposito;
	private br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus;
	private br.com.ksisolucoes.vo.basico.Pessoa pessoa;
	private br.com.ksisolucoes.vo.entradas.dispensacao.DispensacaoMedicamentoItem dispensacaoMedicamentoItem;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     */
	public br.com.ksisolucoes.vo.entradas.estoque.MovimentoEstoquePK getId () {
	    return getPropertyValue(this,  id, "id" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param id the new ID
	 */
	public void setId (br.com.ksisolucoes.vo.entradas.estoque.MovimentoEstoquePK id) {
		this.id = id;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: nome_Pessoa
	 */
	public java.lang.String getNomePessoa () {
		return getPropertyValue(this, nomePessoa, PROP_NOME_PESSOA); 
	}

	/**
	 * Set the value related to the column: nome_Pessoa
	 * @param nomePessoa the nome_Pessoa value
	 */
	public void setNomePessoa (java.lang.String nomePessoa) {
//        java.lang.String nomePessoaOld = this.nomePessoa;
		this.nomePessoa = nomePessoa;
//        this.getPropertyChangeSupport().firePropertyChange ("nomePessoa", nomePessoaOld, nomePessoa);
	}



	/**
	 * Return the value associated with the column: item_doc
	 */
	public java.lang.Long getItemDocumento () {
		return getPropertyValue(this, itemDocumento, PROP_ITEM_DOCUMENTO); 
	}

	/**
	 * Set the value related to the column: item_doc
	 * @param itemDocumento the item_doc value
	 */
	public void setItemDocumento (java.lang.Long itemDocumento) {
//        java.lang.Long itemDocumentoOld = this.itemDocumento;
		this.itemDocumento = itemDocumento;
//        this.getPropertyChangeSupport().firePropertyChange ("itemDocumento", itemDocumentoOld, itemDocumento);
	}



	/**
	 * Return the value associated with the column: quantidade
	 */
	public java.lang.Double getQuantidade () {
		return getPropertyValue(this, quantidade, PROP_QUANTIDADE); 
	}

	/**
	 * Set the value related to the column: quantidade
	 * @param quantidade the quantidade value
	 */
	public void setQuantidade (java.lang.Double quantidade) {
//        java.lang.Double quantidadeOld = this.quantidade;
		this.quantidade = quantidade;
//        this.getPropertyChangeSupport().firePropertyChange ("quantidade", quantidadeOld, quantidade);
	}



	/**
	 * Return the value associated with the column: numero_documento
	 */
	public java.lang.String getNumeroDocumento () {
		return getPropertyValue(this, numeroDocumento, PROP_NUMERO_DOCUMENTO); 
	}

	/**
	 * Set the value related to the column: numero_documento
	 * @param numeroDocumento the numero_documento value
	 */
	public void setNumeroDocumento (java.lang.String numeroDocumento) {
//        java.lang.String numeroDocumentoOld = this.numeroDocumento;
		this.numeroDocumento = numeroDocumento;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroDocumento", numeroDocumentoOld, numeroDocumento);
	}



	/**
	 * Return the value associated with the column: estoque_fisico_anterior
	 */
	public java.lang.Double getEstoqueFisicoAnterior () {
		return getPropertyValue(this, estoqueFisicoAnterior, PROP_ESTOQUE_FISICO_ANTERIOR); 
	}

	/**
	 * Set the value related to the column: estoque_fisico_anterior
	 * @param estoqueFisicoAnterior the estoque_fisico_anterior value
	 */
	public void setEstoqueFisicoAnterior (java.lang.Double estoqueFisicoAnterior) {
//        java.lang.Double estoqueFisicoAnteriorOld = this.estoqueFisicoAnterior;
		this.estoqueFisicoAnterior = estoqueFisicoAnterior;
//        this.getPropertyChangeSupport().firePropertyChange ("estoqueFisicoAnterior", estoqueFisicoAnteriorOld, estoqueFisicoAnterior);
	}



	/**
	 * Return the value associated with the column: serie
	 */
	public java.lang.String getSerie () {
		return getPropertyValue(this, serie, PROP_SERIE); 
	}

	/**
	 * Set the value related to the column: serie
	 * @param serie the serie value
	 */
	public void setSerie (java.lang.String serie) {
//        java.lang.String serieOld = this.serie;
		this.serie = serie;
//        this.getPropertyChangeSupport().firePropertyChange ("serie", serieOld, serie);
	}



	/**
	 * Return the value associated with the column: preco_medio
	 */
	public java.lang.Double getPrecoMedio () {
		return getPropertyValue(this, precoMedio, PROP_PRECO_MEDIO); 
	}

	/**
	 * Set the value related to the column: preco_medio
	 * @param precoMedio the preco_medio value
	 */
	public void setPrecoMedio (java.lang.Double precoMedio) {
//        java.lang.Double precoMedioOld = this.precoMedio;
		this.precoMedio = precoMedio;
//        this.getPropertyChangeSupport().firePropertyChange ("precoMedio", precoMedioOld, precoMedio);
	}



	/**
	 * Return the value associated with the column: preco_unitario
	 */
	public java.lang.Double getPrecoUnitario () {
		return getPropertyValue(this, precoUnitario, PROP_PRECO_UNITARIO); 
	}

	/**
	 * Set the value related to the column: preco_unitario
	 * @param precoUnitario the preco_unitario value
	 */
	public void setPrecoUnitario (java.lang.Double precoUnitario) {
//        java.lang.Double precoUnitarioOld = this.precoUnitario;
		this.precoUnitario = precoUnitario;
//        this.getPropertyChangeSupport().firePropertyChange ("precoUnitario", precoUnitarioOld, precoUnitario);
	}



	/**
	 * Return the value associated with the column: observacao
	 */
	public java.lang.String getObservacao () {
		return getPropertyValue(this, observacao, PROP_OBSERVACAO); 
	}

	/**
	 * Set the value related to the column: observacao
	 * @param observacao the observacao value
	 */
	public void setObservacao (java.lang.String observacao) {
//        java.lang.String observacaoOld = this.observacao;
		this.observacao = observacao;
//        this.getPropertyChangeSupport().firePropertyChange ("observacao", observacaoOld, observacao);
	}



	/**
	 * Return the value associated with the column: dt_lancto
	 */
	public java.util.Date getDataLancamento () {
		return getPropertyValue(this, dataLancamento, PROP_DATA_LANCAMENTO); 
	}

	/**
	 * Set the value related to the column: dt_lancto
	 * @param dataLancamento the dt_lancto value
	 */
	public void setDataLancamento (java.util.Date dataLancamento) {
//        java.util.Date dataLancamentoOld = this.dataLancamento;
		this.dataLancamento = dataLancamento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataLancamento", dataLancamentoOld, dataLancamento);
	}



	/**
	 * Return the value associated with the column: preco_custo
	 */
	public java.lang.Double getPrecoCusto () {
		return getPropertyValue(this, precoCusto, PROP_PRECO_CUSTO); 
	}

	/**
	 * Set the value related to the column: preco_custo
	 * @param precoCusto the preco_custo value
	 */
	public void setPrecoCusto (java.lang.Double precoCusto) {
//        java.lang.Double precoCustoOld = this.precoCusto;
		this.precoCusto = precoCusto;
//        this.getPropertyChangeSupport().firePropertyChange ("precoCusto", precoCustoOld, precoCusto);
	}



	/**
	 * Return the value associated with the column: dt_portaria
	 */
	public java.util.Date getDataPortaria () {
		return getPropertyValue(this, dataPortaria, PROP_DATA_PORTARIA); 
	}

	/**
	 * Set the value related to the column: dt_portaria
	 * @param dataPortaria the dt_portaria value
	 */
	public void setDataPortaria (java.util.Date dataPortaria) {
//        java.util.Date dataPortariaOld = this.dataPortaria;
		this.dataPortaria = dataPortaria;
//        this.getPropertyChangeSupport().firePropertyChange ("dataPortaria", dataPortariaOld, dataPortaria);
	}



	/**
	 * Return the value associated with the column: dt_cadastro
	 */
	public java.util.Date getDataCadastro () {
		return getPropertyValue(this, dataCadastro, PROP_DATA_CADASTRO); 
	}

	/**
	 * Set the value related to the column: dt_cadastro
	 * @param dataCadastro the dt_cadastro value
	 */
	public void setDataCadastro (java.util.Date dataCadastro) {
//        java.util.Date dataCadastroOld = this.dataCadastro;
		this.dataCadastro = dataCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCadastro", dataCadastroOld, dataCadastro);
	}



	/**
	 * Return the value associated with the column: estoque_fisico
	 */
	public java.lang.Double getEstoqueFisico () {
		return getPropertyValue(this, estoqueFisico, PROP_ESTOQUE_FISICO); 
	}

	/**
	 * Set the value related to the column: estoque_fisico
	 * @param estoqueFisico the estoque_fisico value
	 */
	public void setEstoqueFisico (java.lang.Double estoqueFisico) {
//        java.lang.Double estoqueFisicoOld = this.estoqueFisico;
		this.estoqueFisico = estoqueFisico;
//        this.getPropertyChangeSupport().firePropertyChange ("estoqueFisico", estoqueFisicoOld, estoqueFisico);
	}



	/**
	 * Return the value associated with the column: preco_crediario
	 */
	public java.lang.Double getPrecoCrediario () {
		return getPropertyValue(this, precoCrediario, PROP_PRECO_CREDIARIO); 
	}

	/**
	 * Set the value related to the column: preco_crediario
	 * @param precoCrediario the preco_crediario value
	 */
	public void setPrecoCrediario (java.lang.Double precoCrediario) {
//        java.lang.Double precoCrediarioOld = this.precoCrediario;
		this.precoCrediario = precoCrediario;
//        this.getPropertyChangeSupport().firePropertyChange ("precoCrediario", precoCrediarioOld, precoCrediario);
	}



	/**
	 * Return the value associated with the column: dt_usuario
	 */
	public java.util.Date getDataUsuario () {
		return getPropertyValue(this, dataUsuario, PROP_DATA_USUARIO); 
	}

	/**
	 * Set the value related to the column: dt_usuario
	 * @param dataUsuario the dt_usuario value
	 */
	public void setDataUsuario (java.util.Date dataUsuario) {
//        java.util.Date dataUsuarioOld = this.dataUsuario;
		this.dataUsuario = dataUsuario;
//        this.getPropertyChangeSupport().firePropertyChange ("dataUsuario", dataUsuarioOld, dataUsuario);
	}



	/**
	 * Return the value associated with the column: grupo_estoque
	 */
	public java.lang.String getGrupoEstoque () {
		return getPropertyValue(this, grupoEstoque, PROP_GRUPO_ESTOQUE); 
	}

	/**
	 * Set the value related to the column: grupo_estoque
	 * @param grupoEstoque the grupo_estoque value
	 */
	public void setGrupoEstoque (java.lang.String grupoEstoque) {
//        java.lang.String grupoEstoqueOld = this.grupoEstoque;
		this.grupoEstoque = grupoEstoque;
//        this.getPropertyChangeSupport().firePropertyChange ("grupoEstoque", grupoEstoqueOld, grupoEstoque);
	}



	/**
	 * Return the value associated with the column: ESTOQUE_DISPONIVEL_GRUPO
	 */
	public java.lang.Double getEstoqueDisponivelGrupo () {
		return getPropertyValue(this, estoqueDisponivelGrupo, PROP_ESTOQUE_DISPONIVEL_GRUPO); 
	}

	/**
	 * Set the value related to the column: ESTOQUE_DISPONIVEL_GRUPO
	 * @param estoqueDisponivelGrupo the ESTOQUE_DISPONIVEL_GRUPO value
	 */
	public void setEstoqueDisponivelGrupo (java.lang.Double estoqueDisponivelGrupo) {
//        java.lang.Double estoqueDisponivelGrupoOld = this.estoqueDisponivelGrupo;
		this.estoqueDisponivelGrupo = estoqueDisponivelGrupo;
//        this.getPropertyChangeSupport().firePropertyChange ("estoqueDisponivelGrupo", estoqueDisponivelGrupoOld, estoqueDisponivelGrupo);
	}



	/**
	 * Return the value associated with the column: ESTOQUE_FISICO_GRUPO
	 */
	public java.lang.Double getEstoqueFisicoGrupo () {
		return getPropertyValue(this, estoqueFisicoGrupo, PROP_ESTOQUE_FISICO_GRUPO); 
	}

	/**
	 * Set the value related to the column: ESTOQUE_FISICO_GRUPO
	 * @param estoqueFisicoGrupo the ESTOQUE_FISICO_GRUPO value
	 */
	public void setEstoqueFisicoGrupo (java.lang.Double estoqueFisicoGrupo) {
//        java.lang.Double estoqueFisicoGrupoOld = this.estoqueFisicoGrupo;
		this.estoqueFisicoGrupo = estoqueFisicoGrupo;
//        this.getPropertyChangeSupport().firePropertyChange ("estoqueFisicoGrupo", estoqueFisicoGrupoOld, estoqueFisicoGrupo);
	}



	/**
	 * Return the value associated with the column: ESTOQUE_FISICO_ANTERIOR_GRUPO
	 */
	public java.lang.Double getEstoqueFisicoAnteriorGrupo () {
		return getPropertyValue(this, estoqueFisicoAnteriorGrupo, PROP_ESTOQUE_FISICO_ANTERIOR_GRUPO); 
	}

	/**
	 * Set the value related to the column: ESTOQUE_FISICO_ANTERIOR_GRUPO
	 * @param estoqueFisicoAnteriorGrupo the ESTOQUE_FISICO_ANTERIOR_GRUPO value
	 */
	public void setEstoqueFisicoAnteriorGrupo (java.lang.Double estoqueFisicoAnteriorGrupo) {
//        java.lang.Double estoqueFisicoAnteriorGrupoOld = this.estoqueFisicoAnteriorGrupo;
		this.estoqueFisicoAnteriorGrupo = estoqueFisicoAnteriorGrupo;
//        this.getPropertyChangeSupport().firePropertyChange ("estoqueFisicoAnteriorGrupo", estoqueFisicoAnteriorGrupoOld, estoqueFisicoAnteriorGrupo);
	}



	/**
	 * Return the value associated with the column: ESTOQUE_DISPONIVEL_GERAL
	 */
	public java.lang.Double getEstoqueDisponivelGeral () {
		return getPropertyValue(this, estoqueDisponivelGeral, PROP_ESTOQUE_DISPONIVEL_GERAL); 
	}

	/**
	 * Set the value related to the column: ESTOQUE_DISPONIVEL_GERAL
	 * @param estoqueDisponivelGeral the ESTOQUE_DISPONIVEL_GERAL value
	 */
	public void setEstoqueDisponivelGeral (java.lang.Double estoqueDisponivelGeral) {
//        java.lang.Double estoqueDisponivelGeralOld = this.estoqueDisponivelGeral;
		this.estoqueDisponivelGeral = estoqueDisponivelGeral;
//        this.getPropertyChangeSupport().firePropertyChange ("estoqueDisponivelGeral", estoqueDisponivelGeralOld, estoqueDisponivelGeral);
	}



	/**
	 * Return the value associated with the column: ESTOQUE_FISICO_GERAL
	 */
	public java.lang.Double getEstoqueFisicoGeral () {
		return getPropertyValue(this, estoqueFisicoGeral, PROP_ESTOQUE_FISICO_GERAL); 
	}

	/**
	 * Set the value related to the column: ESTOQUE_FISICO_GERAL
	 * @param estoqueFisicoGeral the ESTOQUE_FISICO_GERAL value
	 */
	public void setEstoqueFisicoGeral (java.lang.Double estoqueFisicoGeral) {
//        java.lang.Double estoqueFisicoGeralOld = this.estoqueFisicoGeral;
		this.estoqueFisicoGeral = estoqueFisicoGeral;
//        this.getPropertyChangeSupport().firePropertyChange ("estoqueFisicoGeral", estoqueFisicoGeralOld, estoqueFisicoGeral);
	}



	/**
	 * Return the value associated with the column: ESTOQUE_FISICO_ANTERIOR_GERAL
	 */
	public java.lang.Double getEstoqueFisicoAnteriorGeral () {
		return getPropertyValue(this, estoqueFisicoAnteriorGeral, PROP_ESTOQUE_FISICO_ANTERIOR_GERAL); 
	}

	/**
	 * Set the value related to the column: ESTOQUE_FISICO_ANTERIOR_GERAL
	 * @param estoqueFisicoAnteriorGeral the ESTOQUE_FISICO_ANTERIOR_GERAL value
	 */
	public void setEstoqueFisicoAnteriorGeral (java.lang.Double estoqueFisicoAnteriorGeral) {
//        java.lang.Double estoqueFisicoAnteriorGeralOld = this.estoqueFisicoAnteriorGeral;
		this.estoqueFisicoAnteriorGeral = estoqueFisicoAnteriorGeral;
//        this.getPropertyChangeSupport().firePropertyChange ("estoqueFisicoAnteriorGeral", estoqueFisicoAnteriorGeralOld, estoqueFisicoAnteriorGeral);
	}



	/**
	 * Return the value associated with the column: ESTOQUE_DISPONIVEL
	 */
	public java.lang.Double getEstoqueDisponivel () {
		return getPropertyValue(this, estoqueDisponivel, PROP_ESTOQUE_DISPONIVEL); 
	}

	/**
	 * Set the value related to the column: ESTOQUE_DISPONIVEL
	 * @param estoqueDisponivel the ESTOQUE_DISPONIVEL value
	 */
	public void setEstoqueDisponivel (java.lang.Double estoqueDisponivel) {
//        java.lang.Double estoqueDisponivelOld = this.estoqueDisponivel;
		this.estoqueDisponivel = estoqueDisponivel;
//        this.getPropertyChangeSupport().firePropertyChange ("estoqueDisponivel", estoqueDisponivelOld, estoqueDisponivel);
	}



	/**
	 * Return the value associated with the column: nf_entrada
	 */
	public java.lang.Long getNotaFiscalEntrada () {
		return getPropertyValue(this, notaFiscalEntrada, PROP_NOTA_FISCAL_ENTRADA); 
	}

	/**
	 * Set the value related to the column: nf_entrada
	 * @param notaFiscalEntrada the nf_entrada value
	 */
	public void setNotaFiscalEntrada (java.lang.Long notaFiscalEntrada) {
//        java.lang.Long notaFiscalEntradaOld = this.notaFiscalEntrada;
		this.notaFiscalEntrada = notaFiscalEntrada;
//        this.getPropertyChangeSupport().firePropertyChange ("notaFiscalEntrada", notaFiscalEntradaOld, notaFiscalEntrada);
	}



	/**
	 * Return the value associated with the column: justificativa_lote_posterior
	 */
	public java.lang.String getJustificativaLotePosterior () {
		return getPropertyValue(this, justificativaLotePosterior, PROP_JUSTIFICATIVA_LOTE_POSTERIOR); 
	}

	/**
	 * Set the value related to the column: justificativa_lote_posterior
	 * @param justificativaLotePosterior the justificativa_lote_posterior value
	 */
	public void setJustificativaLotePosterior (java.lang.String justificativaLotePosterior) {
//        java.lang.String justificativaLotePosteriorOld = this.justificativaLotePosterior;
		this.justificativaLotePosterior = justificativaLotePosterior;
//        this.getPropertyChangeSupport().firePropertyChange ("justificativaLotePosterior", justificativaLotePosteriorOld, justificativaLotePosterior);
	}



	/**
	 * Return the value associated with the column: laboratorio_fabricante
	 */
	public java.lang.String getLaboratorioFabricante () {
		return getPropertyValue(this, laboratorioFabricante, PROP_LABORATORIO_FABRICANTE); 
	}

	/**
	 * Set the value related to the column: laboratorio_fabricante
	 * @param laboratorioFabricante the laboratorio_fabricante value
	 */
	public void setLaboratorioFabricante (java.lang.String laboratorioFabricante) {
//        java.lang.String laboratorioFabricanteOld = this.laboratorioFabricante;
		this.laboratorioFabricante = laboratorioFabricante;
//        this.getPropertyChangeSupport().firePropertyChange ("laboratorioFabricante", laboratorioFabricanteOld, laboratorioFabricante);
	}



	/**
	 * Return the value associated with the column: num_lancto
	 */
	public java.lang.Long getRoNumeroLancamento () {
		return getPropertyValue(this, roNumeroLancamento, PROP_RO_NUMERO_LANCAMENTO); 
	}

	/**
	 * Set the value related to the column: num_lancto
	 * @param roNumeroLancamento the num_lancto value
	 */
	public void setRoNumeroLancamento (java.lang.Long roNumeroLancamento) {
//        java.lang.Long roNumeroLancamentoOld = this.roNumeroLancamento;
		this.roNumeroLancamento = roNumeroLancamento;
//        this.getPropertyChangeSupport().firePropertyChange ("roNumeroLancamento", roNumeroLancamentoOld, roNumeroLancamento);
	}



	/**
	 * Return the value associated with the column: valor_produto
	 */
	public java.lang.Double getValorProduto () {
		return getPropertyValue(this, valorProduto, PROP_VALOR_PRODUTO); 
	}

	/**
	 * Set the value related to the column: valor_produto
	 * @param valorProduto the valor_produto value
	 */
	public void setValorProduto (java.lang.Double valorProduto) {
//        java.lang.Double valorProdutoOld = this.valorProduto;
		this.valorProduto = valorProduto;
//        this.getPropertyChangeSupport().firePropertyChange ("valorProduto", valorProdutoOld, valorProduto);
	}



	/**
	 * Return the value associated with the column: auto_intimacao
	 */
	public java.lang.String getAutoIntimacao () {
		return getPropertyValue(this, autoIntimacao, PROP_AUTO_INTIMACAO); 
	}

	/**
	 * Set the value related to the column: auto_intimacao
	 * @param autoIntimacao the auto_intimacao value
	 */
	public void setAutoIntimacao (java.lang.String autoIntimacao) {
//        java.lang.String autoIntimacaoOld = this.autoIntimacao;
		this.autoIntimacao = autoIntimacao;
//        this.getPropertyChangeSupport().firePropertyChange ("autoIntimacao", autoIntimacaoOld, autoIntimacao);
	}



	/**
	 * Return the value associated with the column: id_recibo
	 */
	public java.lang.Long getIdRecibo () {
		return getPropertyValue(this, idRecibo, PROP_ID_RECIBO); 
	}

	/**
	 * Set the value related to the column: id_recibo
	 * @param idRecibo the id_recibo value
	 */
	public void setIdRecibo (java.lang.Long idRecibo) {
//        java.lang.Long idReciboOld = this.idRecibo;
		this.idRecibo = idRecibo;
//        this.getPropertyChangeSupport().firePropertyChange ("idRecibo", idReciboOld, idRecibo);
	}



	/**
	 * Return the value associated with the column: cod_centro_custo
	 */
	public br.com.ksisolucoes.vo.entradas.estoque.CentroCusto getCentroCusto () {
		return getPropertyValue(this, centroCusto, PROP_CENTRO_CUSTO); 
	}

	/**
	 * Set the value related to the column: cod_centro_custo
	 * @param centroCusto the cod_centro_custo value
	 */
	public void setCentroCusto (br.com.ksisolucoes.vo.entradas.estoque.CentroCusto centroCusto) {
//        br.com.ksisolucoes.vo.entradas.estoque.CentroCusto centroCustoOld = this.centroCusto;
		this.centroCusto = centroCusto;
//        this.getPropertyChangeSupport().firePropertyChange ("centroCusto", centroCustoOld, centroCusto);
	}



	/**
	 * Return the value associated with the column: cd_reg_it_nf
	 */
	public br.com.ksisolucoes.vo.entradas.recebimento.RegistroItemNotaFiscal getRegistroItemNotaFiscal () {
		return getPropertyValue(this, registroItemNotaFiscal, PROP_REGISTRO_ITEM_NOTA_FISCAL); 
	}

	/**
	 * Set the value related to the column: cd_reg_it_nf
	 * @param registroItemNotaFiscal the cd_reg_it_nf value
	 */
	public void setRegistroItemNotaFiscal (br.com.ksisolucoes.vo.entradas.recebimento.RegistroItemNotaFiscal registroItemNotaFiscal) {
//        br.com.ksisolucoes.vo.entradas.recebimento.RegistroItemNotaFiscal registroItemNotaFiscalOld = this.registroItemNotaFiscal;
		this.registroItemNotaFiscal = registroItemNotaFiscal;
//        this.getPropertyChangeSupport().firePropertyChange ("registroItemNotaFiscal", registroItemNotaFiscalOld, registroItemNotaFiscal);
	}



	/**
	 * Return the value associated with the column: cod_gru
	 */
	public br.com.ksisolucoes.vo.entradas.estoque.SubGrupo getSubGrupo () {
		return getPropertyValue(this, subGrupo, PROP_SUB_GRUPO); 
	}

	/**
	 * Set the value related to the column: cod_gru
	 * @param subGrupo the cod_gru value
	 */
	public void setSubGrupo (br.com.ksisolucoes.vo.entradas.estoque.SubGrupo subGrupo) {
//        br.com.ksisolucoes.vo.entradas.estoque.SubGrupo subGrupoOld = this.subGrupo;
		this.subGrupo = subGrupo;
//        this.getPropertyChangeSupport().firePropertyChange ("subGrupo", subGrupoOld, subGrupo);
	}



	/**
	 * Return the value associated with the column: cd_classificacao
	 */
	public br.com.ksisolucoes.vo.entradas.estoque.ClassificacaoContabil getClassificacaoContabil () {
		return getPropertyValue(this, classificacaoContabil, PROP_CLASSIFICACAO_CONTABIL); 
	}

	/**
	 * Set the value related to the column: cd_classificacao
	 * @param classificacaoContabil the cd_classificacao value
	 */
	public void setClassificacaoContabil (br.com.ksisolucoes.vo.entradas.estoque.ClassificacaoContabil classificacaoContabil) {
//        br.com.ksisolucoes.vo.entradas.estoque.ClassificacaoContabil classificacaoContabilOld = this.classificacaoContabil;
		this.classificacaoContabil = classificacaoContabil;
//        this.getPropertyChangeSupport().firePropertyChange ("classificacaoContabil", classificacaoContabilOld, classificacaoContabil);
	}



	/**
	 * Return the value associated with the column: cd_horus_sincronizacao_processo
	 */
	public br.com.ksisolucoes.vo.materiais.horus.SincronizacaoHorusProcesso getSincronizacaoHorusProcesso () {
		return getPropertyValue(this, sincronizacaoHorusProcesso, PROP_SINCRONIZACAO_HORUS_PROCESSO); 
	}

	/**
	 * Set the value related to the column: cd_horus_sincronizacao_processo
	 * @param sincronizacaoHorusProcesso the cd_horus_sincronizacao_processo value
	 */
	public void setSincronizacaoHorusProcesso (br.com.ksisolucoes.vo.materiais.horus.SincronizacaoHorusProcesso sincronizacaoHorusProcesso) {
//        br.com.ksisolucoes.vo.materiais.horus.SincronizacaoHorusProcesso sincronizacaoHorusProcessoOld = this.sincronizacaoHorusProcesso;
		this.sincronizacaoHorusProcesso = sincronizacaoHorusProcesso;
//        this.getPropertyChangeSupport().firePropertyChange ("sincronizacaoHorusProcesso", sincronizacaoHorusProcessoOld, sincronizacaoHorusProcesso);
	}



	/**
	 * Return the value associated with the column: cd_localizacao_estrutura
	 */
	public br.com.ksisolucoes.vo.entradas.estoque.LocalizacaoEstrutura getLocalizacaoEstrutura () {
		return getPropertyValue(this, localizacaoEstrutura, PROP_LOCALIZACAO_ESTRUTURA); 
	}

	/**
	 * Set the value related to the column: cd_localizacao_estrutura
	 * @param localizacaoEstrutura the cd_localizacao_estrutura value
	 */
	public void setLocalizacaoEstrutura (br.com.ksisolucoes.vo.entradas.estoque.LocalizacaoEstrutura localizacaoEstrutura) {
//        br.com.ksisolucoes.vo.entradas.estoque.LocalizacaoEstrutura localizacaoEstruturaOld = this.localizacaoEstrutura;
		this.localizacaoEstrutura = localizacaoEstrutura;
//        this.getPropertyChangeSupport().firePropertyChange ("localizacaoEstrutura", localizacaoEstruturaOld, localizacaoEstrutura);
	}



	/**
	 * Return the value associated with the column: cd_fabricante
	 */
	public br.com.ksisolucoes.vo.entradas.estoque.Fabricante getFabricante () {
		return getPropertyValue(this, fabricante, PROP_FABRICANTE); 
	}

	/**
	 * Set the value related to the column: cd_fabricante
	 * @param fabricante the cd_fabricante value
	 */
	public void setFabricante (br.com.ksisolucoes.vo.entradas.estoque.Fabricante fabricante) {
//        br.com.ksisolucoes.vo.entradas.estoque.Fabricante fabricanteOld = this.fabricante;
		this.fabricante = fabricante;
//        this.getPropertyChangeSupport().firePropertyChange ("fabricante", fabricanteOld, fabricante);
	}



	/**
	 * Return the value associated with the column: empresa
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getRoEmpresa () {
		return getPropertyValue(this, roEmpresa, PROP_RO_EMPRESA); 
	}

	/**
	 * Set the value related to the column: empresa
	 * @param roEmpresa the empresa value
	 */
	public void setRoEmpresa (br.com.ksisolucoes.vo.basico.Empresa roEmpresa) {
//        br.com.ksisolucoes.vo.basico.Empresa roEmpresaOld = this.roEmpresa;
		this.roEmpresa = roEmpresa;
//        this.getPropertyChangeSupport().firePropertyChange ("roEmpresa", roEmpresaOld, roEmpresa);
	}



	/**
	 * Return the value associated with the column: usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO); 
	}

	/**
	 * Set the value related to the column: usuario
	 * @param usuario the usuario value
	 */
	public void setUsuario (br.com.ksisolucoes.vo.controle.Usuario usuario) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}



	/**
	 * Return the value associated with the column: cod_pro
	 */
	public br.com.ksisolucoes.vo.entradas.estoque.Produto getProduto () {
		return getPropertyValue(this, produto, PROP_PRODUTO); 
	}

	/**
	 * Set the value related to the column: cod_pro
	 * @param produto the cod_pro value
	 */
	public void setProduto (br.com.ksisolucoes.vo.entradas.estoque.Produto produto) {
//        br.com.ksisolucoes.vo.entradas.estoque.Produto produtoOld = this.produto;
		this.produto = produto;
//        this.getPropertyChangeSupport().firePropertyChange ("produto", produtoOld, produto);
	}



	/**
	 * Return the value associated with the column: cod_tip_doc_estoque
	 */
	public br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento getTipoDocumento () {
		return getPropertyValue(this, tipoDocumento, PROP_TIPO_DOCUMENTO); 
	}

	/**
	 * Set the value related to the column: cod_tip_doc_estoque
	 * @param tipoDocumento the cod_tip_doc_estoque value
	 */
	public void setTipoDocumento (br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento tipoDocumento) {
//        br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento tipoDocumentoOld = this.tipoDocumento;
		this.tipoDocumento = tipoDocumento;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoDocumento", tipoDocumentoOld, tipoDocumento);
	}



	/**
	 * Return the value associated with the column: empresa_destino
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getEmpresaDestino () {
		return getPropertyValue(this, empresaDestino, PROP_EMPRESA_DESTINO); 
	}

	/**
	 * Set the value related to the column: empresa_destino
	 * @param empresaDestino the empresa_destino value
	 */
	public void setEmpresaDestino (br.com.ksisolucoes.vo.basico.Empresa empresaDestino) {
//        br.com.ksisolucoes.vo.basico.Empresa empresaDestinoOld = this.empresaDestino;
		this.empresaDestino = empresaDestino;
//        this.getPropertyChangeSupport().firePropertyChange ("empresaDestino", empresaDestinoOld, empresaDestino);
	}



	/**
	 * Return the value associated with the column: cd_profissional
	 */
	public br.com.ksisolucoes.vo.cadsus.Profissional getProfissional () {
		return getPropertyValue(this, profissional, PROP_PROFISSIONAL); 
	}

	/**
	 * Set the value related to the column: cd_profissional
	 * @param profissional the cd_profissional value
	 */
	public void setProfissional (br.com.ksisolucoes.vo.cadsus.Profissional profissional) {
//        br.com.ksisolucoes.vo.cadsus.Profissional profissionalOld = this.profissional;
		this.profissional = profissional;
//        this.getPropertyChangeSupport().firePropertyChange ("profissional", profissionalOld, profissional);
	}



	/**
	 * Return the value associated with the column: cod_deposito
	 */
	public br.com.ksisolucoes.vo.entradas.estoque.Deposito getDeposito () {
		return getPropertyValue(this, deposito, PROP_DEPOSITO); 
	}

	/**
	 * Set the value related to the column: cod_deposito
	 * @param deposito the cod_deposito value
	 */
	public void setDeposito (br.com.ksisolucoes.vo.entradas.estoque.Deposito deposito) {
//        br.com.ksisolucoes.vo.entradas.estoque.Deposito depositoOld = this.deposito;
		this.deposito = deposito;
//        this.getPropertyChangeSupport().firePropertyChange ("deposito", depositoOld, deposito);
	}



	/**
	 * Return the value associated with the column: cd_usu_cadsus
	 */
	public br.com.ksisolucoes.vo.cadsus.UsuarioCadsus getUsuarioCadsus () {
		return getPropertyValue(this, usuarioCadsus, PROP_USUARIO_CADSUS); 
	}

	/**
	 * Set the value related to the column: cd_usu_cadsus
	 * @param usuarioCadsus the cd_usu_cadsus value
	 */
	public void setUsuarioCadsus (br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus) {
//        br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsusOld = this.usuarioCadsus;
		this.usuarioCadsus = usuarioCadsus;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCadsus", usuarioCadsusOld, usuarioCadsus);
	}



	/**
	 * Return the value associated with the column: cod_pessoa
	 */
	public br.com.ksisolucoes.vo.basico.Pessoa getPessoa () {
		return getPropertyValue(this, pessoa, PROP_PESSOA); 
	}

	/**
	 * Set the value related to the column: cod_pessoa
	 * @param pessoa the cod_pessoa value
	 */
	public void setPessoa (br.com.ksisolucoes.vo.basico.Pessoa pessoa) {
//        br.com.ksisolucoes.vo.basico.Pessoa pessoaOld = this.pessoa;
		this.pessoa = pessoa;
//        this.getPropertyChangeSupport().firePropertyChange ("pessoa", pessoaOld, pessoa);
	}

	/**
	 * Return the value associated with the column: cd_dis_med_item
	 */
	public br.com.ksisolucoes.vo.entradas.dispensacao.DispensacaoMedicamentoItem getDispensacaoMedicamentoItem () {
		return getPropertyValue(this, dispensacaoMedicamentoItem, PROP_DISPENSACAO_MEDICAMENTO_ITEM);
	}

	/**
	 * Set the value related to the column: cd_dis_med_item
	 * @param dispensacaoMedicamentoItem the cd_dis_med_item value
	 */
	public void setDispensacaoMedicamentoItem (br.com.ksisolucoes.vo.entradas.dispensacao.DispensacaoMedicamentoItem dispensacaoMedicamentoItem) {
		this.dispensacaoMedicamentoItem = dispensacaoMedicamentoItem;
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.entradas.estoque.MovimentoEstoque)) return false;
		else {
			br.com.ksisolucoes.vo.entradas.estoque.MovimentoEstoque movimentoEstoque = (br.com.ksisolucoes.vo.entradas.estoque.MovimentoEstoque) obj;
			if (null == this.getId() || null == movimentoEstoque.getId()) return false;
			else return (this.getId().equals(movimentoEstoque.getId()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getId()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getId().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}