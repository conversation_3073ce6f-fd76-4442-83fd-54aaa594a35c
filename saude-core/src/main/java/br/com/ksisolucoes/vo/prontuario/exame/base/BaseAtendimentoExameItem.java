package br.com.ksisolucoes.vo.prontuario.exame.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the atendimento_exame_item table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="atendimento_exame_item"
 */

public abstract class BaseAtendimentoExameItem extends BaseRootVO implements Serializable {

	public static String REF = "AtendimentoExameItem";
	public static final String PROP_USUARIO = "usuario";
	public static final String PROP_COMPLEMENTO = "complemento";
	public static final String PROP_STATUS = "status";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_DATA_CADASTRO = "dataCadastro";
	public static final String PROP_CID = "cid";
	public static final String PROP_PRECO_UNITARIO = "precoUnitario";
	public static final String PROP_USUARIO_CANCELAMENTO = "usuarioCancelamento";
	public static final String PROP_USUARIO_LAUDO = "usuarioLaudo";
	public static final String PROP_ATENDIMENTO_EXAME = "atendimentoExame";
	public static final String PROP_LAUDO = "laudo";
	public static final String PROP_EXAME_REQUISICAO = "exameRequisicao";
	public static final String PROP_EXAME_PROCEDIMENTO = "exameProcedimento";
	public static final String PROP_DATA_CANCELAMENTO = "dataCancelamento";


	// constructors
	public BaseAtendimentoExameItem () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseAtendimentoExameItem (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseAtendimentoExameItem (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.prontuario.exame.AtendimentoExame atendimentoExame,
		java.lang.Long status,
		java.util.Date dataCadastro) {

		this.setCodigo(codigo);
		this.setAtendimentoExame(atendimentoExame);
		this.setStatus(status);
		this.setDataCadastro(dataCadastro);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Long status;
	private java.util.Date dataCadastro;
	private java.util.Date dataCancelamento;
	private java.lang.String complemento;
	private java.lang.Double precoUnitario;
	private java.lang.String laudo;

	// many to one
	private br.com.ksisolucoes.vo.prontuario.exame.AtendimentoExame atendimentoExame;
	private br.com.ksisolucoes.vo.prontuario.basico.ExameProcedimento exameProcedimento;
	private br.com.ksisolucoes.vo.controle.Usuario usuario;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamento;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioLaudo;
	private br.com.ksisolucoes.vo.prontuario.basico.ExameRequisicao exameRequisicao;
	private br.com.ksisolucoes.vo.prontuario.basico.Cid cid;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_ate_exa_item"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: status
	 */
	public java.lang.Long getStatus () {
		return getPropertyValue(this, status, PROP_STATUS); 
	}

	/**
	 * Set the value related to the column: status
	 * @param status the status value
	 */
	public void setStatus (java.lang.Long status) {
//        java.lang.Long statusOld = this.status;
		this.status = status;
//        this.getPropertyChangeSupport().firePropertyChange ("status", statusOld, status);
	}



	/**
	 * Return the value associated with the column: dt_cadastro
	 */
	public java.util.Date getDataCadastro () {
		return getPropertyValue(this, dataCadastro, PROP_DATA_CADASTRO); 
	}

	/**
	 * Set the value related to the column: dt_cadastro
	 * @param dataCadastro the dt_cadastro value
	 */
	public void setDataCadastro (java.util.Date dataCadastro) {
//        java.util.Date dataCadastroOld = this.dataCadastro;
		this.dataCadastro = dataCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCadastro", dataCadastroOld, dataCadastro);
	}



	/**
	 * Return the value associated with the column: dt_cancelamento
	 */
	public java.util.Date getDataCancelamento () {
		return getPropertyValue(this, dataCancelamento, PROP_DATA_CANCELAMENTO); 
	}

	/**
	 * Set the value related to the column: dt_cancelamento
	 * @param dataCancelamento the dt_cancelamento value
	 */
	public void setDataCancelamento (java.util.Date dataCancelamento) {
//        java.util.Date dataCancelamentoOld = this.dataCancelamento;
		this.dataCancelamento = dataCancelamento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCancelamento", dataCancelamentoOld, dataCancelamento);
	}



	/**
	 * Return the value associated with the column: complemento
	 */
	public java.lang.String getComplemento () {
		return getPropertyValue(this, complemento, PROP_COMPLEMENTO); 
	}

	/**
	 * Set the value related to the column: complemento
	 * @param complemento the complemento value
	 */
	public void setComplemento (java.lang.String complemento) {
//        java.lang.String complementoOld = this.complemento;
		this.complemento = complemento;
//        this.getPropertyChangeSupport().firePropertyChange ("complemento", complementoOld, complemento);
	}



	/**
	 * Return the value associated with the column: preco_unitario
	 */
	public java.lang.Double getPrecoUnitario () {
		return getPropertyValue(this, precoUnitario, PROP_PRECO_UNITARIO); 
	}

	/**
	 * Set the value related to the column: preco_unitario
	 * @param precoUnitario the preco_unitario value
	 */
	public void setPrecoUnitario (java.lang.Double precoUnitario) {
//        java.lang.Double precoUnitarioOld = this.precoUnitario;
		this.precoUnitario = precoUnitario;
//        this.getPropertyChangeSupport().firePropertyChange ("precoUnitario", precoUnitarioOld, precoUnitario);
	}



	/**
	 * Return the value associated with the column: laudo
	 */
	public java.lang.String getLaudo () {
		return getPropertyValue(this, laudo, PROP_LAUDO); 
	}

	/**
	 * Set the value related to the column: laudo
	 * @param laudo the laudo value
	 */
	public void setLaudo (java.lang.String laudo) {
//        java.lang.String laudoOld = this.laudo;
		this.laudo = laudo;
//        this.getPropertyChangeSupport().firePropertyChange ("laudo", laudoOld, laudo);
	}



	/**
	 * Return the value associated with the column: cd_atendimento_exame
	 */
	public br.com.ksisolucoes.vo.prontuario.exame.AtendimentoExame getAtendimentoExame () {
		return getPropertyValue(this, atendimentoExame, PROP_ATENDIMENTO_EXAME); 
	}

	/**
	 * Set the value related to the column: cd_atendimento_exame
	 * @param atendimentoExame the cd_atendimento_exame value
	 */
	public void setAtendimentoExame (br.com.ksisolucoes.vo.prontuario.exame.AtendimentoExame atendimentoExame) {
//        br.com.ksisolucoes.vo.prontuario.exame.AtendimentoExame atendimentoExameOld = this.atendimentoExame;
		this.atendimentoExame = atendimentoExame;
//        this.getPropertyChangeSupport().firePropertyChange ("atendimentoExame", atendimentoExameOld, atendimentoExame);
	}



	/**
	 * Return the value associated with the column: cd_exame_procedimento
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.ExameProcedimento getExameProcedimento () {
		return getPropertyValue(this, exameProcedimento, PROP_EXAME_PROCEDIMENTO); 
	}

	/**
	 * Set the value related to the column: cd_exame_procedimento
	 * @param exameProcedimento the cd_exame_procedimento value
	 */
	public void setExameProcedimento (br.com.ksisolucoes.vo.prontuario.basico.ExameProcedimento exameProcedimento) {
//        br.com.ksisolucoes.vo.prontuario.basico.ExameProcedimento exameProcedimentoOld = this.exameProcedimento;
		this.exameProcedimento = exameProcedimento;
//        this.getPropertyChangeSupport().firePropertyChange ("exameProcedimento", exameProcedimentoOld, exameProcedimento);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuario the cd_usuario value
	 */
	public void setUsuario (br.com.ksisolucoes.vo.controle.Usuario usuario) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}



	/**
	 * Return the value associated with the column: cd_usu_can
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioCancelamento () {
		return getPropertyValue(this, usuarioCancelamento, PROP_USUARIO_CANCELAMENTO); 
	}

	/**
	 * Set the value related to the column: cd_usu_can
	 * @param usuarioCancelamento the cd_usu_can value
	 */
	public void setUsuarioCancelamento (br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamento) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamentoOld = this.usuarioCancelamento;
		this.usuarioCancelamento = usuarioCancelamento;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCancelamento", usuarioCancelamentoOld, usuarioCancelamento);
	}



	/**
	 * Return the value associated with the column: cd_usu_laudo
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioLaudo () {
		return getPropertyValue(this, usuarioLaudo, PROP_USUARIO_LAUDO); 
	}

	/**
	 * Set the value related to the column: cd_usu_laudo
	 * @param usuarioLaudo the cd_usu_laudo value
	 */
	public void setUsuarioLaudo (br.com.ksisolucoes.vo.controle.Usuario usuarioLaudo) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioLaudoOld = this.usuarioLaudo;
		this.usuarioLaudo = usuarioLaudo;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioLaudo", usuarioLaudoOld, usuarioLaudo);
	}



	/**
	 * Return the value associated with the column: cd_exame_requisicao
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.ExameRequisicao getExameRequisicao () {
		return getPropertyValue(this, exameRequisicao, PROP_EXAME_REQUISICAO); 
	}

	/**
	 * Set the value related to the column: cd_exame_requisicao
	 * @param exameRequisicao the cd_exame_requisicao value
	 */
	public void setExameRequisicao (br.com.ksisolucoes.vo.prontuario.basico.ExameRequisicao exameRequisicao) {
//        br.com.ksisolucoes.vo.prontuario.basico.ExameRequisicao exameRequisicaoOld = this.exameRequisicao;
		this.exameRequisicao = exameRequisicao;
//        this.getPropertyChangeSupport().firePropertyChange ("exameRequisicao", exameRequisicaoOld, exameRequisicao);
	}



	/**
	 * Return the value associated with the column: cd_cid
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.Cid getCid () {
		return getPropertyValue(this, cid, PROP_CID); 
	}

	/**
	 * Set the value related to the column: cd_cid
	 * @param cid the cd_cid value
	 */
	public void setCid (br.com.ksisolucoes.vo.prontuario.basico.Cid cid) {
//        br.com.ksisolucoes.vo.prontuario.basico.Cid cidOld = this.cid;
		this.cid = cid;
//        this.getPropertyChangeSupport().firePropertyChange ("cid", cidOld, cid);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.exame.AtendimentoExameItem)) return false;
		else {
			br.com.ksisolucoes.vo.prontuario.exame.AtendimentoExameItem atendimentoExameItem = (br.com.ksisolucoes.vo.prontuario.exame.AtendimentoExameItem) obj;
			if (null == this.getCodigo() || null == atendimentoExameItem.getCodigo()) return false;
			else return (this.getCodigo().equals(atendimentoExameItem.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}