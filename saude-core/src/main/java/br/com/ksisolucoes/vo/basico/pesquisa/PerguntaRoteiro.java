package br.com.ksisolucoes.vo.basico.pesquisa;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.EnumUtil;
import br.com.ksisolucoes.vo.basico.pesquisa.base.BasePerguntaRoteiro;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.unidadesaude.roteiro.TipoRoteiro;

import java.io.Serializable;



public class PerguntaRoteiro extends BasePerguntaRoteiro implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public PerguntaRoteiro () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public PerguntaRoteiro (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public PerguntaRoteiro (
		java.lang.Long codigo,
		java.lang.String descricao,
		java.lang.String complemento,
		java.lang.Long tipo) {

		super (
			codigo,
			descricao,
			complemento,
			tipo);
	}

/*[CONSTRUCTOR MARKER END]*/


	public enum Tipo implements IEnum<Tipo> {
		ROTEIRO_ENFERMAGEM(0L, Bundle.getStringApplication("rotulo_roteiro_enfermagem")),
		ENTREVISTA(1L, Bundle.getStringApplication("rotulo_roteiro_entrevista")),
		EXAME_FISICO(2L, Bundle.getStringApplication("rotulo_roteiro_exame_fisico"));

		private Long value;
		private String descricao;

		private Tipo(Long value, String descricao) {
			this.value = value;
			this.descricao = descricao;
		}

		public static Tipo valueOf(Long value) {
			for (Tipo tipo : Tipo.values()) {
				if (tipo.value().equals(value)) {
					return tipo;
				}
			}
			return null;
		}

		@Override
		public Long value() {
			return value;
		}

		@Override
		public String descricao() {
			return descricao;
		}
	}

	public String getTipoPerguntaFormatado() {
		return new EnumUtil().resolveDescricao(Tipo.values(), getTipo());
	}

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}