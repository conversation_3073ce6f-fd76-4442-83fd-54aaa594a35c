<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.prontuario.enfermagem">
    <class name="AtendimentoEnfermagemIntervencaoPrescricao" table="atendimento_enfermagem_interv_prescricao">

        <id
                name="codigo"
                type="java.lang.Long"
                column="cd_atendimento_enfermagem_interv_prescricao"
        >
            <generator class="sequence">
                <param name="sequence">seq_atendimento_enfermagem_interv_prescricao</param>
            </generator>
        </id>
        <version column="version" name="version" type="long"/>

        <many-to-one
                class="br.com.ksisolucoes.vo.prontuario.enfermagem.AtendimentoEnfermagem"
                column="cd_atendimento_enfermagem"
                name="atendimentoEnfermagem"
                not-null="true"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.prontuario.enfermagem.AtendimentoEnfermagemDiagnosticoPlanejamento"
                column="cd_atendimento_enfermagem_diag_planejamento"
                name="atendimentoEnfermagemDiagnosticoPlanejamento"
                not-null="false"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.prontuario.basico.Receituario"
                name="receituario"
                column="cd_receituario"
                not-null="true"
        />

        <property
                name="prescricao"
                column="prescricao"
                type="java.lang.String"
        />

        <property
                column="publico"
                name="publico"
                not-null="true"
                type="java.lang.Long"
        />

    </class>
</hibernate-mapping>