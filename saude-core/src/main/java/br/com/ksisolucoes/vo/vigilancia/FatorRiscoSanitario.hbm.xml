<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.vigilancia">
    <class name="FatorRiscoSanitario" table="fator_risco_sanitario">
        <id
                column="cd_fator_risco_sanitario"
                name="codigo"
                type="java.lang.Long"
        >
            <generator class="sequence">
                <param name="sequence">seq_fator_risco_sanitario</param>
            </generator>
        </id>
        <version column="version" name="version" type="long"/>


        <many-to-one
                name="tabelaCnae"
                class="br.com.ksisolucoes.vo.vigilancia.TabelaCnae"
        >
            <column name="cd_cnae"/>
        </many-to-one>

        <many-to-one
                name="classificacaoRisco"
                class="br.com.ksisolucoes.vo.vigilancia.ClassificacaoGrupoEstabelecimento"
        >
            <column name="cd_classificacao_grupo_estab"/>
        </many-to-one>


        <property
                column="condicao_classificacao"
                name="condicaoClassificacao"
                length="255"
                type="java.lang.String"
        />

        <property
                column="flag_auxiliar_condicao"
                name="flagAuxiliarCondicao"
                type="java.lang.Long"
        />

        <property
                column="auxiliar_condicao_classificacao"
                name="auxiliarCondicaoClassificacao"
                length="255"
                type="java.lang.String"
        />

        <property
                column="flag_libera_anexo"
                name="flagLiberaAnexo"
                type="java.lang.Long"
        />

        <property
                column="status"
                name="status"
                type="java.lang.Long"
        />

        <property
                column="flag_aum_risco_condicao"
                name="flagAumentarRiscoCondicao"
                length="1"
                type="java.lang.String"
        />

        <property
                column="flag_aum_risco_condicao_auxiliar"
                name="flagAumentarRiscoCondicaoAuxilar"
                length="1"
                type="java.lang.String"
        />

        <many-to-one
                name="classificacaoRiscoCondicionada"
                class="br.com.ksisolucoes.vo.vigilancia.ClassificacaoGrupoEstabelecimento"
        >
            <column name="cd_classificacao_grupo_estab_cond"/>
        </many-to-one>

        <property
                column="classificacao_grupo"
                name="classificacaoGrupo"
                type="java.lang.Long"
        />

        <many-to-one
                name="classificacaoRiscoCondicionadaAux"
                class="br.com.ksisolucoes.vo.vigilancia.ClassificacaoGrupoEstabelecimento"
        >
            <column name="cd_classificacao_grupo_estab_cond_aux"/>
        </many-to-one>

        <property
                column="classificacao_grupo_aux"
                name="classificacaoGrupoAux"
                type="java.lang.Long"
        />


        <property name="dataCriacao" type="java.util.Date">
            <column name="dt_criacao"/>
        </property>

        <property name="dataAutualizacao" type="java.util.Date">
            <column name="dt_autualizacao"/>
        </property>

    </class>
</hibernate-mapping>
