package br.com.ksisolucoes.vo.siab.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the siab_ssa2 table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="siab_ssa2"
 */

public abstract class BaseSiabSsa2 extends BaseRootVO implements Serializable {

	public static String REF = "SiabSsa2";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_HOSPITALIZADOS_POR_ABUSO_ALCOOL = "hospitalizadosPorAbusoAlcool";
	public static final String PROP_HOSPITALIZADOS_POR_COMPLICACOES_DIABETES = "hospitalizadosPorComplicacoesDiabetes";
	public static final String PROP_OBITOS_ATE1_ANO_POR_OUTRAS_CAUSAS = "obitosAte1AnoPorOutrasCausas";
	public static final String PROP_PESSOAS_COM_TUBERCULOSE_ACOMPANHADAS = "pessoasComTuberculoseAcompanhadas";
	public static final String PROP_CRIANCAS0_A11_MESES = "criancas0A11Meses";
	public static final String PROP_OBITOS_MULHERES10_A14_ANOS = "obitosMulheres10A14Anos";
	public static final String PROP_CRIANCAS_COM_ATE4_MESES = "criancasComAte4Meses";
	public static final String PROP_RECEM_NASCIDOS_PESADOS_AO_NASCER = "recemNascidosPesadosAoNascer";
	public static final String PROP_DIABETICOS_CADASTRADOS = "diabeticosCadastrados";
	public static final String PROP_OBITOS28_DIAS_A1_ANO_POR_OUTRAS_CAUSAS = "obitos28DiasA1AnoPorOutrasCausas";
	public static final String PROP_CRIANCAS12_A23_MESES_COM_VACINAS_EM_DIA = "criancas12A23MesesComVacinasEmDia";
	public static final String PROP_OBITOS28_DIAS_A1_ANO_POR_DIARREIA = "obitos28DiasA1AnoPorDiarreia";
	public static final String PROP_PESSOAS_COM_HANSENIASE_CADASTRADAS = "pessoasComHanseniaseCadastradas";
	public static final String PROP_OBITOS_MENORES28_DIAS_POR_DIARREIA = "obitosMenores28DiasPorDiarreia";
	public static final String PROP_MES = "mes";
	public static final String PROP_GESTANTES_CADASTRADAS = "gestantesCadastradas";
	public static final String PROP_OUTROS_OBITOS = "outrosObitos";
	public static final String PROP_CRIANCAS_MENORES2_ANOS_TIVERAM_DIARREIA_USARAM_TRO = "criancasMenores2AnosTiveramDiarreiaUsaramTro";
	public static final String PROP_USUARIO = "usuario";
	public static final String PROP_PESSOAS_COM_HANSENIASE_ACOMPANHADAS = "pessoasComHanseniaseAcompanhadas";
	public static final String PROP_NASCIDOS_VIVOS_NO_MES = "nascidosVivosNoMes";
	public static final String PROP_INTERNACOES_EM_HOSPITAL_PSIQUIATRICO = "internacoesEmHospitalPsiquiatrico";
	public static final String PROP_CRIANCAS0_A11_MESES_PESADAS = "criancas0A11MesesPesadas";
	public static final String PROP_HOSPITALIZADOS_MENORES5_ANOS_POR_PNEUMONIA = "hospitalizadosMenores5AnosPorPneumonia";
	public static final String PROP_CRIANCAS_COM_ATE4_MESES_ALEITAMENTO_MISTO = "criancasComAte4MesesAleitamentoMisto";
	public static final String PROP_DIABETICOS_ACOMPANHADOS = "diabeticosAcompanhados";
	public static final String PROP_HIPERTENSOS_ACOMPANHADOS = "hipertensosAcompanhados";
	public static final String PROP_GESTANTES_COM_VACINAS_EM_DIA = "gestantesComVacinasEmDia";
	public static final String PROP_ID_MODELO = "idModelo";
	public static final String PROP_GESTANTES_MENORES20_ANOS = "gestantesMenores20Anos";
	public static final String PROP_ANO = "ano";
	public static final String PROP_OBITOS_MENORES28_DIAS_POR_OUTRAS_CAUSAS = "obitosMenores28DiasPorOutrasCausas";
	public static final String PROP_HOSPITALIZADOS_MENORES5_ANOS_POR_DESIDRATACAO = "hospitalizadosMenores5AnosPorDesidratacao";
	public static final String PROP_OBITOS28_DIAS_A1_ANO_POR_INFECCAO_RESPIRATORIA = "obitos28DiasA1AnoPorInfeccaoRespiratoria";
	public static final String PROP_GESTANTES_COM_PRE_NATAL_PRIMEIRO_TRI = "gestantesComPreNatalPrimeiroTri";
	public static final String PROP_GESTANTES_ACOMPANHADAS = "gestantesAcompanhadas";
	public static final String PROP_DATA_USUARIO = "dataUsuario";
	public static final String PROP_HOSPITALIZADOS_POR_OUTRAS_CAUSAS = "hospitalizadosPorOutrasCausas";
	public static final String PROP_HIPERTENSOS_CADASTRADOS = "hipertensosCadastrados";
	public static final String PROP_CRIANCAS12_A23_MESES_DESNUTRIDAS = "criancas12A23MesesDesnutridas";
	public static final String PROP_CRIANCAS_MENORES2_ANOS_TIVERAM_DIARREIA = "criancasMenores2AnosTiveramDiarreia";
	public static final String PROP_EQUIPE_PROFISSIONAL = "equipeProfissional";
	public static final String PROP_CRIANCAS12_A23_MESES = "criancas12A23Meses";
	public static final String PROP_CRIANCAS0_A11_MESES_DESNUTRIDAS = "criancas0A11MesesDesnutridas";
	public static final String PROP_OBITOS_MULHERES15_A49_ANOS = "obitosMulheres15A49Anos";
	public static final String PROP_OBITOS_ADOLESCENTES10_A19_ANOS_POR_VIOLENCIA = "obitosAdolescentes10A19AnosPorViolencia";
	public static final String PROP_VISITAS_DOMICILIARES = "visitasDomiciliares";
	public static final String PROP_OBITOS_ATE1_ANO_POR_INFECCAO_RESPIRATORIA = "obitosAte1AnoPorInfeccaoRespiratoria";
	public static final String PROP_CRIANCAS_COM_ATE4_MESES_ALEITAMENTO_EXCLUSIVO = "criancasComAte4MesesAleitamentoExclusivo";
	public static final String PROP_CRIANCAS_MENORES2_ANOS_TIVERAM_INFECCAO_RESPIRATORIA_AGUDA = "criancasMenores2AnosTiveramInfeccaoRespiratoriaAguda";
	public static final String PROP_STATUS = "status";
	public static final String PROP_RECEM_NASCIDOS_PESADOS_AO_NASCER_COM_MENOS2500G = "recemNascidosPesadosAoNascerComMenos2500g";
	public static final String PROP_SIGAB = "sigab";
	public static final String PROP_CRIANCAS12_A23_MESES_PESADAS = "criancas12A23MesesPesadas";
	public static final String PROP_CRIANCAS0_A11_MESES_COM_VACINAS_EM_DIA = "criancas0A11MesesComVacinasEmDia";
	public static final String PROP_GESTANTES_COM_PRE_NATAL_DO_MES = "gestantesComPreNatalDoMes";
	public static final String PROP_PESSOAS_COM_TUBERCULOSE_CADASTRADAS = "pessoasComTuberculoseCadastradas";
	public static final String PROP_TOTAL_FAMILIAS_CADASTRADAS = "totalFamiliasCadastradas";
	public static final String PROP_OBITOS_ATE1_ANO_POR_DIARREIA = "obitosAte1AnoPorDiarreia";
	public static final String PROP_OBITOS_MENORES28_DIAS_POR_INFECCAO_RESPIRATORIA = "obitosMenores28DiasPorInfeccaoRespiratoria";


	// constructors
	public BaseSiabSsa2 () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseSiabSsa2 (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseSiabSsa2 (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		java.lang.Long mes,
		java.lang.Long ano,
		java.util.Date dataUsuario,
		java.lang.Long status) {

		this.setCodigo(codigo);
		this.setUsuario(usuario);
		this.setMes(mes);
		this.setAno(ano);
		this.setDataUsuario(dataUsuario);
		this.setStatus(status);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Long mes;
	private java.lang.Long ano;
	private java.util.Date dataUsuario;
	private java.lang.Long status;
	private java.lang.String idModelo;
	private java.lang.Long totalFamiliasCadastradas;
	private java.lang.Long visitasDomiciliares;
	private java.lang.Long gestantesCadastradas;
	private java.lang.Long gestantesMenores20Anos;
	private java.lang.Long gestantesAcompanhadas;
	private java.lang.Long gestantesComVacinasEmDia;
	private java.lang.Long gestantesComPreNatalDoMes;
	private java.lang.Long gestantesComPreNatalPrimeiroTri;
	private java.lang.Long criancasComAte4Meses;
	private java.lang.Long criancasComAte4MesesAleitamentoExclusivo;
	private java.lang.Long criancasComAte4MesesAleitamentoMisto;
	private java.lang.Long criancas0A11Meses;
	private java.lang.Long criancas0A11MesesComVacinasEmDia;
	private java.lang.Long criancas0A11MesesPesadas;
	private java.lang.Long criancas0A11MesesDesnutridas;
	private java.lang.Long criancas12A23Meses;
	private java.lang.Long criancas12A23MesesComVacinasEmDia;
	private java.lang.Long criancas12A23MesesPesadas;
	private java.lang.Long criancas12A23MesesDesnutridas;
	private java.lang.Long criancasMenores2AnosTiveramDiarreia;
	private java.lang.Long criancasMenores2AnosTiveramDiarreiaUsaramTro;
	private java.lang.Long criancasMenores2AnosTiveramInfeccaoRespiratoriaAguda;
	private java.lang.Long nascidosVivosNoMes;
	private java.lang.Long recemNascidosPesadosAoNascer;
	private java.lang.Long recemNascidosPesadosAoNascerComMenos2500g;
	private java.lang.Long obitosMenores28DiasPorDiarreia;
	private java.lang.Long obitosMenores28DiasPorInfeccaoRespiratoria;
	private java.lang.Long obitosMenores28DiasPorOutrasCausas;
	private java.lang.Long obitos28DiasA1AnoPorDiarreia;
	private java.lang.Long obitos28DiasA1AnoPorInfeccaoRespiratoria;
	private java.lang.Long obitos28DiasA1AnoPorOutrasCausas;
	private java.lang.Long obitosAte1AnoPorDiarreia;
	private java.lang.Long obitosAte1AnoPorInfeccaoRespiratoria;
	private java.lang.Long obitosAte1AnoPorOutrasCausas;
	private java.lang.Long obitosMulheres10A14Anos;
	private java.lang.Long obitosMulheres15A49Anos;
	private java.lang.Long obitosAdolescentes10A19AnosPorViolencia;
	private java.lang.Long outrosObitos;
	private java.lang.Long diabeticosCadastrados;
	private java.lang.Long diabeticosAcompanhados;
	private java.lang.Long hipertensosCadastrados;
	private java.lang.Long hipertensosAcompanhados;
	private java.lang.Long pessoasComTuberculoseCadastradas;
	private java.lang.Long pessoasComTuberculoseAcompanhadas;
	private java.lang.Long pessoasComHanseniaseCadastradas;
	private java.lang.Long pessoasComHanseniaseAcompanhadas;
	private java.lang.Long hospitalizadosMenores5AnosPorPneumonia;
	private java.lang.Long hospitalizadosMenores5AnosPorDesidratacao;
	private java.lang.Long hospitalizadosPorAbusoAlcool;
	private java.lang.Long internacoesEmHospitalPsiquiatrico;
	private java.lang.Long hospitalizadosPorComplicacoesDiabetes;
	private java.lang.Long hospitalizadosPorOutrasCausas;
	private java.lang.String sigab;

	// many to one
	private br.com.ksisolucoes.vo.basico.EquipeProfissional equipeProfissional;
	private br.com.ksisolucoes.vo.controle.Usuario usuario;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_siab_ssa2"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: mes
	 */
	public java.lang.Long getMes () {
		return getPropertyValue(this, mes, PROP_MES); 
	}

	/**
	 * Set the value related to the column: mes
	 * @param mes the mes value
	 */
	public void setMes (java.lang.Long mes) {
//        java.lang.Long mesOld = this.mes;
		this.mes = mes;
//        this.getPropertyChangeSupport().firePropertyChange ("mes", mesOld, mes);
	}



	/**
	 * Return the value associated with the column: ano
	 */
	public java.lang.Long getAno () {
		return getPropertyValue(this, ano, PROP_ANO); 
	}

	/**
	 * Set the value related to the column: ano
	 * @param ano the ano value
	 */
	public void setAno (java.lang.Long ano) {
//        java.lang.Long anoOld = this.ano;
		this.ano = ano;
//        this.getPropertyChangeSupport().firePropertyChange ("ano", anoOld, ano);
	}



	/**
	 * Return the value associated with the column: dt_usuario
	 */
	public java.util.Date getDataUsuario () {
		return getPropertyValue(this, dataUsuario, PROP_DATA_USUARIO); 
	}

	/**
	 * Set the value related to the column: dt_usuario
	 * @param dataUsuario the dt_usuario value
	 */
	public void setDataUsuario (java.util.Date dataUsuario) {
//        java.util.Date dataUsuarioOld = this.dataUsuario;
		this.dataUsuario = dataUsuario;
//        this.getPropertyChangeSupport().firePropertyChange ("dataUsuario", dataUsuarioOld, dataUsuario);
	}



	/**
	 * Return the value associated with the column: status
	 */
	public java.lang.Long getStatus () {
		return getPropertyValue(this, status, PROP_STATUS); 
	}

	/**
	 * Set the value related to the column: status
	 * @param status the status value
	 */
	public void setStatus (java.lang.Long status) {
//        java.lang.Long statusOld = this.status;
		this.status = status;
//        this.getPropertyChangeSupport().firePropertyChange ("status", statusOld, status);
	}



	/**
	 * Return the value associated with the column: id_modelo
	 */
	public java.lang.String getIdModelo () {
		return getPropertyValue(this, idModelo, PROP_ID_MODELO); 
	}

	/**
	 * Set the value related to the column: id_modelo
	 * @param idModelo the id_modelo value
	 */
	public void setIdModelo (java.lang.String idModelo) {
//        java.lang.String idModeloOld = this.idModelo;
		this.idModelo = idModelo;
//        this.getPropertyChangeSupport().firePropertyChange ("idModelo", idModeloOld, idModelo);
	}



	/**
	 * Return the value associated with the column: NFAMICAD
	 */
	public java.lang.Long getTotalFamiliasCadastradas () {
		return getPropertyValue(this, totalFamiliasCadastradas, PROP_TOTAL_FAMILIAS_CADASTRADAS); 
	}

	/**
	 * Set the value related to the column: NFAMICAD
	 * @param totalFamiliasCadastradas the NFAMICAD value
	 */
	public void setTotalFamiliasCadastradas (java.lang.Long totalFamiliasCadastradas) {
//        java.lang.Long totalFamiliasCadastradasOld = this.totalFamiliasCadastradas;
		this.totalFamiliasCadastradas = totalFamiliasCadastradas;
//        this.getPropertyChangeSupport().firePropertyChange ("totalFamiliasCadastradas", totalFamiliasCadastradasOld, totalFamiliasCadastradas);
	}



	/**
	 * Return the value associated with the column: NVISITAS
	 */
	public java.lang.Long getVisitasDomiciliares () {
		return getPropertyValue(this, visitasDomiciliares, PROP_VISITAS_DOMICILIARES); 
	}

	/**
	 * Set the value related to the column: NVISITAS
	 * @param visitasDomiciliares the NVISITAS value
	 */
	public void setVisitasDomiciliares (java.lang.Long visitasDomiciliares) {
//        java.lang.Long visitasDomiciliaresOld = this.visitasDomiciliares;
		this.visitasDomiciliares = visitasDomiciliares;
//        this.getPropertyChangeSupport().firePropertyChange ("visitasDomiciliares", visitasDomiciliaresOld, visitasDomiciliares);
	}



	/**
	 * Return the value associated with the column: NGESCAD
	 */
	public java.lang.Long getGestantesCadastradas () {
		return getPropertyValue(this, gestantesCadastradas, PROP_GESTANTES_CADASTRADAS); 
	}

	/**
	 * Set the value related to the column: NGESCAD
	 * @param gestantesCadastradas the NGESCAD value
	 */
	public void setGestantesCadastradas (java.lang.Long gestantesCadastradas) {
//        java.lang.Long gestantesCadastradasOld = this.gestantesCadastradas;
		this.gestantesCadastradas = gestantesCadastradas;
//        this.getPropertyChangeSupport().firePropertyChange ("gestantesCadastradas", gestantesCadastradasOld, gestantesCadastradas);
	}



	/**
	 * Return the value associated with the column: NGESM20
	 */
	public java.lang.Long getGestantesMenores20Anos () {
		return getPropertyValue(this, gestantesMenores20Anos, PROP_GESTANTES_MENORES20_ANOS); 
	}

	/**
	 * Set the value related to the column: NGESM20
	 * @param gestantesMenores20Anos the NGESM20 value
	 */
	public void setGestantesMenores20Anos (java.lang.Long gestantesMenores20Anos) {
//        java.lang.Long gestantesMenores20AnosOld = this.gestantesMenores20Anos;
		this.gestantesMenores20Anos = gestantesMenores20Anos;
//        this.getPropertyChangeSupport().firePropertyChange ("gestantesMenores20Anos", gestantesMenores20AnosOld, gestantesMenores20Anos);
	}



	/**
	 * Return the value associated with the column: NGESAC
	 */
	public java.lang.Long getGestantesAcompanhadas () {
		return getPropertyValue(this, gestantesAcompanhadas, PROP_GESTANTES_ACOMPANHADAS); 
	}

	/**
	 * Set the value related to the column: NGESAC
	 * @param gestantesAcompanhadas the NGESAC value
	 */
	public void setGestantesAcompanhadas (java.lang.Long gestantesAcompanhadas) {
//        java.lang.Long gestantesAcompanhadasOld = this.gestantesAcompanhadas;
		this.gestantesAcompanhadas = gestantesAcompanhadas;
//        this.getPropertyChangeSupport().firePropertyChange ("gestantesAcompanhadas", gestantesAcompanhadasOld, gestantesAcompanhadas);
	}



	/**
	 * Return the value associated with the column: NGESVAC
	 */
	public java.lang.Long getGestantesComVacinasEmDia () {
		return getPropertyValue(this, gestantesComVacinasEmDia, PROP_GESTANTES_COM_VACINAS_EM_DIA); 
	}

	/**
	 * Set the value related to the column: NGESVAC
	 * @param gestantesComVacinasEmDia the NGESVAC value
	 */
	public void setGestantesComVacinasEmDia (java.lang.Long gestantesComVacinasEmDia) {
//        java.lang.Long gestantesComVacinasEmDiaOld = this.gestantesComVacinasEmDia;
		this.gestantesComVacinasEmDia = gestantesComVacinasEmDia;
//        this.getPropertyChangeSupport().firePropertyChange ("gestantesComVacinasEmDia", gestantesComVacinasEmDiaOld, gestantesComVacinasEmDia);
	}



	/**
	 * Return the value associated with the column: NGESPRE1
	 */
	public java.lang.Long getGestantesComPreNatalDoMes () {
		return getPropertyValue(this, gestantesComPreNatalDoMes, PROP_GESTANTES_COM_PRE_NATAL_DO_MES); 
	}

	/**
	 * Set the value related to the column: NGESPRE1
	 * @param gestantesComPreNatalDoMes the NGESPRE1 value
	 */
	public void setGestantesComPreNatalDoMes (java.lang.Long gestantesComPreNatalDoMes) {
//        java.lang.Long gestantesComPreNatalDoMesOld = this.gestantesComPreNatalDoMes;
		this.gestantesComPreNatalDoMes = gestantesComPreNatalDoMes;
//        this.getPropertyChangeSupport().firePropertyChange ("gestantesComPreNatalDoMes", gestantesComPreNatalDoMesOld, gestantesComPreNatalDoMes);
	}



	/**
	 * Return the value associated with the column: NGESPRE2
	 */
	public java.lang.Long getGestantesComPreNatalPrimeiroTri () {
		return getPropertyValue(this, gestantesComPreNatalPrimeiroTri, PROP_GESTANTES_COM_PRE_NATAL_PRIMEIRO_TRI); 
	}

	/**
	 * Set the value related to the column: NGESPRE2
	 * @param gestantesComPreNatalPrimeiroTri the NGESPRE2 value
	 */
	public void setGestantesComPreNatalPrimeiroTri (java.lang.Long gestantesComPreNatalPrimeiroTri) {
//        java.lang.Long gestantesComPreNatalPrimeiroTriOld = this.gestantesComPreNatalPrimeiroTri;
		this.gestantesComPreNatalPrimeiroTri = gestantesComPreNatalPrimeiroTri;
//        this.getPropertyChangeSupport().firePropertyChange ("gestantesComPreNatalPrimeiroTri", gestantesComPreNatalPrimeiroTriOld, gestantesComPreNatalPrimeiroTri);
	}



	/**
	 * Return the value associated with the column: C_4MESES
	 */
	public java.lang.Long getCriancasComAte4Meses () {
		return getPropertyValue(this, criancasComAte4Meses, PROP_CRIANCAS_COM_ATE4_MESES); 
	}

	/**
	 * Set the value related to the column: C_4MESES
	 * @param criancasComAte4Meses the C_4MESES value
	 */
	public void setCriancasComAte4Meses (java.lang.Long criancasComAte4Meses) {
//        java.lang.Long criancasComAte4MesesOld = this.criancasComAte4Meses;
		this.criancasComAte4Meses = criancasComAte4Meses;
//        this.getPropertyChangeSupport().firePropertyChange ("criancasComAte4Meses", criancasComAte4MesesOld, criancasComAte4Meses);
	}



	/**
	 * Return the value associated with the column: C_MAMAND
	 */
	public java.lang.Long getCriancasComAte4MesesAleitamentoExclusivo () {
		return getPropertyValue(this, criancasComAte4MesesAleitamentoExclusivo, PROP_CRIANCAS_COM_ATE4_MESES_ALEITAMENTO_EXCLUSIVO); 
	}

	/**
	 * Set the value related to the column: C_MAMAND
	 * @param criancasComAte4MesesAleitamentoExclusivo the C_MAMAND value
	 */
	public void setCriancasComAte4MesesAleitamentoExclusivo (java.lang.Long criancasComAte4MesesAleitamentoExclusivo) {
//        java.lang.Long criancasComAte4MesesAleitamentoExclusivoOld = this.criancasComAte4MesesAleitamentoExclusivo;
		this.criancasComAte4MesesAleitamentoExclusivo = criancasComAte4MesesAleitamentoExclusivo;
//        this.getPropertyChangeSupport().firePropertyChange ("criancasComAte4MesesAleitamentoExclusivo", criancasComAte4MesesAleitamentoExclusivoOld, criancasComAte4MesesAleitamentoExclusivo);
	}



	/**
	 * Return the value associated with the column: C_MISTO
	 */
	public java.lang.Long getCriancasComAte4MesesAleitamentoMisto () {
		return getPropertyValue(this, criancasComAte4MesesAleitamentoMisto, PROP_CRIANCAS_COM_ATE4_MESES_ALEITAMENTO_MISTO); 
	}

	/**
	 * Set the value related to the column: C_MISTO
	 * @param criancasComAte4MesesAleitamentoMisto the C_MISTO value
	 */
	public void setCriancasComAte4MesesAleitamentoMisto (java.lang.Long criancasComAte4MesesAleitamentoMisto) {
//        java.lang.Long criancasComAte4MesesAleitamentoMistoOld = this.criancasComAte4MesesAleitamentoMisto;
		this.criancasComAte4MesesAleitamentoMisto = criancasComAte4MesesAleitamentoMisto;
//        this.getPropertyChangeSupport().firePropertyChange ("criancasComAte4MesesAleitamentoMisto", criancasComAte4MesesAleitamentoMistoOld, criancasComAte4MesesAleitamentoMisto);
	}



	/**
	 * Return the value associated with the column: C_0A11
	 */
	public java.lang.Long getCriancas0A11Meses () {
		return getPropertyValue(this, criancas0A11Meses, PROP_CRIANCAS0_A11_MESES); 
	}

	/**
	 * Set the value related to the column: C_0A11
	 * @param criancas0A11Meses the C_0A11 value
	 */
	public void setCriancas0A11Meses (java.lang.Long criancas0A11Meses) {
//        java.lang.Long criancas0A11MesesOld = this.criancas0A11Meses;
		this.criancas0A11Meses = criancas0A11Meses;
//        this.getPropertyChangeSupport().firePropertyChange ("criancas0A11Meses", criancas0A11MesesOld, criancas0A11Meses);
	}



	/**
	 * Return the value associated with the column: C_VACDIA
	 */
	public java.lang.Long getCriancas0A11MesesComVacinasEmDia () {
		return getPropertyValue(this, criancas0A11MesesComVacinasEmDia, PROP_CRIANCAS0_A11_MESES_COM_VACINAS_EM_DIA); 
	}

	/**
	 * Set the value related to the column: C_VACDIA
	 * @param criancas0A11MesesComVacinasEmDia the C_VACDIA value
	 */
	public void setCriancas0A11MesesComVacinasEmDia (java.lang.Long criancas0A11MesesComVacinasEmDia) {
//        java.lang.Long criancas0A11MesesComVacinasEmDiaOld = this.criancas0A11MesesComVacinasEmDia;
		this.criancas0A11MesesComVacinasEmDia = criancas0A11MesesComVacinasEmDia;
//        this.getPropertyChangeSupport().firePropertyChange ("criancas0A11MesesComVacinasEmDia", criancas0A11MesesComVacinasEmDiaOld, criancas0A11MesesComVacinasEmDia);
	}



	/**
	 * Return the value associated with the column: C_0A112P
	 */
	public java.lang.Long getCriancas0A11MesesPesadas () {
		return getPropertyValue(this, criancas0A11MesesPesadas, PROP_CRIANCAS0_A11_MESES_PESADAS); 
	}

	/**
	 * Set the value related to the column: C_0A112P
	 * @param criancas0A11MesesPesadas the C_0A112P value
	 */
	public void setCriancas0A11MesesPesadas (java.lang.Long criancas0A11MesesPesadas) {
//        java.lang.Long criancas0A11MesesPesadasOld = this.criancas0A11MesesPesadas;
		this.criancas0A11MesesPesadas = criancas0A11MesesPesadas;
//        this.getPropertyChangeSupport().firePropertyChange ("criancas0A11MesesPesadas", criancas0A11MesesPesadasOld, criancas0A11MesesPesadas);
	}



	/**
	 * Return the value associated with the column: C_0A11GP
	 */
	public java.lang.Long getCriancas0A11MesesDesnutridas () {
		return getPropertyValue(this, criancas0A11MesesDesnutridas, PROP_CRIANCAS0_A11_MESES_DESNUTRIDAS); 
	}

	/**
	 * Set the value related to the column: C_0A11GP
	 * @param criancas0A11MesesDesnutridas the C_0A11GP value
	 */
	public void setCriancas0A11MesesDesnutridas (java.lang.Long criancas0A11MesesDesnutridas) {
//        java.lang.Long criancas0A11MesesDesnutridasOld = this.criancas0A11MesesDesnutridas;
		this.criancas0A11MesesDesnutridas = criancas0A11MesesDesnutridas;
//        this.getPropertyChangeSupport().firePropertyChange ("criancas0A11MesesDesnutridas", criancas0A11MesesDesnutridasOld, criancas0A11MesesDesnutridas);
	}



	/**
	 * Return the value associated with the column: C_1223
	 */
	public java.lang.Long getCriancas12A23Meses () {
		return getPropertyValue(this, criancas12A23Meses, PROP_CRIANCAS12_A23_MESES); 
	}

	/**
	 * Set the value related to the column: C_1223
	 * @param criancas12A23Meses the C_1223 value
	 */
	public void setCriancas12A23Meses (java.lang.Long criancas12A23Meses) {
//        java.lang.Long criancas12A23MesesOld = this.criancas12A23Meses;
		this.criancas12A23Meses = criancas12A23Meses;
//        this.getPropertyChangeSupport().firePropertyChange ("criancas12A23Meses", criancas12A23MesesOld, criancas12A23Meses);
	}



	/**
	 * Return the value associated with the column: C_VACINA
	 */
	public java.lang.Long getCriancas12A23MesesComVacinasEmDia () {
		return getPropertyValue(this, criancas12A23MesesComVacinasEmDia, PROP_CRIANCAS12_A23_MESES_COM_VACINAS_EM_DIA); 
	}

	/**
	 * Set the value related to the column: C_VACINA
	 * @param criancas12A23MesesComVacinasEmDia the C_VACINA value
	 */
	public void setCriancas12A23MesesComVacinasEmDia (java.lang.Long criancas12A23MesesComVacinasEmDia) {
//        java.lang.Long criancas12A23MesesComVacinasEmDiaOld = this.criancas12A23MesesComVacinasEmDia;
		this.criancas12A23MesesComVacinasEmDia = criancas12A23MesesComVacinasEmDia;
//        this.getPropertyChangeSupport().firePropertyChange ("criancas12A23MesesComVacinasEmDia", criancas12A23MesesComVacinasEmDiaOld, criancas12A23MesesComVacinasEmDia);
	}



	/**
	 * Return the value associated with the column: C_12232P
	 */
	public java.lang.Long getCriancas12A23MesesPesadas () {
		return getPropertyValue(this, criancas12A23MesesPesadas, PROP_CRIANCAS12_A23_MESES_PESADAS); 
	}

	/**
	 * Set the value related to the column: C_12232P
	 * @param criancas12A23MesesPesadas the C_12232P value
	 */
	public void setCriancas12A23MesesPesadas (java.lang.Long criancas12A23MesesPesadas) {
//        java.lang.Long criancas12A23MesesPesadasOld = this.criancas12A23MesesPesadas;
		this.criancas12A23MesesPesadas = criancas12A23MesesPesadas;
//        this.getPropertyChangeSupport().firePropertyChange ("criancas12A23MesesPesadas", criancas12A23MesesPesadasOld, criancas12A23MesesPesadas);
	}



	/**
	 * Return the value associated with the column: C_1223GP
	 */
	public java.lang.Long getCriancas12A23MesesDesnutridas () {
		return getPropertyValue(this, criancas12A23MesesDesnutridas, PROP_CRIANCAS12_A23_MESES_DESNUTRIDAS); 
	}

	/**
	 * Set the value related to the column: C_1223GP
	 * @param criancas12A23MesesDesnutridas the C_1223GP value
	 */
	public void setCriancas12A23MesesDesnutridas (java.lang.Long criancas12A23MesesDesnutridas) {
//        java.lang.Long criancas12A23MesesDesnutridasOld = this.criancas12A23MesesDesnutridas;
		this.criancas12A23MesesDesnutridas = criancas12A23MesesDesnutridas;
//        this.getPropertyChangeSupport().firePropertyChange ("criancas12A23MesesDesnutridas", criancas12A23MesesDesnutridasOld, criancas12A23MesesDesnutridas);
	}



	/**
	 * Return the value associated with the column: C_DIARRE
	 */
	public java.lang.Long getCriancasMenores2AnosTiveramDiarreia () {
		return getPropertyValue(this, criancasMenores2AnosTiveramDiarreia, PROP_CRIANCAS_MENORES2_ANOS_TIVERAM_DIARREIA); 
	}

	/**
	 * Set the value related to the column: C_DIARRE
	 * @param criancasMenores2AnosTiveramDiarreia the C_DIARRE value
	 */
	public void setCriancasMenores2AnosTiveramDiarreia (java.lang.Long criancasMenores2AnosTiveramDiarreia) {
//        java.lang.Long criancasMenores2AnosTiveramDiarreiaOld = this.criancasMenores2AnosTiveramDiarreia;
		this.criancasMenores2AnosTiveramDiarreia = criancasMenores2AnosTiveramDiarreia;
//        this.getPropertyChangeSupport().firePropertyChange ("criancasMenores2AnosTiveramDiarreia", criancasMenores2AnosTiveramDiarreiaOld, criancasMenores2AnosTiveramDiarreia);
	}



	/**
	 * Return the value associated with the column: C_DIASRO
	 */
	public java.lang.Long getCriancasMenores2AnosTiveramDiarreiaUsaramTro () {
		return getPropertyValue(this, criancasMenores2AnosTiveramDiarreiaUsaramTro, PROP_CRIANCAS_MENORES2_ANOS_TIVERAM_DIARREIA_USARAM_TRO); 
	}

	/**
	 * Set the value related to the column: C_DIASRO
	 * @param criancasMenores2AnosTiveramDiarreiaUsaramTro the C_DIASRO value
	 */
	public void setCriancasMenores2AnosTiveramDiarreiaUsaramTro (java.lang.Long criancasMenores2AnosTiveramDiarreiaUsaramTro) {
//        java.lang.Long criancasMenores2AnosTiveramDiarreiaUsaramTroOld = this.criancasMenores2AnosTiveramDiarreiaUsaramTro;
		this.criancasMenores2AnosTiveramDiarreiaUsaramTro = criancasMenores2AnosTiveramDiarreiaUsaramTro;
//        this.getPropertyChangeSupport().firePropertyChange ("criancasMenores2AnosTiveramDiarreiaUsaramTro", criancasMenores2AnosTiveramDiarreiaUsaramTroOld, criancasMenores2AnosTiveramDiarreiaUsaramTro);
	}



	/**
	 * Return the value associated with the column: C_IRA
	 */
	public java.lang.Long getCriancasMenores2AnosTiveramInfeccaoRespiratoriaAguda () {
		return getPropertyValue(this, criancasMenores2AnosTiveramInfeccaoRespiratoriaAguda, PROP_CRIANCAS_MENORES2_ANOS_TIVERAM_INFECCAO_RESPIRATORIA_AGUDA); 
	}

	/**
	 * Set the value related to the column: C_IRA
	 * @param criancasMenores2AnosTiveramInfeccaoRespiratoriaAguda the C_IRA value
	 */
	public void setCriancasMenores2AnosTiveramInfeccaoRespiratoriaAguda (java.lang.Long criancasMenores2AnosTiveramInfeccaoRespiratoriaAguda) {
//        java.lang.Long criancasMenores2AnosTiveramInfeccaoRespiratoriaAgudaOld = this.criancasMenores2AnosTiveramInfeccaoRespiratoriaAguda;
		this.criancasMenores2AnosTiveramInfeccaoRespiratoriaAguda = criancasMenores2AnosTiveramInfeccaoRespiratoriaAguda;
//        this.getPropertyChangeSupport().firePropertyChange ("criancasMenores2AnosTiveramInfeccaoRespiratoriaAguda", criancasMenores2AnosTiveramInfeccaoRespiratoriaAgudaOld, criancasMenores2AnosTiveramInfeccaoRespiratoriaAguda);
	}



	/**
	 * Return the value associated with the column: NASCVIVO
	 */
	public java.lang.Long getNascidosVivosNoMes () {
		return getPropertyValue(this, nascidosVivosNoMes, PROP_NASCIDOS_VIVOS_NO_MES); 
	}

	/**
	 * Set the value related to the column: NASCVIVO
	 * @param nascidosVivosNoMes the NASCVIVO value
	 */
	public void setNascidosVivosNoMes (java.lang.Long nascidosVivosNoMes) {
//        java.lang.Long nascidosVivosNoMesOld = this.nascidosVivosNoMes;
		this.nascidosVivosNoMes = nascidosVivosNoMes;
//        this.getPropertyChangeSupport().firePropertyChange ("nascidosVivosNoMes", nascidosVivosNoMesOld, nascidosVivosNoMes);
	}



	/**
	 * Return the value associated with the column: PESADOS
	 */
	public java.lang.Long getRecemNascidosPesadosAoNascer () {
		return getPropertyValue(this, recemNascidosPesadosAoNascer, PROP_RECEM_NASCIDOS_PESADOS_AO_NASCER); 
	}

	/**
	 * Set the value related to the column: PESADOS
	 * @param recemNascidosPesadosAoNascer the PESADOS value
	 */
	public void setRecemNascidosPesadosAoNascer (java.lang.Long recemNascidosPesadosAoNascer) {
//        java.lang.Long recemNascidosPesadosAoNascerOld = this.recemNascidosPesadosAoNascer;
		this.recemNascidosPesadosAoNascer = recemNascidosPesadosAoNascer;
//        this.getPropertyChangeSupport().firePropertyChange ("recemNascidosPesadosAoNascer", recemNascidosPesadosAoNascerOld, recemNascidosPesadosAoNascer);
	}



	/**
	 * Return the value associated with the column: PESO2500
	 */
	public java.lang.Long getRecemNascidosPesadosAoNascerComMenos2500g () {
		return getPropertyValue(this, recemNascidosPesadosAoNascerComMenos2500g, PROP_RECEM_NASCIDOS_PESADOS_AO_NASCER_COM_MENOS2500G); 
	}

	/**
	 * Set the value related to the column: PESO2500
	 * @param recemNascidosPesadosAoNascerComMenos2500g the PESO2500 value
	 */
	public void setRecemNascidosPesadosAoNascerComMenos2500g (java.lang.Long recemNascidosPesadosAoNascerComMenos2500g) {
//        java.lang.Long recemNascidosPesadosAoNascerComMenos2500gOld = this.recemNascidosPesadosAoNascerComMenos2500g;
		this.recemNascidosPesadosAoNascerComMenos2500g = recemNascidosPesadosAoNascerComMenos2500g;
//        this.getPropertyChangeSupport().firePropertyChange ("recemNascidosPesadosAoNascerComMenos2500g", recemNascidosPesadosAoNascerComMenos2500gOld, recemNascidosPesadosAoNascerComMenos2500g);
	}



	/**
	 * Return the value associated with the column: O_DIA0A28
	 */
	public java.lang.Long getObitosMenores28DiasPorDiarreia () {
		return getPropertyValue(this, obitosMenores28DiasPorDiarreia, PROP_OBITOS_MENORES28_DIAS_POR_DIARREIA); 
	}

	/**
	 * Set the value related to the column: O_DIA0A28
	 * @param obitosMenores28DiasPorDiarreia the O_DIA0A28 value
	 */
	public void setObitosMenores28DiasPorDiarreia (java.lang.Long obitosMenores28DiasPorDiarreia) {
//        java.lang.Long obitosMenores28DiasPorDiarreiaOld = this.obitosMenores28DiasPorDiarreia;
		this.obitosMenores28DiasPorDiarreia = obitosMenores28DiasPorDiarreia;
//        this.getPropertyChangeSupport().firePropertyChange ("obitosMenores28DiasPorDiarreia", obitosMenores28DiasPorDiarreiaOld, obitosMenores28DiasPorDiarreia);
	}



	/**
	 * Return the value associated with the column: O_IRA0A28
	 */
	public java.lang.Long getObitosMenores28DiasPorInfeccaoRespiratoria () {
		return getPropertyValue(this, obitosMenores28DiasPorInfeccaoRespiratoria, PROP_OBITOS_MENORES28_DIAS_POR_INFECCAO_RESPIRATORIA); 
	}

	/**
	 * Set the value related to the column: O_IRA0A28
	 * @param obitosMenores28DiasPorInfeccaoRespiratoria the O_IRA0A28 value
	 */
	public void setObitosMenores28DiasPorInfeccaoRespiratoria (java.lang.Long obitosMenores28DiasPorInfeccaoRespiratoria) {
//        java.lang.Long obitosMenores28DiasPorInfeccaoRespiratoriaOld = this.obitosMenores28DiasPorInfeccaoRespiratoria;
		this.obitosMenores28DiasPorInfeccaoRespiratoria = obitosMenores28DiasPorInfeccaoRespiratoria;
//        this.getPropertyChangeSupport().firePropertyChange ("obitosMenores28DiasPorInfeccaoRespiratoria", obitosMenores28DiasPorInfeccaoRespiratoriaOld, obitosMenores28DiasPorInfeccaoRespiratoria);
	}



	/**
	 * Return the value associated with the column: O_CAU0A28
	 */
	public java.lang.Long getObitosMenores28DiasPorOutrasCausas () {
		return getPropertyValue(this, obitosMenores28DiasPorOutrasCausas, PROP_OBITOS_MENORES28_DIAS_POR_OUTRAS_CAUSAS); 
	}

	/**
	 * Set the value related to the column: O_CAU0A28
	 * @param obitosMenores28DiasPorOutrasCausas the O_CAU0A28 value
	 */
	public void setObitosMenores28DiasPorOutrasCausas (java.lang.Long obitosMenores28DiasPorOutrasCausas) {
//        java.lang.Long obitosMenores28DiasPorOutrasCausasOld = this.obitosMenores28DiasPorOutrasCausas;
		this.obitosMenores28DiasPorOutrasCausas = obitosMenores28DiasPorOutrasCausas;
//        this.getPropertyChangeSupport().firePropertyChange ("obitosMenores28DiasPorOutrasCausas", obitosMenores28DiasPorOutrasCausasOld, obitosMenores28DiasPorOutrasCausas);
	}



	/**
	 * Return the value associated with the column: O_DIA28A1
	 */
	public java.lang.Long getObitos28DiasA1AnoPorDiarreia () {
		return getPropertyValue(this, obitos28DiasA1AnoPorDiarreia, PROP_OBITOS28_DIAS_A1_ANO_POR_DIARREIA); 
	}

	/**
	 * Set the value related to the column: O_DIA28A1
	 * @param obitos28DiasA1AnoPorDiarreia the O_DIA28A1 value
	 */
	public void setObitos28DiasA1AnoPorDiarreia (java.lang.Long obitos28DiasA1AnoPorDiarreia) {
//        java.lang.Long obitos28DiasA1AnoPorDiarreiaOld = this.obitos28DiasA1AnoPorDiarreia;
		this.obitos28DiasA1AnoPorDiarreia = obitos28DiasA1AnoPorDiarreia;
//        this.getPropertyChangeSupport().firePropertyChange ("obitos28DiasA1AnoPorDiarreia", obitos28DiasA1AnoPorDiarreiaOld, obitos28DiasA1AnoPorDiarreia);
	}



	/**
	 * Return the value associated with the column: O_IRA28A1
	 */
	public java.lang.Long getObitos28DiasA1AnoPorInfeccaoRespiratoria () {
		return getPropertyValue(this, obitos28DiasA1AnoPorInfeccaoRespiratoria, PROP_OBITOS28_DIAS_A1_ANO_POR_INFECCAO_RESPIRATORIA); 
	}

	/**
	 * Set the value related to the column: O_IRA28A1
	 * @param obitos28DiasA1AnoPorInfeccaoRespiratoria the O_IRA28A1 value
	 */
	public void setObitos28DiasA1AnoPorInfeccaoRespiratoria (java.lang.Long obitos28DiasA1AnoPorInfeccaoRespiratoria) {
//        java.lang.Long obitos28DiasA1AnoPorInfeccaoRespiratoriaOld = this.obitos28DiasA1AnoPorInfeccaoRespiratoria;
		this.obitos28DiasA1AnoPorInfeccaoRespiratoria = obitos28DiasA1AnoPorInfeccaoRespiratoria;
//        this.getPropertyChangeSupport().firePropertyChange ("obitos28DiasA1AnoPorInfeccaoRespiratoria", obitos28DiasA1AnoPorInfeccaoRespiratoriaOld, obitos28DiasA1AnoPorInfeccaoRespiratoria);
	}



	/**
	 * Return the value associated with the column: O_CAU28A1
	 */
	public java.lang.Long getObitos28DiasA1AnoPorOutrasCausas () {
		return getPropertyValue(this, obitos28DiasA1AnoPorOutrasCausas, PROP_OBITOS28_DIAS_A1_ANO_POR_OUTRAS_CAUSAS); 
	}

	/**
	 * Set the value related to the column: O_CAU28A1
	 * @param obitos28DiasA1AnoPorOutrasCausas the O_CAU28A1 value
	 */
	public void setObitos28DiasA1AnoPorOutrasCausas (java.lang.Long obitos28DiasA1AnoPorOutrasCausas) {
//        java.lang.Long obitos28DiasA1AnoPorOutrasCausasOld = this.obitos28DiasA1AnoPorOutrasCausas;
		this.obitos28DiasA1AnoPorOutrasCausas = obitos28DiasA1AnoPorOutrasCausas;
//        this.getPropertyChangeSupport().firePropertyChange ("obitos28DiasA1AnoPorOutrasCausas", obitos28DiasA1AnoPorOutrasCausasOld, obitos28DiasA1AnoPorOutrasCausas);
	}



	/**
	 * Return the value associated with the column: OBITODIA
	 */
	public java.lang.Long getObitosAte1AnoPorDiarreia () {
		return getPropertyValue(this, obitosAte1AnoPorDiarreia, PROP_OBITOS_ATE1_ANO_POR_DIARREIA); 
	}

	/**
	 * Set the value related to the column: OBITODIA
	 * @param obitosAte1AnoPorDiarreia the OBITODIA value
	 */
	public void setObitosAte1AnoPorDiarreia (java.lang.Long obitosAte1AnoPorDiarreia) {
//        java.lang.Long obitosAte1AnoPorDiarreiaOld = this.obitosAte1AnoPorDiarreia;
		this.obitosAte1AnoPorDiarreia = obitosAte1AnoPorDiarreia;
//        this.getPropertyChangeSupport().firePropertyChange ("obitosAte1AnoPorDiarreia", obitosAte1AnoPorDiarreiaOld, obitosAte1AnoPorDiarreia);
	}



	/**
	 * Return the value associated with the column: OBITOIRA
	 */
	public java.lang.Long getObitosAte1AnoPorInfeccaoRespiratoria () {
		return getPropertyValue(this, obitosAte1AnoPorInfeccaoRespiratoria, PROP_OBITOS_ATE1_ANO_POR_INFECCAO_RESPIRATORIA); 
	}

	/**
	 * Set the value related to the column: OBITOIRA
	 * @param obitosAte1AnoPorInfeccaoRespiratoria the OBITOIRA value
	 */
	public void setObitosAte1AnoPorInfeccaoRespiratoria (java.lang.Long obitosAte1AnoPorInfeccaoRespiratoria) {
//        java.lang.Long obitosAte1AnoPorInfeccaoRespiratoriaOld = this.obitosAte1AnoPorInfeccaoRespiratoria;
		this.obitosAte1AnoPorInfeccaoRespiratoria = obitosAte1AnoPorInfeccaoRespiratoria;
//        this.getPropertyChangeSupport().firePropertyChange ("obitosAte1AnoPorInfeccaoRespiratoria", obitosAte1AnoPorInfeccaoRespiratoriaOld, obitosAte1AnoPorInfeccaoRespiratoria);
	}



	/**
	 * Return the value associated with the column: OBITOCAU
	 */
	public java.lang.Long getObitosAte1AnoPorOutrasCausas () {
		return getPropertyValue(this, obitosAte1AnoPorOutrasCausas, PROP_OBITOS_ATE1_ANO_POR_OUTRAS_CAUSAS); 
	}

	/**
	 * Set the value related to the column: OBITOCAU
	 * @param obitosAte1AnoPorOutrasCausas the OBITOCAU value
	 */
	public void setObitosAte1AnoPorOutrasCausas (java.lang.Long obitosAte1AnoPorOutrasCausas) {
//        java.lang.Long obitosAte1AnoPorOutrasCausasOld = this.obitosAte1AnoPorOutrasCausas;
		this.obitosAte1AnoPorOutrasCausas = obitosAte1AnoPorOutrasCausas;
//        this.getPropertyChangeSupport().firePropertyChange ("obitosAte1AnoPorOutrasCausas", obitosAte1AnoPorOutrasCausasOld, obitosAte1AnoPorOutrasCausas);
	}



	/**
	 * Return the value associated with the column: O_MUL10A14
	 */
	public java.lang.Long getObitosMulheres10A14Anos () {
		return getPropertyValue(this, obitosMulheres10A14Anos, PROP_OBITOS_MULHERES10_A14_ANOS); 
	}

	/**
	 * Set the value related to the column: O_MUL10A14
	 * @param obitosMulheres10A14Anos the O_MUL10A14 value
	 */
	public void setObitosMulheres10A14Anos (java.lang.Long obitosMulheres10A14Anos) {
//        java.lang.Long obitosMulheres10A14AnosOld = this.obitosMulheres10A14Anos;
		this.obitosMulheres10A14Anos = obitosMulheres10A14Anos;
//        this.getPropertyChangeSupport().firePropertyChange ("obitosMulheres10A14Anos", obitosMulheres10A14AnosOld, obitosMulheres10A14Anos);
	}



	/**
	 * Return the value associated with the column: OBITOMUL
	 */
	public java.lang.Long getObitosMulheres15A49Anos () {
		return getPropertyValue(this, obitosMulheres15A49Anos, PROP_OBITOS_MULHERES15_A49_ANOS); 
	}

	/**
	 * Set the value related to the column: OBITOMUL
	 * @param obitosMulheres15A49Anos the OBITOMUL value
	 */
	public void setObitosMulheres15A49Anos (java.lang.Long obitosMulheres15A49Anos) {
//        java.lang.Long obitosMulheres15A49AnosOld = this.obitosMulheres15A49Anos;
		this.obitosMulheres15A49Anos = obitosMulheres15A49Anos;
//        this.getPropertyChangeSupport().firePropertyChange ("obitosMulheres15A49Anos", obitosMulheres15A49AnosOld, obitosMulheres15A49Anos);
	}



	/**
	 * Return the value associated with the column: OBITOADOL
	 */
	public java.lang.Long getObitosAdolescentes10A19AnosPorViolencia () {
		return getPropertyValue(this, obitosAdolescentes10A19AnosPorViolencia, PROP_OBITOS_ADOLESCENTES10_A19_ANOS_POR_VIOLENCIA); 
	}

	/**
	 * Set the value related to the column: OBITOADOL
	 * @param obitosAdolescentes10A19AnosPorViolencia the OBITOADOL value
	 */
	public void setObitosAdolescentes10A19AnosPorViolencia (java.lang.Long obitosAdolescentes10A19AnosPorViolencia) {
//        java.lang.Long obitosAdolescentes10A19AnosPorViolenciaOld = this.obitosAdolescentes10A19AnosPorViolencia;
		this.obitosAdolescentes10A19AnosPorViolencia = obitosAdolescentes10A19AnosPorViolencia;
//        this.getPropertyChangeSupport().firePropertyChange ("obitosAdolescentes10A19AnosPorViolencia", obitosAdolescentes10A19AnosPorViolenciaOld, obitosAdolescentes10A19AnosPorViolencia);
	}



	/**
	 * Return the value associated with the column: OBITOOUT
	 */
	public java.lang.Long getOutrosObitos () {
		return getPropertyValue(this, outrosObitos, PROP_OUTROS_OBITOS); 
	}

	/**
	 * Set the value related to the column: OBITOOUT
	 * @param outrosObitos the OBITOOUT value
	 */
	public void setOutrosObitos (java.lang.Long outrosObitos) {
//        java.lang.Long outrosObitosOld = this.outrosObitos;
		this.outrosObitos = outrosObitos;
//        this.getPropertyChangeSupport().firePropertyChange ("outrosObitos", outrosObitosOld, outrosObitos);
	}



	/**
	 * Return the value associated with the column: D_DIABETE
	 */
	public java.lang.Long getDiabeticosCadastrados () {
		return getPropertyValue(this, diabeticosCadastrados, PROP_DIABETICOS_CADASTRADOS); 
	}

	/**
	 * Set the value related to the column: D_DIABETE
	 * @param diabeticosCadastrados the D_DIABETE value
	 */
	public void setDiabeticosCadastrados (java.lang.Long diabeticosCadastrados) {
//        java.lang.Long diabeticosCadastradosOld = this.diabeticosCadastrados;
		this.diabeticosCadastrados = diabeticosCadastrados;
//        this.getPropertyChangeSupport().firePropertyChange ("diabeticosCadastrados", diabeticosCadastradosOld, diabeticosCadastrados);
	}



	/**
	 * Return the value associated with the column: D_DIAAC
	 */
	public java.lang.Long getDiabeticosAcompanhados () {
		return getPropertyValue(this, diabeticosAcompanhados, PROP_DIABETICOS_ACOMPANHADOS); 
	}

	/**
	 * Set the value related to the column: D_DIAAC
	 * @param diabeticosAcompanhados the D_DIAAC value
	 */
	public void setDiabeticosAcompanhados (java.lang.Long diabeticosAcompanhados) {
//        java.lang.Long diabeticosAcompanhadosOld = this.diabeticosAcompanhados;
		this.diabeticosAcompanhados = diabeticosAcompanhados;
//        this.getPropertyChangeSupport().firePropertyChange ("diabeticosAcompanhados", diabeticosAcompanhadosOld, diabeticosAcompanhados);
	}



	/**
	 * Return the value associated with the column: D_HIPERTEN
	 */
	public java.lang.Long getHipertensosCadastrados () {
		return getPropertyValue(this, hipertensosCadastrados, PROP_HIPERTENSOS_CADASTRADOS); 
	}

	/**
	 * Set the value related to the column: D_HIPERTEN
	 * @param hipertensosCadastrados the D_HIPERTEN value
	 */
	public void setHipertensosCadastrados (java.lang.Long hipertensosCadastrados) {
//        java.lang.Long hipertensosCadastradosOld = this.hipertensosCadastrados;
		this.hipertensosCadastrados = hipertensosCadastrados;
//        this.getPropertyChangeSupport().firePropertyChange ("hipertensosCadastrados", hipertensosCadastradosOld, hipertensosCadastrados);
	}



	/**
	 * Return the value associated with the column: D_HIPERAC
	 */
	public java.lang.Long getHipertensosAcompanhados () {
		return getPropertyValue(this, hipertensosAcompanhados, PROP_HIPERTENSOS_ACOMPANHADOS); 
	}

	/**
	 * Set the value related to the column: D_HIPERAC
	 * @param hipertensosAcompanhados the D_HIPERAC value
	 */
	public void setHipertensosAcompanhados (java.lang.Long hipertensosAcompanhados) {
//        java.lang.Long hipertensosAcompanhadosOld = this.hipertensosAcompanhados;
		this.hipertensosAcompanhados = hipertensosAcompanhados;
//        this.getPropertyChangeSupport().firePropertyChange ("hipertensosAcompanhados", hipertensosAcompanhadosOld, hipertensosAcompanhados);
	}



	/**
	 * Return the value associated with the column: D_TUBERCUL
	 */
	public java.lang.Long getPessoasComTuberculoseCadastradas () {
		return getPropertyValue(this, pessoasComTuberculoseCadastradas, PROP_PESSOAS_COM_TUBERCULOSE_CADASTRADAS); 
	}

	/**
	 * Set the value related to the column: D_TUBERCUL
	 * @param pessoasComTuberculoseCadastradas the D_TUBERCUL value
	 */
	public void setPessoasComTuberculoseCadastradas (java.lang.Long pessoasComTuberculoseCadastradas) {
//        java.lang.Long pessoasComTuberculoseCadastradasOld = this.pessoasComTuberculoseCadastradas;
		this.pessoasComTuberculoseCadastradas = pessoasComTuberculoseCadastradas;
//        this.getPropertyChangeSupport().firePropertyChange ("pessoasComTuberculoseCadastradas", pessoasComTuberculoseCadastradasOld, pessoasComTuberculoseCadastradas);
	}



	/**
	 * Return the value associated with the column: D_TUBERAC
	 */
	public java.lang.Long getPessoasComTuberculoseAcompanhadas () {
		return getPropertyValue(this, pessoasComTuberculoseAcompanhadas, PROP_PESSOAS_COM_TUBERCULOSE_ACOMPANHADAS); 
	}

	/**
	 * Set the value related to the column: D_TUBERAC
	 * @param pessoasComTuberculoseAcompanhadas the D_TUBERAC value
	 */
	public void setPessoasComTuberculoseAcompanhadas (java.lang.Long pessoasComTuberculoseAcompanhadas) {
//        java.lang.Long pessoasComTuberculoseAcompanhadasOld = this.pessoasComTuberculoseAcompanhadas;
		this.pessoasComTuberculoseAcompanhadas = pessoasComTuberculoseAcompanhadas;
//        this.getPropertyChangeSupport().firePropertyChange ("pessoasComTuberculoseAcompanhadas", pessoasComTuberculoseAcompanhadasOld, pessoasComTuberculoseAcompanhadas);
	}



	/**
	 * Return the value associated with the column: D_HANSEN
	 */
	public java.lang.Long getPessoasComHanseniaseCadastradas () {
		return getPropertyValue(this, pessoasComHanseniaseCadastradas, PROP_PESSOAS_COM_HANSENIASE_CADASTRADAS); 
	}

	/**
	 * Set the value related to the column: D_HANSEN
	 * @param pessoasComHanseniaseCadastradas the D_HANSEN value
	 */
	public void setPessoasComHanseniaseCadastradas (java.lang.Long pessoasComHanseniaseCadastradas) {
//        java.lang.Long pessoasComHanseniaseCadastradasOld = this.pessoasComHanseniaseCadastradas;
		this.pessoasComHanseniaseCadastradas = pessoasComHanseniaseCadastradas;
//        this.getPropertyChangeSupport().firePropertyChange ("pessoasComHanseniaseCadastradas", pessoasComHanseniaseCadastradasOld, pessoasComHanseniaseCadastradas);
	}



	/**
	 * Return the value associated with the column: D_HANSEAC
	 */
	public java.lang.Long getPessoasComHanseniaseAcompanhadas () {
		return getPropertyValue(this, pessoasComHanseniaseAcompanhadas, PROP_PESSOAS_COM_HANSENIASE_ACOMPANHADAS); 
	}

	/**
	 * Set the value related to the column: D_HANSEAC
	 * @param pessoasComHanseniaseAcompanhadas the D_HANSEAC value
	 */
	public void setPessoasComHanseniaseAcompanhadas (java.lang.Long pessoasComHanseniaseAcompanhadas) {
//        java.lang.Long pessoasComHanseniaseAcompanhadasOld = this.pessoasComHanseniaseAcompanhadas;
		this.pessoasComHanseniaseAcompanhadas = pessoasComHanseniaseAcompanhadas;
//        this.getPropertyChangeSupport().firePropertyChange ("pessoasComHanseniaseAcompanhadas", pessoasComHanseniaseAcompanhadasOld, pessoasComHanseniaseAcompanhadas);
	}



	/**
	 * Return the value associated with the column: H_0A5PNEU
	 */
	public java.lang.Long getHospitalizadosMenores5AnosPorPneumonia () {
		return getPropertyValue(this, hospitalizadosMenores5AnosPorPneumonia, PROP_HOSPITALIZADOS_MENORES5_ANOS_POR_PNEUMONIA); 
	}

	/**
	 * Set the value related to the column: H_0A5PNEU
	 * @param hospitalizadosMenores5AnosPorPneumonia the H_0A5PNEU value
	 */
	public void setHospitalizadosMenores5AnosPorPneumonia (java.lang.Long hospitalizadosMenores5AnosPorPneumonia) {
//        java.lang.Long hospitalizadosMenores5AnosPorPneumoniaOld = this.hospitalizadosMenores5AnosPorPneumonia;
		this.hospitalizadosMenores5AnosPorPneumonia = hospitalizadosMenores5AnosPorPneumonia;
//        this.getPropertyChangeSupport().firePropertyChange ("hospitalizadosMenores5AnosPorPneumonia", hospitalizadosMenores5AnosPorPneumoniaOld, hospitalizadosMenores5AnosPorPneumonia);
	}



	/**
	 * Return the value associated with the column: H_0A5DES
	 */
	public java.lang.Long getHospitalizadosMenores5AnosPorDesidratacao () {
		return getPropertyValue(this, hospitalizadosMenores5AnosPorDesidratacao, PROP_HOSPITALIZADOS_MENORES5_ANOS_POR_DESIDRATACAO); 
	}

	/**
	 * Set the value related to the column: H_0A5DES
	 * @param hospitalizadosMenores5AnosPorDesidratacao the H_0A5DES value
	 */
	public void setHospitalizadosMenores5AnosPorDesidratacao (java.lang.Long hospitalizadosMenores5AnosPorDesidratacao) {
//        java.lang.Long hospitalizadosMenores5AnosPorDesidratacaoOld = this.hospitalizadosMenores5AnosPorDesidratacao;
		this.hospitalizadosMenores5AnosPorDesidratacao = hospitalizadosMenores5AnosPorDesidratacao;
//        this.getPropertyChangeSupport().firePropertyChange ("hospitalizadosMenores5AnosPorDesidratacao", hospitalizadosMenores5AnosPorDesidratacaoOld, hospitalizadosMenores5AnosPorDesidratacao);
	}



	/**
	 * Return the value associated with the column: H_ALCOOL
	 */
	public java.lang.Long getHospitalizadosPorAbusoAlcool () {
		return getPropertyValue(this, hospitalizadosPorAbusoAlcool, PROP_HOSPITALIZADOS_POR_ABUSO_ALCOOL); 
	}

	/**
	 * Set the value related to the column: H_ALCOOL
	 * @param hospitalizadosPorAbusoAlcool the H_ALCOOL value
	 */
	public void setHospitalizadosPorAbusoAlcool (java.lang.Long hospitalizadosPorAbusoAlcool) {
//        java.lang.Long hospitalizadosPorAbusoAlcoolOld = this.hospitalizadosPorAbusoAlcool;
		this.hospitalizadosPorAbusoAlcool = hospitalizadosPorAbusoAlcool;
//        this.getPropertyChangeSupport().firePropertyChange ("hospitalizadosPorAbusoAlcool", hospitalizadosPorAbusoAlcoolOld, hospitalizadosPorAbusoAlcool);
	}



	/**
	 * Return the value associated with the column: H_PSIQUI
	 */
	public java.lang.Long getInternacoesEmHospitalPsiquiatrico () {
		return getPropertyValue(this, internacoesEmHospitalPsiquiatrico, PROP_INTERNACOES_EM_HOSPITAL_PSIQUIATRICO); 
	}

	/**
	 * Set the value related to the column: H_PSIQUI
	 * @param internacoesEmHospitalPsiquiatrico the H_PSIQUI value
	 */
	public void setInternacoesEmHospitalPsiquiatrico (java.lang.Long internacoesEmHospitalPsiquiatrico) {
//        java.lang.Long internacoesEmHospitalPsiquiatricoOld = this.internacoesEmHospitalPsiquiatrico;
		this.internacoesEmHospitalPsiquiatrico = internacoesEmHospitalPsiquiatrico;
//        this.getPropertyChangeSupport().firePropertyChange ("internacoesEmHospitalPsiquiatrico", internacoesEmHospitalPsiquiatricoOld, internacoesEmHospitalPsiquiatrico);
	}



	/**
	 * Return the value associated with the column: H_DIABETE
	 */
	public java.lang.Long getHospitalizadosPorComplicacoesDiabetes () {
		return getPropertyValue(this, hospitalizadosPorComplicacoesDiabetes, PROP_HOSPITALIZADOS_POR_COMPLICACOES_DIABETES); 
	}

	/**
	 * Set the value related to the column: H_DIABETE
	 * @param hospitalizadosPorComplicacoesDiabetes the H_DIABETE value
	 */
	public void setHospitalizadosPorComplicacoesDiabetes (java.lang.Long hospitalizadosPorComplicacoesDiabetes) {
//        java.lang.Long hospitalizadosPorComplicacoesDiabetesOld = this.hospitalizadosPorComplicacoesDiabetes;
		this.hospitalizadosPorComplicacoesDiabetes = hospitalizadosPorComplicacoesDiabetes;
//        this.getPropertyChangeSupport().firePropertyChange ("hospitalizadosPorComplicacoesDiabetes", hospitalizadosPorComplicacoesDiabetesOld, hospitalizadosPorComplicacoesDiabetes);
	}



	/**
	 * Return the value associated with the column: H_OUTCAU
	 */
	public java.lang.Long getHospitalizadosPorOutrasCausas () {
		return getPropertyValue(this, hospitalizadosPorOutrasCausas, PROP_HOSPITALIZADOS_POR_OUTRAS_CAUSAS); 
	}

	/**
	 * Set the value related to the column: H_OUTCAU
	 * @param hospitalizadosPorOutrasCausas the H_OUTCAU value
	 */
	public void setHospitalizadosPorOutrasCausas (java.lang.Long hospitalizadosPorOutrasCausas) {
//        java.lang.Long hospitalizadosPorOutrasCausasOld = this.hospitalizadosPorOutrasCausas;
		this.hospitalizadosPorOutrasCausas = hospitalizadosPorOutrasCausas;
//        this.getPropertyChangeSupport().firePropertyChange ("hospitalizadosPorOutrasCausas", hospitalizadosPorOutrasCausasOld, hospitalizadosPorOutrasCausas);
	}



	/**
	 * Return the value associated with the column: SIGAB
	 */
	public java.lang.String getSigab () {
		return getPropertyValue(this, sigab, PROP_SIGAB); 
	}

	/**
	 * Set the value related to the column: SIGAB
	 * @param sigab the SIGAB value
	 */
	public void setSigab (java.lang.String sigab) {
//        java.lang.String sigabOld = this.sigab;
		this.sigab = sigab;
//        this.getPropertyChangeSupport().firePropertyChange ("sigab", sigabOld, sigab);
	}



	/**
	 * Return the value associated with the column: cd_equipe_profissional
	 */
	public br.com.ksisolucoes.vo.basico.EquipeProfissional getEquipeProfissional () {
		return getPropertyValue(this, equipeProfissional, PROP_EQUIPE_PROFISSIONAL); 
	}

	/**
	 * Set the value related to the column: cd_equipe_profissional
	 * @param equipeProfissional the cd_equipe_profissional value
	 */
	public void setEquipeProfissional (br.com.ksisolucoes.vo.basico.EquipeProfissional equipeProfissional) {
//        br.com.ksisolucoes.vo.basico.EquipeProfissional equipeProfissionalOld = this.equipeProfissional;
		this.equipeProfissional = equipeProfissional;
//        this.getPropertyChangeSupport().firePropertyChange ("equipeProfissional", equipeProfissionalOld, equipeProfissional);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuario the cd_usuario value
	 */
	public void setUsuario (br.com.ksisolucoes.vo.controle.Usuario usuario) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.siab.SiabSsa2)) return false;
		else {
			br.com.ksisolucoes.vo.siab.SiabSsa2 siabSsa2 = (br.com.ksisolucoes.vo.siab.SiabSsa2) obj;
			if (null == this.getCodigo() || null == siabSsa2.getCodigo()) return false;
			else return (this.getCodigo().equals(siabSsa2.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}