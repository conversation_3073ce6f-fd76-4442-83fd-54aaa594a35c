<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >
	
<hibernate-mapping package="br.com.ksisolucoes.vo.patrimonio"  >
	<class 
		name="BemMotivoBaixa"
		table="bem_motivo_baixa"
	>

        <id
                name="codigo"
                type="java.lang.Long"
                column="cd_motivo_baixa"
        >
                <generator class="assigned"/>
        </id> <version column="version" name="version" type="long" />

        <property
                name="descricao"
                column="ds_motivo_baixa"
                type="java.lang.String"
                not-null="true"
        />
	</class>
</hibernate-mapping>