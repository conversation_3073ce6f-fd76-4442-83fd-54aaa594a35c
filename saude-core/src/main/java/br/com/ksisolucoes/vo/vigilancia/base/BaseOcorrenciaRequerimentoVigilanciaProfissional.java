package br.com.ksisolucoes.vo.vigilancia.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the ocorrencia_req_vigilancia_prof table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="ocorrencia_req_vigilancia_prof"
 */

public abstract class BaseOcorrenciaRequerimentoVigilanciaProfissional extends BaseRootVO implements Serializable {

	public static String REF = "OcorrenciaRequerimentoVigilanciaProfissional";
	public static final String PROP_SITUACAO_OCORRENCIA = "situacaoOcorrencia";
	public static final String PROP_DATA = "data";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_DESCRICAO = "descricao";
	public static final String PROP_PROFISSIONAL = "profissional";
	public static final String PROP_OCORRENCIA_REQUERIMENTO_VIGILANCIA = "ocorrenciaRequerimentoVigilancia";


	// constructors
	public BaseOcorrenciaRequerimentoVigilanciaProfissional () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseOcorrenciaRequerimentoVigilanciaProfissional (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseOcorrenciaRequerimentoVigilanciaProfissional (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.OcorrenciaRequerimentoVigilancia ocorrenciaRequerimentoVigilancia,
		br.com.ksisolucoes.vo.cadsus.Profissional profissional,
		java.util.Date data,
		java.lang.String descricao,
		java.lang.Long situacaoOcorrencia) {

		this.setCodigo(codigo);
		this.setOcorrenciaRequerimentoVigilancia(ocorrenciaRequerimentoVigilancia);
		this.setProfissional(profissional);
		this.setData(data);
		this.setDescricao(descricao);
		this.setSituacaoOcorrencia(situacaoOcorrencia);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.util.Date data;
	private java.lang.String descricao;
	private java.lang.Long situacaoOcorrencia;

	// many to one
	private br.com.ksisolucoes.vo.vigilancia.OcorrenciaRequerimentoVigilancia ocorrenciaRequerimentoVigilancia;
	private br.com.ksisolucoes.vo.cadsus.Profissional profissional;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_ocorr_req_vig_prof"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: data
	 */
	public java.util.Date getData () {
		return getPropertyValue(this, data, PROP_DATA); 
	}

	/**
	 * Set the value related to the column: data
	 * @param data the data value
	 */
	public void setData (java.util.Date data) {
//        java.util.Date dataOld = this.data;
		this.data = data;
//        this.getPropertyChangeSupport().firePropertyChange ("data", dataOld, data);
	}



	/**
	 * Return the value associated with the column: descricao
	 */
	public java.lang.String getDescricao () {
		return getPropertyValue(this, descricao, PROP_DESCRICAO); 
	}

	/**
	 * Set the value related to the column: descricao
	 * @param descricao the descricao value
	 */
	public void setDescricao (java.lang.String descricao) {
//        java.lang.String descricaoOld = this.descricao;
		this.descricao = descricao;
//        this.getPropertyChangeSupport().firePropertyChange ("descricao", descricaoOld, descricao);
	}



	/**
	 * Return the value associated with the column: situacao_ocorrencia
	 */
	public java.lang.Long getSituacaoOcorrencia () {
		return getPropertyValue(this, situacaoOcorrencia, PROP_SITUACAO_OCORRENCIA); 
	}

	/**
	 * Set the value related to the column: situacao_ocorrencia
	 * @param situacaoOcorrencia the situacao_ocorrencia value
	 */
	public void setSituacaoOcorrencia (java.lang.Long situacaoOcorrencia) {
//        java.lang.Long situacaoOcorrenciaOld = this.situacaoOcorrencia;
		this.situacaoOcorrencia = situacaoOcorrencia;
//        this.getPropertyChangeSupport().firePropertyChange ("situacaoOcorrencia", situacaoOcorrenciaOld, situacaoOcorrencia);
	}



	/**
	 * Return the value associated with the column: cd_ocorr_req_vig
	 */
	public br.com.ksisolucoes.vo.vigilancia.OcorrenciaRequerimentoVigilancia getOcorrenciaRequerimentoVigilancia () {
		return getPropertyValue(this, ocorrenciaRequerimentoVigilancia, PROP_OCORRENCIA_REQUERIMENTO_VIGILANCIA); 
	}

	/**
	 * Set the value related to the column: cd_ocorr_req_vig
	 * @param ocorrenciaRequerimentoVigilancia the cd_ocorr_req_vig value
	 */
	public void setOcorrenciaRequerimentoVigilancia (br.com.ksisolucoes.vo.vigilancia.OcorrenciaRequerimentoVigilancia ocorrenciaRequerimentoVigilancia) {
//        br.com.ksisolucoes.vo.vigilancia.OcorrenciaRequerimentoVigilancia ocorrenciaRequerimentoVigilanciaOld = this.ocorrenciaRequerimentoVigilancia;
		this.ocorrenciaRequerimentoVigilancia = ocorrenciaRequerimentoVigilancia;
//        this.getPropertyChangeSupport().firePropertyChange ("ocorrenciaRequerimentoVigilancia", ocorrenciaRequerimentoVigilanciaOld, ocorrenciaRequerimentoVigilancia);
	}



	/**
	 * Return the value associated with the column: cd_profissional
	 */
	public br.com.ksisolucoes.vo.cadsus.Profissional getProfissional () {
		return getPropertyValue(this, profissional, PROP_PROFISSIONAL); 
	}

	/**
	 * Set the value related to the column: cd_profissional
	 * @param profissional the cd_profissional value
	 */
	public void setProfissional (br.com.ksisolucoes.vo.cadsus.Profissional profissional) {
//        br.com.ksisolucoes.vo.cadsus.Profissional profissionalOld = this.profissional;
		this.profissional = profissional;
//        this.getPropertyChangeSupport().firePropertyChange ("profissional", profissionalOld, profissional);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.vigilancia.OcorrenciaRequerimentoVigilanciaProfissional)) return false;
		else {
			br.com.ksisolucoes.vo.vigilancia.OcorrenciaRequerimentoVigilanciaProfissional ocorrenciaRequerimentoVigilanciaProfissional = (br.com.ksisolucoes.vo.vigilancia.OcorrenciaRequerimentoVigilanciaProfissional) obj;
			if (null == this.getCodigo() || null == ocorrenciaRequerimentoVigilanciaProfissional.getCodigo()) return false;
			else return (this.getCodigo().equals(ocorrenciaRequerimentoVigilanciaProfissional.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}