<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.basico"  >
    <class name="InvestigacaoAgravoHemograma" table="investigacao_agravo_hemograma">

        <id
                name="codigo"
                type="java.lang.Long"
                column="cd_investigacao_agravo_hemograma"
        >
            <generator class="sequence">
                <param name="sequence">seq_investigacao_agravo_hemograma</param>
            </generator>
        </id>
        <version column="version" name="version" type="long" />

		<many-to-one class="br.com.ksisolucoes.vo.basico.InvestigacaoAgravo"
                name="investigacaoAgravo" not-null="true">
            <column name="cd_investigacao_agravo" />
        </many-to-one>
        
        <property 
            name="dataColeta"
            column="dt_coleta"
            type="timestamp"
        />
        
        <property 
            name="hemacias"
            column="hemacias"
            type="java.lang.String"
        />
        
        <property 
            name="hemoglobina"
            column="hemoglobina"
            type="java.lang.String"
        />
        
        <property 
            name="hematocrito"
            column="hematocrito"
            type="java.lang.String"
        />
        
        <property 
            name="plaquetas"
            column="plaquetas"
            type="java.lang.String"
        />
        
        <property 
            name="bastoes"
            column="bastoes"
            type="java.lang.String"
        />
        
        <property 
            name="neutrofilos"
            column="neutrofilos"
            type="java.lang.String"
        />
        
        <property 
            name="linfocitos"
            column="linfocitos"
            type="java.lang.String"
        />
        
        <property 
            name="eosinofitos"
            column="eosinofitos"
            type="java.lang.String"
        />
        
        <property 
            name="leucocitos"
            column="leucocitos"
            type="java.lang.String"
        />
        
        <property 
            name="monocitos"
            column="monocitos"
            type="java.lang.String"
        />
        
        <property 
            name="dataCadastro"
            column="dt_cadastro"
            type="timestamp"
            not-null="true"
        />
    </class>
</hibernate-mapping>
