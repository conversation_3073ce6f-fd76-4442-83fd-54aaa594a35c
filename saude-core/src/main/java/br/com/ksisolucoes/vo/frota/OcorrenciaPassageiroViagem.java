package br.com.ksisolucoes.vo.frota;

import java.io.Serializable;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.frota.base.BaseOcorrenciaPassageiroViagem;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class OcorrenciaPassageiroViagem extends BaseOcorrenciaPassageiroViagem implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public OcorrenciaPassageiroViagem () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public OcorrenciaPassageiroViagem (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public OcorrenciaPassageiroViagem (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		br.com.ksisolucoes.vo.frota.RoteiroViagem roteiroViagem,
		java.util.Date dataCadastro,
		java.lang.Long tipoOcorrencia,
		java.lang.String descricaoOcorrencia) {

		super (
			codigo,
			usuario,
			roteiroViagem,
			dataCadastro,
			tipoOcorrencia,
			descricaoOcorrencia);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

	public enum TipoOcorrencia implements IEnum{
		CADASTRO(1L, Bundle.getStringApplication("rotulo_cadastro")),
		INCLUSAO_PACIENTE(2L, Bundle.getStringApplication("rotulo_inclusao_paciente")),
		EXCLUSAO_PACIENTE(3L, Bundle.getStringApplication("rotulo_exclusao_paciente")),
		CANCELAMENTO(4L,Bundle.getStringApplication("rotulo_cancelamento"));

		private Long value;
		private String descricao;

		TipoOcorrencia(Long value, String descricao){
			this.value = value;
			this.descricao = descricao;
		}

		public static TipoOcorrencia valueOf(Long value) {
			for (TipoOcorrencia tipoOcorrencia : TipoOcorrencia.values()){
				if (tipoOcorrencia.value().equals(value))
					return tipoOcorrencia;
			}

			return null;
		}

		@Override
		public Long value() {
			return value;
		}

		@Override
		public String descricao() {
			return descricao;
		}
	}

	public String getDescricaoTipoOcorrencia(){
		TipoOcorrencia tipoOcorrencia = TipoOcorrencia.valueOf(getTipoOcorrencia());
		if (tipoOcorrencia != null && tipoOcorrencia.descricao != null) {
			return tipoOcorrencia.descricao();
		}
		return "";
	}
}