<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
"-//Hibernate/Hibernate Mapping DTD//EN"
"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >
 
<hibernate-mapping package="br.com.ksisolucoes.vo.prontuario.basico"  >
    <class name="EloTipoProcedimentoRelacionado" table="elo_tipo_procedimento_relacionado" >
        <id name="codigo" type="java.lang.Long" column="cd_elo_tp_proc_rel">
            <generator class="sequence">
                <param name="sequence">seq_gem</param>
            </generator>
        </id> 

        <version column="version" name="version" type="long" />

        <many-to-one class="br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento" name="tipoProcedimentoPrincipal">
            <column name="cd_tp_procedimento_princ"/>
        </many-to-one>

        <many-to-one class="br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento" name="tipoProcedimentoRelacionado">
            <column name="cd_tp_procedimento_rel"/>
        </many-to-one>
    </class>
</hibernate-mapping>



