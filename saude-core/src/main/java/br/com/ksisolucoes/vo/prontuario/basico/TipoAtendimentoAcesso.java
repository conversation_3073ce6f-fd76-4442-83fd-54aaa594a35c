package br.com.ksisolucoes.vo.prontuario.basico;

import java.io.Serializable;

import br.com.ksisolucoes.vo.prontuario.basico.base.BaseTipoAtendimentoAcesso;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class TipoAtendimentoAcesso extends BaseTipoAtendimentoAcesso implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public TipoAtendimentoAcesso () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public TipoAtendimentoAcesso (java.lang.Long codigo) {
		super(codigo);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}