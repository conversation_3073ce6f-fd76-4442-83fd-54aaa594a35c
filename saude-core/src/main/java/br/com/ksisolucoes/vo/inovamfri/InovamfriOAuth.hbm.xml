<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
"-//Hibernate/Hibernate Mapping DTD//EN"
"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.inovamfri" >
    <class name="InovamfriOAuth" table="inovamfri_oauth">

        <id
                column="token"
                name="token"
                type="java.lang.String"
        >
            <generator class="assigned"/>
        </id>

        <!--<property-->
            <!--name="token"-->
            <!--column="token"-->
            <!--type="java.lang.String"-->
        <!--/>-->
        
        <property
            name="dataHoraExpiracao"
            column="dt_expiracao"
            type="timestamp"
            not-null="true"
        />

    </class>
</hibernate-mapping>
