package br.com.ksisolucoes.vo.vigilancia.investigacao.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the investigacao_agr_febre_amarela_deslocamento table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="investigacao_agr_febre_amarela_deslocamento"
 */

public abstract class BaseInvestigacaoAgravoFebreAmarelaDeslocamento extends BaseRootVO implements Serializable {

	public static String REF = "InvestigacaoAgravoFebreAmarelaDeslocamento";
	public static final String PROP_MEIO_TRANSPORTE = "meioTransporte";
	public static final String PROP_INVESTIGACAO_AGRAVO_FEBRE_AMARELA = "investigacaoAgravoFebreAmarela";
	public static final String PROP_CIDADE_DESLOCAMENTO = "cidadeDeslocamento";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_DATA_DESLOCAMENTO = "dataDeslocamento";
	public static final String PROP_PAIS_DESLOCAMENTO = "paisDeslocamento";


	// constructors
	public BaseInvestigacaoAgravoFebreAmarelaDeslocamento () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseInvestigacaoAgravoFebreAmarelaDeslocamento (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.util.Date dataDeslocamento;
	private java.lang.String meioTransporte;

	// many to one
	private br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoFebreAmarela investigacaoAgravoFebreAmarela;
	private br.com.ksisolucoes.vo.basico.Cidade cidadeDeslocamento;
	private br.com.ksisolucoes.vo.basico.Pais paisDeslocamento;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_invest_agr_febre_amarela_deslocamento"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: dt_deslocamento
	 */
	public java.util.Date getDataDeslocamento () {
		return getPropertyValue(this, dataDeslocamento, PROP_DATA_DESLOCAMENTO); 
	}

	/**
	 * Set the value related to the column: dt_deslocamento
	 * @param dataDeslocamento the dt_deslocamento value
	 */
	public void setDataDeslocamento (java.util.Date dataDeslocamento) {
//        java.util.Date dataDeslocamentoOld = this.dataDeslocamento;
		this.dataDeslocamento = dataDeslocamento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataDeslocamento", dataDeslocamentoOld, dataDeslocamento);
	}



	/**
	 * Return the value associated with the column: str_meio_transporte
	 */
	public java.lang.String getMeioTransporte () {
		return getPropertyValue(this, meioTransporte, PROP_MEIO_TRANSPORTE); 
	}

	/**
	 * Set the value related to the column: str_meio_transporte
	 * @param meioTransporte the str_meio_transporte value
	 */
	public void setMeioTransporte (java.lang.String meioTransporte) {
//        java.lang.String meioTransporteOld = this.meioTransporte;
		this.meioTransporte = meioTransporte;
//        this.getPropertyChangeSupport().firePropertyChange ("meioTransporte", meioTransporteOld, meioTransporte);
	}



	/**
	 * Return the value associated with the column: cd_invest_agr_febre_amarela
	 */
	public br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoFebreAmarela getInvestigacaoAgravoFebreAmarela () {
		return getPropertyValue(this, investigacaoAgravoFebreAmarela, PROP_INVESTIGACAO_AGRAVO_FEBRE_AMARELA); 
	}

	/**
	 * Set the value related to the column: cd_invest_agr_febre_amarela
	 * @param investigacaoAgravoFebreAmarela the cd_invest_agr_febre_amarela value
	 */
	public void setInvestigacaoAgravoFebreAmarela (br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoFebreAmarela investigacaoAgravoFebreAmarela) {
//        br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoFebreAmarela investigacaoAgravoFebreAmarelaOld = this.investigacaoAgravoFebreAmarela;
		this.investigacaoAgravoFebreAmarela = investigacaoAgravoFebreAmarela;
//        this.getPropertyChangeSupport().firePropertyChange ("investigacaoAgravoFebreAmarela", investigacaoAgravoFebreAmarelaOld, investigacaoAgravoFebreAmarela);
	}



	/**
	 * Return the value associated with the column: cd_cidade_deslocamento
	 */
	public br.com.ksisolucoes.vo.basico.Cidade getCidadeDeslocamento () {
		return getPropertyValue(this, cidadeDeslocamento, PROP_CIDADE_DESLOCAMENTO); 
	}

	/**
	 * Set the value related to the column: cd_cidade_deslocamento
	 * @param cidadeDeslocamento the cd_cidade_deslocamento value
	 */
	public void setCidadeDeslocamento (br.com.ksisolucoes.vo.basico.Cidade cidadeDeslocamento) {
//        br.com.ksisolucoes.vo.basico.Cidade cidadeDeslocamentoOld = this.cidadeDeslocamento;
		this.cidadeDeslocamento = cidadeDeslocamento;
//        this.getPropertyChangeSupport().firePropertyChange ("cidadeDeslocamento", cidadeDeslocamentoOld, cidadeDeslocamento);
	}



	/**
	 * Return the value associated with the column: cd_pais_deslocamento
	 */
	public br.com.ksisolucoes.vo.basico.Pais getPaisDeslocamento () {
		return getPropertyValue(this, paisDeslocamento, PROP_PAIS_DESLOCAMENTO); 
	}

	/**
	 * Set the value related to the column: cd_pais_deslocamento
	 * @param paisDeslocamento the cd_pais_deslocamento value
	 */
	public void setPaisDeslocamento (br.com.ksisolucoes.vo.basico.Pais paisDeslocamento) {
//        br.com.ksisolucoes.vo.basico.Pais paisDeslocamentoOld = this.paisDeslocamento;
		this.paisDeslocamento = paisDeslocamento;
//        this.getPropertyChangeSupport().firePropertyChange ("paisDeslocamento", paisDeslocamentoOld, paisDeslocamento);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoFebreAmarelaDeslocamento)) return false;
		else {
			br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoFebreAmarelaDeslocamento investigacaoAgravoFebreAmarelaDeslocamento = (br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoFebreAmarelaDeslocamento) obj;
			if (null == this.getCodigo() || null == investigacaoAgravoFebreAmarelaDeslocamento.getCodigo()) return false;
			else return (this.getCodigo().equals(investigacaoAgravoFebreAmarelaDeslocamento.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}