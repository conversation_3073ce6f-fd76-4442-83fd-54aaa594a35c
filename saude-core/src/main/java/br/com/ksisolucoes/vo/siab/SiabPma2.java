package br.com.ksisolucoes.vo.siab;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Data;
import java.io.Serializable;

import br.com.ksisolucoes.vo.siab.base.BaseSiabPma2;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class SiabPma2 extends BaseSiabPma2 implements CodigoManager {
	private static final long serialVersionUID = 1L;

        public static final String PROP_DESCRICAO_MES = "descricaoMes";

        public enum StatusSiabPma2 implements IEnum<SiabPma2.StatusSiabPma2> {
            CADASTRADO(0L, Bundle.getStringApplication("rotulo_cadastrado")),
            ;
            
            private Long value;
            private String descricao;
            
            private StatusSiabPma2(Long value, String descricao){
                this.value = value;
                this.descricao = descricao;
            }

            @Override
            public Long value() {
                return this.value;
            }

            @Override
            public String descricao() {
                return this.descricao;
            }
        }
        
/*[CONSTRUCTOR MARKER BEGIN]*/
	public SiabPma2 () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public SiabPma2 (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public SiabPma2 (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		java.lang.Long mes,
		java.lang.Long ano,
		java.util.Date dataUsuario,
		java.lang.Long status) {

		super (
			codigo,
			usuario,
			mes,
			ano,
			dataUsuario,
			status);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
    
    public String getDescricaoMes(){
        return Data.getDescricaoMes(getMes().intValue()-1);
    }
}