package br.com.ksisolucoes.vo.entradas.estoque.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the produto_brasindice_item table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="produto_brasindice_item"
 */

public abstract class BaseProdutoBrasindiceItem extends BaseRootVO implements Serializable {

	public static String REF = "ProdutoBrasindiceItem";
	public static final String PROP_PRECO = "preco";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_TIPO_PRECO = "tipoPreco";
	public static final String PROP_VERSAO_BRASINDICE = "versaoBrasindice";
	public static final String PROP_PRODUTO_BRASINDICE = "produtoBrasindice";
	public static final String PROP_DATA_INICIO_VIGENCIA = "dataInicioVigencia";


	// constructors
	public BaseProdutoBrasindiceItem () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseProdutoBrasindiceItem (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseProdutoBrasindiceItem (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.entradas.estoque.ProdutoBrasindice produtoBrasindice,
		java.lang.Double preco,
		java.lang.String tipoPreco,
		java.lang.Long versaoBrasindice,
		java.util.Date dataInicioVigencia) {

		this.setCodigo(codigo);
		this.setProdutoBrasindice(produtoBrasindice);
		this.setPreco(preco);
		this.setTipoPreco(tipoPreco);
		this.setVersaoBrasindice(versaoBrasindice);
		this.setDataInicioVigencia(dataInicioVigencia);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Double preco;
	private java.lang.String tipoPreco;
	private java.lang.Long versaoBrasindice;
	private java.util.Date dataInicioVigencia;

	// many to one
	private br.com.ksisolucoes.vo.entradas.estoque.ProdutoBrasindice produtoBrasindice;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_produto_brasindice_it"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: preco
	 */
	public java.lang.Double getPreco () {
		return getPropertyValue(this, preco, PROP_PRECO); 
	}

	/**
	 * Set the value related to the column: preco
	 * @param preco the preco value
	 */
	public void setPreco (java.lang.Double preco) {
//        java.lang.Double precoOld = this.preco;
		this.preco = preco;
//        this.getPropertyChangeSupport().firePropertyChange ("preco", precoOld, preco);
	}



	/**
	 * Return the value associated with the column: tipo_preco
	 */
	public java.lang.String getTipoPreco () {
		return getPropertyValue(this, tipoPreco, PROP_TIPO_PRECO); 
	}

	/**
	 * Set the value related to the column: tipo_preco
	 * @param tipoPreco the tipo_preco value
	 */
	public void setTipoPreco (java.lang.String tipoPreco) {
//        java.lang.String tipoPrecoOld = this.tipoPreco;
		this.tipoPreco = tipoPreco;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoPreco", tipoPrecoOld, tipoPreco);
	}



	/**
	 * Return the value associated with the column: versao_brasindice
	 */
	public java.lang.Long getVersaoBrasindice () {
		return getPropertyValue(this, versaoBrasindice, PROP_VERSAO_BRASINDICE); 
	}

	/**
	 * Set the value related to the column: versao_brasindice
	 * @param versaoBrasindice the versao_brasindice value
	 */
	public void setVersaoBrasindice (java.lang.Long versaoBrasindice) {
//        java.lang.Long versaoBrasindiceOld = this.versaoBrasindice;
		this.versaoBrasindice = versaoBrasindice;
//        this.getPropertyChangeSupport().firePropertyChange ("versaoBrasindice", versaoBrasindiceOld, versaoBrasindice);
	}



	/**
	 * Return the value associated with the column: data_inicio_vigencia
	 */
	public java.util.Date getDataInicioVigencia () {
		return getPropertyValue(this, dataInicioVigencia, PROP_DATA_INICIO_VIGENCIA); 
	}

	/**
	 * Set the value related to the column: data_inicio_vigencia
	 * @param dataInicioVigencia the data_inicio_vigencia value
	 */
	public void setDataInicioVigencia (java.util.Date dataInicioVigencia) {
//        java.util.Date dataInicioVigenciaOld = this.dataInicioVigencia;
		this.dataInicioVigencia = dataInicioVigencia;
//        this.getPropertyChangeSupport().firePropertyChange ("dataInicioVigencia", dataInicioVigenciaOld, dataInicioVigencia);
	}



	/**
	 * Return the value associated with the column: cd_tab_brasindice
	 */
	public br.com.ksisolucoes.vo.entradas.estoque.ProdutoBrasindice getProdutoBrasindice () {
		return getPropertyValue(this, produtoBrasindice, PROP_PRODUTO_BRASINDICE); 
	}

	/**
	 * Set the value related to the column: cd_tab_brasindice
	 * @param produtoBrasindice the cd_tab_brasindice value
	 */
	public void setProdutoBrasindice (br.com.ksisolucoes.vo.entradas.estoque.ProdutoBrasindice produtoBrasindice) {
//        br.com.ksisolucoes.vo.entradas.estoque.ProdutoBrasindice produtoBrasindiceOld = this.produtoBrasindice;
		this.produtoBrasindice = produtoBrasindice;
//        this.getPropertyChangeSupport().firePropertyChange ("produtoBrasindice", produtoBrasindiceOld, produtoBrasindice);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.entradas.estoque.ProdutoBrasindiceItem)) return false;
		else {
			br.com.ksisolucoes.vo.entradas.estoque.ProdutoBrasindiceItem produtoBrasindiceItem = (br.com.ksisolucoes.vo.entradas.estoque.ProdutoBrasindiceItem) obj;
			if (null == this.getCodigo() || null == produtoBrasindiceItem.getCodigo()) return false;
			else return (this.getCodigo().equals(produtoBrasindiceItem.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}