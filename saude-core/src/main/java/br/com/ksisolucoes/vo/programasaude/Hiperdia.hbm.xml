<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.programasaude"  >
    <class name="Hiperdia" table="hiperdia" >
        
        <id
            column="seq_hiperdia"
            name="codigo"
            type="java.lang.Long"
        >
            <generator class="sequence">
                <param name="sequence">hiperdia_seq</param>
            </generator>
        </id>
        <version column="version" name="version" type="long" />

        <many-to-one
            class="br.com.ksisolucoes.vo.programasaude.ProgramaSaudeUsuario"
            column="CD_PROGRAMA_SAUDE"
            name="ProgramaSaudeUsuario"
            not-null="true"
        />
 
        <many-to-one
            class="br.com.ksisolucoes.vo.prontuario.basico.Atendimento"
            name="atendimento"
            not-null="true"
        >
            <column name="nr_atendimento"/>
        </many-to-one>
  
        <property
            name="pressaoArterialSistolica"
            column="pas"
            type="java.lang.Long"
        />

        <property
            name="pressaoArterialDiastolica"
            column="pad"
            type="java.lang.Long"
        />

        <property
            name="cintura"
            column="cintura"
            type="java.lang.Long"
        />

        <property
            name="peso"
            column="peso"
            type="java.lang.Double"
        />

        <property
            name="altura"
            column="altura"
            type="java.lang.Double"
        />

        <property
            name="glicemia"
            column="glicemia"
            type="java.lang.Long"
        />

        <property
            name="glicemiaTipo"
            column="glicemia_tipo"
            type="java.lang.Long"
        />

        <property
            name="antecedentesFamiliaresCardiovasculares"
            column="ant_fam_cardiovascular"
            type="java.lang.String"
            length="1"
        />

        <property
            name="diabeteTipo1"
            column="diabete_tipo1"
            type="java.lang.String"
            length="1"
        />

        <property
            name="diabeteTipo2"
            column="diabete_tipo2"
            type="java.lang.String"
            length="1"
        />

        <property
            name="tabagismo"
            column="tabagismo"
            type="java.lang.String"
            length="1"
        />

        <property
            name="sedentarismo"
            column="sedentarismo"
            type="java.lang.String"
            length="1"
        />

        <property
            name="sobrepesoObesidade"
            column="sobrepeso_obesidade"
            type="java.lang.String"
            length="1"
        />

        <property
            name="hipertensaoArterial"
            column="hipertensao_arterial"
            type="java.lang.String"
            length="1"
        />

        <property
            name="infartoAgudoMiocardio"
            column="infarto_agudo_miocardio"
            type="java.lang.String"
            length="1"
        />

        <property
            name="outrasCoronariopatias"
            column="outras_coronariopatias"
            type="java.lang.String"
            length="1"
        />

        <property
            name="avc"
            column="avc"
            type="java.lang.String"
            length="1"
        />

        <property
            name="peDiabetico"
            column="pe_diabetico"
            type="java.lang.String"
            length="1"
        />

        <property
            name="amputacaoDiabete"
            column="amputacao_diabete"
            type="java.lang.String"
            length="1"
        />

        <property
            name="doencaRenal"
            column="doenca_renal"
            type="java.lang.String"
            length="1"
        />

        <property
            name="tratamentoNaoMedicamentoso"
            column="trat_medicamentoso"
            type="java.lang.String"
            length="1"
        />

    </class>
</hibernate-mapping>
