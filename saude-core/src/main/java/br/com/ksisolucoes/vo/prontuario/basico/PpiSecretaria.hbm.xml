<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.prontuario.basico">
    <class name="PpiSecretaria" table="ppi_secretaria">

        <id name="codigo" type="java.lang.Long" column="cd_ppi_secretaria">
            <generator class="sequence">
                <param name="sequence">ppi_secretaria_cd_ppi_secretaria_seq</param>
            </generator>
        </id>

        <version column="version" name="version" type="long"/>

        <many-to-one
                name="secretaria"
                class="br.com.ksisolucoes.vo.basico.Empresa"
                column="cd_secretaria"
                not-null="true"
        />

        <property
                name="dtCompetencia"
                column="dt_competencia"
                type="java.util.Date"
                not-null="true"
        />

        <property
                name="vlPpi"
                column="vl_ppi"
                type="java.lang.Double"
                not-null="true"
        />
        
        <property
                name="vlGlobal"
                column="vl_global"
                type="java.lang.Double"
                not-null="true"
        />

        <property
                name="vlUsado"
                column="vl_usado"
                type="java.lang.Double"
                not-null="true"
        />

        <property
                name="vlAdicional"
                column="vl_adicional"
                type="java.lang.Double"
                not-null="true"
        />

        <property
                name="stSituacao"
                column="st_situacao"
                type="java.lang.Long"
                not-null="true"
        />

        <property
                name="contratosAdicionais"
                column="contratos_adicionais"
                type="java.lang.String"
                not-null="true"
                length="1000"
        />

        <property
                name="stFoiResgatado"
                column="st_foi_resgatado"
                type="java.lang.Boolean"
                not-null="true"
        />

        <property
                name="permiteResgate"
                column="permite_resgate"
                type="java.lang.Boolean"
                not-null="false"
        />

        <many-to-one
                     class="br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimentoClassificacao"
                     name="tipoProcedimentoClassificacao"
                     not-null="false">
            <column name="cd_tp_pro_cla" />
        </many-to-one>

        <property
                name="vlDecremento"
                column="vl_decremento"
                type="java.lang.Double"
                not-null="false"
        />
    </class>
</hibernate-mapping>