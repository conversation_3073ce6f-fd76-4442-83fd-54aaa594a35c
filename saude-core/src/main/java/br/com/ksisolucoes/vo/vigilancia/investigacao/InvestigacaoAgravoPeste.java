package br.com.ksisolucoes.vo.vigilancia.investigacao;

import java.io.Serializable;

import br.com.ksisolucoes.vo.vigilancia.investigacao.base.BaseInvestigacaoAgravoPeste;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class InvestigacaoAgravoPeste extends BaseInvestigacaoAgravoPeste implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public InvestigacaoAgravoPeste () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public InvestigacaoAgravoPeste (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public InvestigacaoAgravoPeste (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravo,
		java.lang.String flagInformacoesComplementares) {

		super (
			codigo,
			registroAgravo,
			flagInformacoesComplementares);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}