<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.consorcio"  >
    <class name="ConsorcioPrestadorEdital"  table="consorcio_prestador_edital" >

        <id
                name="codigo"
                type="java.lang.Long"
                column="cd_consorcio_prestador_edital"
        >
            <generator class="assigned"/>
        </id>

        <version column="version" name="version" type="long" />

        <many-to-one
                name="consorcioPrestador"
                class="br.com.ksisolucoes.vo.consorcio.ConsorcioPrestador"
                column="cd_prestador"
                not-null="true"
        />

        <many-to-one
                name="tabelaPrecoEdital"
                class="br.com.ksisolucoes.vo.consorcio.TabelaPrecoEdital"
                column="cd_tabela_preco_edital"
                not-null="true"
        />

        <property
                name="dataCadastro"
                type="timestamp"
                column="dt_cadastro"
                not-null="true"
        />

        <many-to-one
                name="usuario"
                class="br.com.ksisolucoes.vo.controle.Usuario"
                column="cd_usuario"
                not-null="true"
        />

        <many-to-one
                name="contratoEdital"
                class="br.com.ksisolucoes.vo.consorcio.ContratoEdital"
                column="cd_contrato_edital"
        />

    </class>
</hibernate-mapping>