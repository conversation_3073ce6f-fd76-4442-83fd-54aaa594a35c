package br.com.ksisolucoes.vo.prontuario.basico;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import java.io.Serializable;

import br.com.ksisolucoes.vo.prontuario.basico.base.BaseRequisicaoImunologia;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class RequisicaoImunologia extends BaseRequisicaoImunologia implements CodigoManager {
	private static final long serialVersionUID = 1L;
        
        public static enum ExamePreNatal implements IEnum<ExamePreNatal> {

            TOXOPLASMOSE(1L, Bundle.getStringApplication("rotulo_toxoplasmose")),
            CITOMEGALOVIRUS(2L, Bundle.getStringApplication("rotulo_citomegalovirus"));

            private Long value;
            private String descricao;

            private ExamePreNatal(Long value, String descricao) {
                this.value = value;
                this.descricao = descricao;
            }

            @Override
            public Long value() {
                return this.value;
            }

            @Override
            public String descricao() {
                return this.descricao;
            }

            @Override
            public String toString() {
                return descricao;
            }

            public static ExamePreNatal valueOf(Long value) {
                for (ExamePreNatal examePreNatal : ExamePreNatal.values()) {
                    if (examePreNatal.value().equals(value)) {
                        return examePreNatal;
                    }
                }
                return null;
            }
        }
        
        public static enum OutrosExames implements IEnum<OutrosExames> {

            SOROLOGIA_CHAGAS(1L, Bundle.getStringApplication("rotulo_sorologia_chagas")),
            BRUCELOSE(2L, Bundle.getStringApplication("rotulo_brucelose")),
            TOXOAVIDEZ(4L, Bundle.getStringApplication("rotulo_toxoavidez")),
            SIFILIS(8L, Bundle.getStringApplication("rotulo_sifilis_teste_confirmatorio"));

            private Long value;
            private String descricao;

            private OutrosExames(Long value, String descricao) {
                this.value = value;
                this.descricao = descricao;
            }

            @Override
            public Long value() {
                return this.value;
            }

            @Override
            public String descricao() {
                return this.descricao;
            }

            @Override
            public String toString() {
                return descricao;
            }

            public static OutrosExames valueOf(Long value) {
                for (OutrosExames outrosExames : OutrosExames.values()) {
                    if (outrosExames.value().equals(value)) {
                        return outrosExames;
                    }
                }
                return null;
            }
        }

/*[CONSTRUCTOR MARKER BEGIN]*/
	public RequisicaoImunologia () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public RequisicaoImunologia (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public RequisicaoImunologia (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.prontuario.basico.ExameRequisicao exameRequisicao) {

		super (
			codigo,
			exameRequisicao);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}