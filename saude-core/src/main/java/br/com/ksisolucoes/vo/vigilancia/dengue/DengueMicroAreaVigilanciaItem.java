package br.com.ksisolucoes.vo.vigilancia.dengue;

import java.io.Serializable;

import br.com.ksisolucoes.vo.vigilancia.dengue.base.BaseDengueMicroAreaVigilanciaItem;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class DengueMicroAreaVigilanciaItem extends BaseDengueMicroAreaVigilanciaItem implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public DengueMicroAreaVigilanciaItem () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public DengueMicroAreaVigilanciaItem (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public DengueMicroAreaVigilanciaItem (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.dengue.DengueMicroAreaVigilancia dengueMicroAreaVigilancia,
		br.com.ksisolucoes.vo.vigilancia.dengue.DengueLocalidade dengueLocalidade) {

		super (
			codigo,
			dengueMicroAreaVigilancia,
			dengueLocalidade);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}