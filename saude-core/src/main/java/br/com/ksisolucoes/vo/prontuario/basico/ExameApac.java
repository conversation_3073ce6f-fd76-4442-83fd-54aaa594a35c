package br.com.ksisolucoes.vo.prontuario.basico;

import br.com.celk.util.StringUtil;
import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.prontuario.basico.base.BaseExameApac;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.io.Serializable;


@JsonIgnoreProperties(ignoreUnknown=true)
public class ExameApac extends BaseExameApac implements CodigoManager {
	private static final long serialVersionUID = 1L;
        public static final String PROP_DESCRICAO_STATUS = "descricaoStatus";

        public enum Status implements IEnum {
            ABERTO(0L, Bundle.getStringApplication("rotulo_aberto")),
            CANCELADO(1L, Bundle.getStringApplication("rotulo_cancelado")),
            CONFIRMADO(2L, Bundle.getStringApplication("rotulo_confirmado")),
            AUTORIZADO(3L, Bundle.getStringApplication("rotulo_autorizado")),
            AGENDADO(4L, Bundle.getStringApplication("rotulo_agendado"))
            ;

            private Long value;
            private String descricao;

            private Status(Long value, String descricao) {
                this.value = value;
                this.descricao = descricao;
            }

            public static Status valeuOf(Long value) {
                for (Status status : Status.values()) {
                    if (status.value().equals(value)) {
                        return status;
                    }
                }
                return null;
            }

            @Override
            public Long value() {
                return value;
            }

            @Override
            public String descricao() {
                return descricao;
            }

        }

/*[CONSTRUCTOR MARKER BEGIN]*/
	public ExameApac () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public ExameApac (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public ExameApac (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.prontuario.basico.Exame exame,
		java.lang.String descricaoDiagnostico,
		java.lang.Long possuiExameComplementar,
		java.lang.Long status) {

		super (
			codigo,
			exame,
			descricaoDiagnostico,
			possuiExameComplementar,
			status);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
    
    public String getDescricaoStatus(){
        Status status = Status.valeuOf(getStatus());
        if (status != null && status.descricao != null) {
            return status.descricao();
        }
        return "";
    }

    public String getExameComplementarFormatado() {
        String exameComplementar;
        if (getExameComplementar() == null) {
            exameComplementar = "";
        } else {
            exameComplementar = getExameComplementar();
        }
        if (RepositoryComponentDefault.SIM_LONG.equals(getPossuiExameComplementar())) {
            return "Sim. " + exameComplementar;
        }
        return "Não. " + exameComplementar;
    }

    public String getResumoAnamneseSemEspaco() {
        return StringUtil.limparHtml(getResumoAnamnese(), true);
    }

    public String getExameComplementarSemEspaco() {
        return StringUtil.limparHtml(getExameComplementarFormatado(), true);
    }

    public String getJustificativaProcedimentoSemEspaco() {
        return StringUtil.limparHtml(getJustificativaProcedimento(), true);
    }
}