package br.com.ksisolucoes.vo.vacina.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the aplicacao_vac_campanha_pacientes table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="aplicacao_vac_campanha_pacientes"
 */

public abstract class BaseAplicacaoVacinaCampanhaPacientes extends BaseRootVO implements Serializable {

	public static String REF = "AplicacaoVacinaCampanhaPacientes";
	public static final String PROP_VACINA_APLICACAO = "vacinaAplicacao";
	public static final String PROP_GRUPO_ATENDIMENTO = "grupoAtendimento";
	public static final String PROP_USUARIO_CADSUS = "usuarioCadsus";
	public static final String PROP_USUARIO = "usuario";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_DATA_CADASTRO = "dataCadastro";
	public static final String PROP_APLICACAO_VACINA_CAMPANHA = "aplicacaoVacinaCampanha";


	// constructors
	public BaseAplicacaoVacinaCampanhaPacientes () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseAplicacaoVacinaCampanhaPacientes (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseAplicacaoVacinaCampanhaPacientes (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vacina.AplicacaoVacinaCampanha aplicacaoVacinaCampanha,
		br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		java.util.Date dataCadastro) {

		this.setCodigo(codigo);
		this.setAplicacaoVacinaCampanha(aplicacaoVacinaCampanha);
		this.setUsuarioCadsus(usuarioCadsus);
		this.setUsuario(usuario);
		this.setDataCadastro(dataCadastro);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Long grupoAtendimento;
	private java.util.Date dataCadastro;

	// many to one
	private br.com.ksisolucoes.vo.vacina.AplicacaoVacinaCampanha aplicacaoVacinaCampanha;
	private br.com.ksisolucoes.vo.vacina.VacinaAplicacao vacinaAplicacao;
	private br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus;
	private br.com.ksisolucoes.vo.controle.Usuario usuario;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_aplicacao_vac_campanha_pacientes"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: grupo_atendimento
	 */
	public java.lang.Long getGrupoAtendimento () {
		return getPropertyValue(this, grupoAtendimento, PROP_GRUPO_ATENDIMENTO); 
	}

	/**
	 * Set the value related to the column: grupo_atendimento
	 * @param grupoAtendimento the grupo_atendimento value
	 */
	public void setGrupoAtendimento (java.lang.Long grupoAtendimento) {
//        java.lang.Long grupoAtendimentoOld = this.grupoAtendimento;
		this.grupoAtendimento = grupoAtendimento;
//        this.getPropertyChangeSupport().firePropertyChange ("grupoAtendimento", grupoAtendimentoOld, grupoAtendimento);
	}



	/**
	 * Return the value associated with the column: dt_cadastro
	 */
	public java.util.Date getDataCadastro () {
		return getPropertyValue(this, dataCadastro, PROP_DATA_CADASTRO); 
	}

	/**
	 * Set the value related to the column: dt_cadastro
	 * @param dataCadastro the dt_cadastro value
	 */
	public void setDataCadastro (java.util.Date dataCadastro) {
//        java.util.Date dataCadastroOld = this.dataCadastro;
		this.dataCadastro = dataCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCadastro", dataCadastroOld, dataCadastro);
	}



	/**
	 * Return the value associated with the column: cd_aplicacao_vacina_campanha
	 */
	public br.com.ksisolucoes.vo.vacina.AplicacaoVacinaCampanha getAplicacaoVacinaCampanha () {
		return getPropertyValue(this, aplicacaoVacinaCampanha, PROP_APLICACAO_VACINA_CAMPANHA); 
	}

	/**
	 * Set the value related to the column: cd_aplicacao_vacina_campanha
	 * @param aplicacaoVacinaCampanha the cd_aplicacao_vacina_campanha value
	 */
	public void setAplicacaoVacinaCampanha (br.com.ksisolucoes.vo.vacina.AplicacaoVacinaCampanha aplicacaoVacinaCampanha) {
//        br.com.ksisolucoes.vo.vacina.AplicacaoVacinaCampanha aplicacaoVacinaCampanhaOld = this.aplicacaoVacinaCampanha;
		this.aplicacaoVacinaCampanha = aplicacaoVacinaCampanha;
//        this.getPropertyChangeSupport().firePropertyChange ("aplicacaoVacinaCampanha", aplicacaoVacinaCampanhaOld, aplicacaoVacinaCampanha);
	}



	/**
	 * Return the value associated with the column: cd_vac_aplicacao
	 */
	public br.com.ksisolucoes.vo.vacina.VacinaAplicacao getVacinaAplicacao () {
		return getPropertyValue(this, vacinaAplicacao, PROP_VACINA_APLICACAO); 
	}

	/**
	 * Set the value related to the column: cd_vac_aplicacao
	 * @param vacinaAplicacao the cd_vac_aplicacao value
	 */
	public void setVacinaAplicacao (br.com.ksisolucoes.vo.vacina.VacinaAplicacao vacinaAplicacao) {
//        br.com.ksisolucoes.vo.vacina.VacinaAplicacao vacinaAplicacaoOld = this.vacinaAplicacao;
		this.vacinaAplicacao = vacinaAplicacao;
//        this.getPropertyChangeSupport().firePropertyChange ("vacinaAplicacao", vacinaAplicacaoOld, vacinaAplicacao);
	}



	/**
	 * Return the value associated with the column: cd_usu_cadsus
	 */
	public br.com.ksisolucoes.vo.cadsus.UsuarioCadsus getUsuarioCadsus () {
		return getPropertyValue(this, usuarioCadsus, PROP_USUARIO_CADSUS); 
	}

	/**
	 * Set the value related to the column: cd_usu_cadsus
	 * @param usuarioCadsus the cd_usu_cadsus value
	 */
	public void setUsuarioCadsus (br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus) {
//        br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsusOld = this.usuarioCadsus;
		this.usuarioCadsus = usuarioCadsus;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCadsus", usuarioCadsusOld, usuarioCadsus);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuario the cd_usuario value
	 */
	public void setUsuario (br.com.ksisolucoes.vo.controle.Usuario usuario) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.vacina.AplicacaoVacinaCampanhaPacientes)) return false;
		else {
			br.com.ksisolucoes.vo.vacina.AplicacaoVacinaCampanhaPacientes aplicacaoVacinaCampanhaPacientes = (br.com.ksisolucoes.vo.vacina.AplicacaoVacinaCampanhaPacientes) obj;
			if (null == this.getCodigo() || null == aplicacaoVacinaCampanhaPacientes.getCodigo()) return false;
			else return (this.getCodigo().equals(aplicacaoVacinaCampanhaPacientes.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}