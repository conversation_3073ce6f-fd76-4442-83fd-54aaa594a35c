package br.com.ksisolucoes.vo.vigilancia.dengue.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the dengue_armadilha table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="dengue_armadilha"
 */

public abstract class BaseDengueArmadilha extends BaseRootVO implements Serializable {

	public static String REF = "DengueArmadilha";
	public static final String PROP_DATA_INSTALACAO = "dataInstalacao";
	public static final String PROP_TIPO_ARMADILHA = "tipoArmadilha";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_DATA_CADASTRO = "dataCadastro";
	public static final String PROP_DENGUE_LOCALIDADE = "dengueLocalidade";
	public static final String PROP_DESCRICAO = "descricao";
	public static final String PROP_COMPLEMENTO_LOGRADOURO = "complementoLogradouro";
	public static final String PROP_SITUACAO = "situacao";
	public static final String PROP_USUARIO_CADASTRO = "usuarioCadastro";
	public static final String PROP_NUMERO_LOGRADOURO = "numeroLogradouro";
	public static final String PROP_DENGUE_TIPO_IMOVEL = "dengueTipoImovel";
	public static final String PROP_MOTIVO_INATIVACAO = "motivoInativacao";
	public static final String PROP_LATITUDE = "latitude";
	public static final String PROP_DATA_INATIVACAO = "dataInativacao";
	public static final String PROP_LONGITUDE = "longitude";
	public static final String PROP_VIGILANCIA_ENDERECO = "vigilanciaEndereco";
	public static final String PROP_NUMERO_QUARTEIRAO = "numeroQuarteirao";


	// constructors
	public BaseDengueArmadilha () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseDengueArmadilha (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseDengueArmadilha (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco vigilanciaEndereco,
		br.com.ksisolucoes.vo.vigilancia.dengue.DengueLocalidade dengueLocalidade,
		br.com.ksisolucoes.vo.vigilancia.dengue.DengueTipoImovel dengueTipoImovel,
		br.com.ksisolucoes.vo.controle.Usuario usuarioCadastro,
		java.lang.String descricao,
		java.util.Date dataInstalacao,
		java.lang.Long tipoArmadilha,
		java.lang.Long situacao,
		java.util.Date dataCadastro) {

		this.setCodigo(codigo);
		this.setVigilanciaEndereco(vigilanciaEndereco);
		this.setDengueLocalidade(dengueLocalidade);
		this.setDengueTipoImovel(dengueTipoImovel);
		this.setUsuarioCadastro(usuarioCadastro);
		this.setDescricao(descricao);
		this.setDataInstalacao(dataInstalacao);
		this.setTipoArmadilha(tipoArmadilha);
		this.setSituacao(situacao);
		this.setDataCadastro(dataCadastro);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String descricao;
	private java.util.Date dataInstalacao;
	private java.lang.String complementoLogradouro;
	private java.lang.String numeroLogradouro;
	private java.lang.Long numeroQuarteirao;
	private java.lang.Long tipoArmadilha;
	private java.lang.Long situacao;
	private java.util.Date dataInativacao;
	private java.lang.String motivoInativacao;
	private java.util.Date dataCadastro;
	private java.lang.Double latitude;
	private java.lang.Double longitude;

	// many to one
	private br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco vigilanciaEndereco;
	private br.com.ksisolucoes.vo.vigilancia.dengue.DengueLocalidade dengueLocalidade;
	private br.com.ksisolucoes.vo.vigilancia.dengue.DengueTipoImovel dengueTipoImovel;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioCadastro;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_dengue_armadilha"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: descricao
	 */
	public java.lang.String getDescricao () {
		return getPropertyValue(this, descricao, PROP_DESCRICAO); 
	}

	/**
	 * Set the value related to the column: descricao
	 * @param descricao the descricao value
	 */
	public void setDescricao (java.lang.String descricao) {
//        java.lang.String descricaoOld = this.descricao;
		this.descricao = descricao;
//        this.getPropertyChangeSupport().firePropertyChange ("descricao", descricaoOld, descricao);
	}



	/**
	 * Return the value associated with the column: dt_instalacao
	 */
	public java.util.Date getDataInstalacao () {
		return getPropertyValue(this, dataInstalacao, PROP_DATA_INSTALACAO); 
	}

	/**
	 * Set the value related to the column: dt_instalacao
	 * @param dataInstalacao the dt_instalacao value
	 */
	public void setDataInstalacao (java.util.Date dataInstalacao) {
//        java.util.Date dataInstalacaoOld = this.dataInstalacao;
		this.dataInstalacao = dataInstalacao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataInstalacao", dataInstalacaoOld, dataInstalacao);
	}



	/**
	 * Return the value associated with the column: comp_logradouro
	 */
	public java.lang.String getComplementoLogradouro () {
		return getPropertyValue(this, complementoLogradouro, PROP_COMPLEMENTO_LOGRADOURO); 
	}

	/**
	 * Set the value related to the column: comp_logradouro
	 * @param complementoLogradouro the comp_logradouro value
	 */
	public void setComplementoLogradouro (java.lang.String complementoLogradouro) {
//        java.lang.String complementoLogradouroOld = this.complementoLogradouro;
		this.complementoLogradouro = complementoLogradouro;
//        this.getPropertyChangeSupport().firePropertyChange ("complementoLogradouro", complementoLogradouroOld, complementoLogradouro);
	}



	/**
	 * Return the value associated with the column: nr_logradouro
	 */
	public java.lang.String getNumeroLogradouro () {
		return getPropertyValue(this, numeroLogradouro, PROP_NUMERO_LOGRADOURO); 
	}

	/**
	 * Set the value related to the column: nr_logradouro
	 * @param numeroLogradouro the nr_logradouro value
	 */
	public void setNumeroLogradouro (java.lang.String numeroLogradouro) {
//        java.lang.String numeroLogradouroOld = this.numeroLogradouro;
		this.numeroLogradouro = numeroLogradouro;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroLogradouro", numeroLogradouroOld, numeroLogradouro);
	}



	/**
	 * Return the value associated with the column: nr_quarteirao
	 */
	public java.lang.Long getNumeroQuarteirao () {
		return getPropertyValue(this, numeroQuarteirao, PROP_NUMERO_QUARTEIRAO); 
	}

	/**
	 * Set the value related to the column: nr_quarteirao
	 * @param numeroQuarteirao the nr_quarteirao value
	 */
	public void setNumeroQuarteirao (java.lang.Long numeroQuarteirao) {
//        java.lang.Long numeroQuarteiraoOld = this.numeroQuarteirao;
		this.numeroQuarteirao = numeroQuarteirao;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroQuarteirao", numeroQuarteiraoOld, numeroQuarteirao);
	}



	/**
	 * Return the value associated with the column: tipo_armadilha
	 */
	public java.lang.Long getTipoArmadilha () {
		return getPropertyValue(this, tipoArmadilha, PROP_TIPO_ARMADILHA); 
	}

	/**
	 * Set the value related to the column: tipo_armadilha
	 * @param tipoArmadilha the tipo_armadilha value
	 */
	public void setTipoArmadilha (java.lang.Long tipoArmadilha) {
//        java.lang.Long tipoArmadilhaOld = this.tipoArmadilha;
		this.tipoArmadilha = tipoArmadilha;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoArmadilha", tipoArmadilhaOld, tipoArmadilha);
	}



	/**
	 * Return the value associated with the column: situacao
	 */
	public java.lang.Long getSituacao () {
		return getPropertyValue(this, situacao, PROP_SITUACAO); 
	}

	/**
	 * Set the value related to the column: situacao
	 * @param situacao the situacao value
	 */
	public void setSituacao (java.lang.Long situacao) {
//        java.lang.Long situacaoOld = this.situacao;
		this.situacao = situacao;
//        this.getPropertyChangeSupport().firePropertyChange ("situacao", situacaoOld, situacao);
	}



	/**
	 * Return the value associated with the column: dt_inativacao
	 */
	public java.util.Date getDataInativacao () {
		return getPropertyValue(this, dataInativacao, PROP_DATA_INATIVACAO); 
	}

	/**
	 * Set the value related to the column: dt_inativacao
	 * @param dataInativacao the dt_inativacao value
	 */
	public void setDataInativacao (java.util.Date dataInativacao) {
//        java.util.Date dataInativacaoOld = this.dataInativacao;
		this.dataInativacao = dataInativacao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataInativacao", dataInativacaoOld, dataInativacao);
	}



	/**
	 * Return the value associated with the column: motivo_inativacao
	 */
	public java.lang.String getMotivoInativacao () {
		return getPropertyValue(this, motivoInativacao, PROP_MOTIVO_INATIVACAO); 
	}

	/**
	 * Set the value related to the column: motivo_inativacao
	 * @param motivoInativacao the motivo_inativacao value
	 */
	public void setMotivoInativacao (java.lang.String motivoInativacao) {
//        java.lang.String motivoInativacaoOld = this.motivoInativacao;
		this.motivoInativacao = motivoInativacao;
//        this.getPropertyChangeSupport().firePropertyChange ("motivoInativacao", motivoInativacaoOld, motivoInativacao);
	}



	/**
	 * Return the value associated with the column: dt_cadastro
	 */
	public java.util.Date getDataCadastro () {
		return getPropertyValue(this, dataCadastro, PROP_DATA_CADASTRO); 
	}

	/**
	 * Set the value related to the column: dt_cadastro
	 * @param dataCadastro the dt_cadastro value
	 */
	public void setDataCadastro (java.util.Date dataCadastro) {
//        java.util.Date dataCadastroOld = this.dataCadastro;
		this.dataCadastro = dataCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCadastro", dataCadastroOld, dataCadastro);
	}



	/**
	 * Return the value associated with the column: latitude
	 */
	public java.lang.Double getLatitude () {
		return getPropertyValue(this, latitude, PROP_LATITUDE); 
	}

	/**
	 * Set the value related to the column: latitude
	 * @param latitude the latitude value
	 */
	public void setLatitude (java.lang.Double latitude) {
//        java.lang.Double latitudeOld = this.latitude;
		this.latitude = latitude;
//        this.getPropertyChangeSupport().firePropertyChange ("latitude", latitudeOld, latitude);
	}



	/**
	 * Return the value associated with the column: longitude
	 */
	public java.lang.Double getLongitude () {
		return getPropertyValue(this, longitude, PROP_LONGITUDE); 
	}

	/**
	 * Set the value related to the column: longitude
	 * @param longitude the longitude value
	 */
	public void setLongitude (java.lang.Double longitude) {
//        java.lang.Double longitudeOld = this.longitude;
		this.longitude = longitude;
//        this.getPropertyChangeSupport().firePropertyChange ("longitude", longitudeOld, longitude);
	}



	/**
	 * Return the value associated with the column: cd_vigilancia_endereco
	 */
	public br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco getVigilanciaEndereco () {
		return getPropertyValue(this, vigilanciaEndereco, PROP_VIGILANCIA_ENDERECO); 
	}

	/**
	 * Set the value related to the column: cd_vigilancia_endereco
	 * @param vigilanciaEndereco the cd_vigilancia_endereco value
	 */
	public void setVigilanciaEndereco (br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco vigilanciaEndereco) {
//        br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco vigilanciaEnderecoOld = this.vigilanciaEndereco;
		this.vigilanciaEndereco = vigilanciaEndereco;
//        this.getPropertyChangeSupport().firePropertyChange ("vigilanciaEndereco", vigilanciaEnderecoOld, vigilanciaEndereco);
	}



	/**
	 * Return the value associated with the column: cd_dengue_localidade
	 */
	public br.com.ksisolucoes.vo.vigilancia.dengue.DengueLocalidade getDengueLocalidade () {
		return getPropertyValue(this, dengueLocalidade, PROP_DENGUE_LOCALIDADE); 
	}

	/**
	 * Set the value related to the column: cd_dengue_localidade
	 * @param dengueLocalidade the cd_dengue_localidade value
	 */
	public void setDengueLocalidade (br.com.ksisolucoes.vo.vigilancia.dengue.DengueLocalidade dengueLocalidade) {
//        br.com.ksisolucoes.vo.vigilancia.dengue.DengueLocalidade dengueLocalidadeOld = this.dengueLocalidade;
		this.dengueLocalidade = dengueLocalidade;
//        this.getPropertyChangeSupport().firePropertyChange ("dengueLocalidade", dengueLocalidadeOld, dengueLocalidade);
	}



	/**
	 * Return the value associated with the column: cd_dengue_tipo_imovel
	 */
	public br.com.ksisolucoes.vo.vigilancia.dengue.DengueTipoImovel getDengueTipoImovel () {
		return getPropertyValue(this, dengueTipoImovel, PROP_DENGUE_TIPO_IMOVEL); 
	}

	/**
	 * Set the value related to the column: cd_dengue_tipo_imovel
	 * @param dengueTipoImovel the cd_dengue_tipo_imovel value
	 */
	public void setDengueTipoImovel (br.com.ksisolucoes.vo.vigilancia.dengue.DengueTipoImovel dengueTipoImovel) {
//        br.com.ksisolucoes.vo.vigilancia.dengue.DengueTipoImovel dengueTipoImovelOld = this.dengueTipoImovel;
		this.dengueTipoImovel = dengueTipoImovel;
//        this.getPropertyChangeSupport().firePropertyChange ("dengueTipoImovel", dengueTipoImovelOld, dengueTipoImovel);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioCadastro () {
		return getPropertyValue(this, usuarioCadastro, PROP_USUARIO_CADASTRO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuarioCadastro the cd_usuario value
	 */
	public void setUsuarioCadastro (br.com.ksisolucoes.vo.controle.Usuario usuarioCadastro) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioCadastroOld = this.usuarioCadastro;
		this.usuarioCadastro = usuarioCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCadastro", usuarioCadastroOld, usuarioCadastro);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.vigilancia.dengue.DengueArmadilha)) return false;
		else {
			br.com.ksisolucoes.vo.vigilancia.dengue.DengueArmadilha dengueArmadilha = (br.com.ksisolucoes.vo.vigilancia.dengue.DengueArmadilha) obj;
			if (null == this.getCodigo() || null == dengueArmadilha.getCodigo()) return false;
			else return (this.getCodigo().equals(dengueArmadilha.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}