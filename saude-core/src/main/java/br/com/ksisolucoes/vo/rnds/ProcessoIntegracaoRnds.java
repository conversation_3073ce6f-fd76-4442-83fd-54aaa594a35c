package br.com.ksisolucoes.vo.rnds;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.rnds.base.BaseProcessoIntegracaoRnds;

import java.io.Serializable;



public class ProcessoIntegracaoRnds extends BaseProcessoIntegracaoRnds implements CodigoManager {
	private static final long serialVersionUID = 1L;
	public enum Status implements IEnum {

		PROCESSAMENTO(1L, Bundle.getStringApplication("rotulo_processamento")),
		PROCESSADO(2L, Bundle.getStringApplication("rotulo_processado")),
		ERRO(3L, Bundle.getStringApplication("rotulo_erro")),
		SEM_REGISTROS(4L, Bundle.getStringApplication("rotulo_processado_sem_registro"));

		private Long value;
		private String descricao;

		private Status(Long value, String descricao) {
			this.value = value;
			this.descricao = descricao;
		}

		public static Status valueOf(Long value) {
			for (Status status : Status.values()) {
				if (status.value().equals(value)) {
					return status;
				}
			}
			return null;
		}

		@Override
		public Long value() {
			return value;
		}

		@Override
		public String descricao() {
			return descricao;
		}
	}

	public enum TipoProcessamento implements IEnum {

		INTEGRACOES_COM_FALHA(1L, Bundle.getStringApplication("rotulo_integracoes_com_falha")),
		INTEGRACOES_NAO_CRIADAS(2L, Bundle.getStringApplication("rotulo_integracoes_nao_criadas"));

		private Long value;
		private String descricao;

		private TipoProcessamento(Long value, String descricao) {
			this.value = value;
			this.descricao = descricao;
		}

		public static TipoProcessamento valueOf(Long value) {
			for (TipoProcessamento tipoProcessamento : TipoProcessamento.values()) {
				if (tipoProcessamento.value().equals(value)) {
					return tipoProcessamento;
				}
			}
			return null;
		}

		@Override
		public Long value() {
			return value;
		}

		@Override
		public String descricao() {
			return descricao;
		}
	}

	public String getDescricaoStatus() {
		Status status = Status.valueOf(getStatus());
		if (status != null) {
			return status.descricao();
		}
		return null;
	}

/*[CONSTRUCTOR MARKER BEGIN]*/
	public ProcessoIntegracaoRnds () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public ProcessoIntegracaoRnds (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public ProcessoIntegracaoRnds (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.service.AsyncProcess asyncProcess,
		java.lang.Long status,
		java.lang.Long tipoProcessamento,
		java.util.Date dataProcesso) {

		super (
			codigo,
			asyncProcess,
			status,
			tipoProcessamento,
			dataProcesso);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}