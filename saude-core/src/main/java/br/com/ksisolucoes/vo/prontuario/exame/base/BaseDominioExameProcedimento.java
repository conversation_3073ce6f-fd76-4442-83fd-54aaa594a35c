package br.com.ksisolucoes.vo.prontuario.exame.base;

import java.io.Serializable;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;


/**
 * This is an object that contains data related to the dom_exame_procedimento table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="dom_exame_procedimento"
 */

public abstract class BaseDominioExameProcedimento extends BaseRootVO implements Serializable {

	public static String REF = "DominioExameProcedimento";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_KEYWORD = "keyword";
	public static final String PROP_DESCRICAO = "descricao";
	public static final String PROP_PROCEDIMENTO = "procedimento";
	public static final String PROP_TIPO_EXAME = "tipoExame";
	public static final String PROP_EXAME_PROCEDIMENTO = "exameProcedimento";


	// constructors
	public BaseDominioExameProcedimento () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseDominioExameProcedimento (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String descricao;
	private java.lang.String keyword;

	// many to one
	private br.com.ksisolucoes.vo.prontuario.basico.ExameProcedimento exameProcedimento;
	private br.com.ksisolucoes.vo.prontuario.basico.TipoExame tipoExame;
	private br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento procedimento;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_dominio"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: ds_exame
	 */
	public java.lang.String getDescricao () {
		return getPropertyValue(this, descricao, PROP_DESCRICAO); 
	}

	/**
	 * Set the value related to the column: ds_exame
	 * @param descricao the ds_exame value
	 */
	public void setDescricao (java.lang.String descricao) {
//        java.lang.String descricaoOld = this.descricao;
		this.descricao = descricao;
//        this.getPropertyChangeSupport().firePropertyChange ("descricao", descricaoOld, descricao);
	}



	/**
	 * Return the value associated with the column: keyword
	 */
	public java.lang.String getKeyword () {
		return getPropertyValue(this, keyword, PROP_KEYWORD); 
	}

	/**
	 * Set the value related to the column: keyword
	 * @param keyword the keyword value
	 */
	public void setKeyword (java.lang.String keyword) {
//        java.lang.String keywordOld = this.keyword;
		this.keyword = keyword;
//        this.getPropertyChangeSupport().firePropertyChange ("keyword", keywordOld, keyword);
	}



	/**
	 * Return the value associated with the column: cd_exame_procedimento
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.ExameProcedimento getExameProcedimento () {
		return getPropertyValue(this, exameProcedimento, PROP_EXAME_PROCEDIMENTO); 
	}

	/**
	 * Set the value related to the column: cd_exame_procedimento
	 * @param exameProcedimento the cd_exame_procedimento value
	 */
	public void setExameProcedimento (br.com.ksisolucoes.vo.prontuario.basico.ExameProcedimento exameProcedimento) {
//        br.com.ksisolucoes.vo.prontuario.basico.ExameProcedimento exameProcedimentoOld = this.exameProcedimento;
		this.exameProcedimento = exameProcedimento;
//        this.getPropertyChangeSupport().firePropertyChange ("exameProcedimento", exameProcedimentoOld, exameProcedimento);
	}



	/**
	 * Return the value associated with the column: cd_tp_exame
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.TipoExame getTipoExame () {
		return getPropertyValue(this, tipoExame, PROP_TIPO_EXAME); 
	}

	/**
	 * Set the value related to the column: cd_tp_exame
	 * @param tipoExame the cd_tp_exame value
	 */
	public void setTipoExame (br.com.ksisolucoes.vo.prontuario.basico.TipoExame tipoExame) {
//        br.com.ksisolucoes.vo.prontuario.basico.TipoExame tipoExameOld = this.tipoExame;
		this.tipoExame = tipoExame;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoExame", tipoExameOld, tipoExame);
	}



	/**
	 * Return the value associated with the column: cd_procedimento
	 */
	public br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento getProcedimento () {
		return getPropertyValue(this, procedimento, PROP_PROCEDIMENTO); 
	}

	/**
	 * Set the value related to the column: cd_procedimento
	 * @param procedimento the cd_procedimento value
	 */
	public void setProcedimento (br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento procedimento) {
//        br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento procedimentoOld = this.procedimento;
		this.procedimento = procedimento;
//        this.getPropertyChangeSupport().firePropertyChange ("procedimento", procedimentoOld, procedimento);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.exame.DominioExameProcedimento)) return false;
		else {
			br.com.ksisolucoes.vo.prontuario.exame.DominioExameProcedimento dominioExameProcedimento = (br.com.ksisolucoes.vo.prontuario.exame.DominioExameProcedimento) obj;
			if (null == this.getCodigo() || null == dominioExameProcedimento.getCodigo()) return false;
			else return (this.getCodigo().equals(dominioExameProcedimento.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}