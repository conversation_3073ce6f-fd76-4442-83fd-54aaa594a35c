package br.com.ksisolucoes.vo.agendamento.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the agenda_grade_exame table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="agenda_grade_exame"
 */

public abstract class BaseAgendaGradeExame extends BaseRootVO implements Serializable {

	public static String REF = "AgendaGradeExame";
	public static final String PROP_USUARIO = "usuario";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_AGENDA_GRADE = "agendaGrade";
	public static final String PROP_DATA_CADASTRO = "dataCadastro";
	public static final String PROP_EXAME_PROCEDIMENTO = "exameProcedimento";


	// constructors
	public BaseAgendaGradeExame () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseAgendaGradeExame (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseAgendaGradeExame (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.agendamento.AgendaGrade agendaGrade,
		br.com.ksisolucoes.vo.prontuario.basico.ExameProcedimento exameProcedimento,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		java.util.Date dataCadastro) {

		this.setCodigo(codigo);
		this.setAgendaGrade(agendaGrade);
		this.setExameProcedimento(exameProcedimento);
		this.setUsuario(usuario);
		this.setDataCadastro(dataCadastro);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.util.Date dataCadastro;

	// many to one
	private br.com.ksisolucoes.vo.agendamento.AgendaGrade agendaGrade;
	private br.com.ksisolucoes.vo.prontuario.basico.ExameProcedimento exameProcedimento;
	private br.com.ksisolucoes.vo.controle.Usuario usuario;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_agenda_grade_exame"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: dt_cadastro
	 */
	public java.util.Date getDataCadastro () {
		return getPropertyValue(this, dataCadastro, PROP_DATA_CADASTRO); 
	}

	/**
	 * Set the value related to the column: dt_cadastro
	 * @param dataCadastro the dt_cadastro value
	 */
	public void setDataCadastro (java.util.Date dataCadastro) {
//        java.util.Date dataCadastroOld = this.dataCadastro;
		this.dataCadastro = dataCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCadastro", dataCadastroOld, dataCadastro);
	}



	/**
	 * Return the value associated with the column: cd_ag_grade
	 */
	public br.com.ksisolucoes.vo.agendamento.AgendaGrade getAgendaGrade () {
		return getPropertyValue(this, agendaGrade, PROP_AGENDA_GRADE); 
	}

	/**
	 * Set the value related to the column: cd_ag_grade
	 * @param agendaGrade the cd_ag_grade value
	 */
	public void setAgendaGrade (br.com.ksisolucoes.vo.agendamento.AgendaGrade agendaGrade) {
//        br.com.ksisolucoes.vo.agendamento.AgendaGrade agendaGradeOld = this.agendaGrade;
		this.agendaGrade = agendaGrade;
//        this.getPropertyChangeSupport().firePropertyChange ("agendaGrade", agendaGradeOld, agendaGrade);
	}



	/**
	 * Return the value associated with the column: cd_exame_procedimento
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.ExameProcedimento getExameProcedimento () {
		return getPropertyValue(this, exameProcedimento, PROP_EXAME_PROCEDIMENTO); 
	}

	/**
	 * Set the value related to the column: cd_exame_procedimento
	 * @param exameProcedimento the cd_exame_procedimento value
	 */
	public void setExameProcedimento (br.com.ksisolucoes.vo.prontuario.basico.ExameProcedimento exameProcedimento) {
//        br.com.ksisolucoes.vo.prontuario.basico.ExameProcedimento exameProcedimentoOld = this.exameProcedimento;
		this.exameProcedimento = exameProcedimento;
//        this.getPropertyChangeSupport().firePropertyChange ("exameProcedimento", exameProcedimentoOld, exameProcedimento);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuario the cd_usuario value
	 */
	public void setUsuario (br.com.ksisolucoes.vo.controle.Usuario usuario) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.agendamento.AgendaGradeExame)) return false;
		else {
			br.com.ksisolucoes.vo.agendamento.AgendaGradeExame agendaGradeExame = (br.com.ksisolucoes.vo.agendamento.AgendaGradeExame) obj;
			if (null == this.getCodigo() || null == agendaGradeExame.getCodigo()) return false;
			else return (this.getCodigo().equals(agendaGradeExame.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}