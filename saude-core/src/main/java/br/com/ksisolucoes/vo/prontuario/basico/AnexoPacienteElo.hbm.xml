<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
"-//Hibernate/Hibernate Mapping DTD//EN"
"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.prontuario.basico"  >
    <class name="AnexoPacienteElo" table="anexo_paciente_elo" >

        <id
            column="cd_anexo_paciente_elo"
            name="codigo"
            type="java.lang.Long"
        >
            <generator class="assigned" />
        </id>

        <version column="version" name="version" type="long" />    

		<many-to-one
			class="br.com.ksisolucoes.vo.prontuario.basico.AnexoPaciente"
			name="anexoPaciente"
			not-null="true"
		>
            <column name="cd_anexo_paciente" />
        </many-to-one>

		<many-to-one
			class="br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo"
			name="gerenciadorArquivo"
			not-null="true"
		>
            <column name="cd_gerenciador_arquivo" />
        </many-to-one>

        <property
                column="uuid_tablet"
                name="uuidTablet"
                type="java.lang.String"
        />
        <property
                name="versionAll"
                column="version_all"
                type="java.lang.Long"
                not-null="true"
        />
    </class>
</hibernate-mapping>
