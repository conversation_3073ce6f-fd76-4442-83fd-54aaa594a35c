package br.com.ksisolucoes.vo.prontuario.basico;

import java.io.Serializable;

import br.com.ksisolucoes.util.Util;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.interfaces.PesquisaObjectInterface;
import br.com.ksisolucoes.vo.prontuario.basico.base.BaseTipoConvenio;



public class TipoConvenio extends BaseTipoConvenio implements CodigoManager, PesquisaObjectInterface {
	private static final long serialVersionUID = 1L;

        public static final String PROP_DESCRICAO_FORMATADO = "descricaoFormatado";

/*[CONSTRUCTOR MARKER BEGIN]*/
	public TipoConvenio () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public TipoConvenio (java.lang.Long codigo) {
		super(codigo);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    public String getDescricaoFormatado(){
        return Util.getDescricaoFormatado(getCodigo(), getDescricao());
    }

    @Override
    public String getDescricaoVO() {
        return getDescricao();
    }

    @Override
    public String getIdentificador() {
        return getCodigo().toString();
    }
}