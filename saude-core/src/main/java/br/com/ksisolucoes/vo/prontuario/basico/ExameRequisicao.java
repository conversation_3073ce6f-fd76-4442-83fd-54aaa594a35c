package br.com.ksisolucoes.vo.prontuario.basico;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Coalesce;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.prontuario.basico.base.BaseExameRequisicao;

import java.io.Serializable;
import java.text.SimpleDateFormat;

public class ExameRequisicao extends BaseExameRequisicao implements CodigoManager {

    private static final long serialVersionUID = 1L;
    public static final String PROP_VALOR_PROCEDIMENTO_TOTAL = "valorProcedimentoTotal";
    public static final String PROP_STATUS_FORMATADO = "statusFormatado";
    public static final String PROP_SITUACAO_LABORATORIO_FORMATADO = "situacaoLaboratorioFormatado";
    public static final String PROP_DATA_RESULTADO_FORMATADO = "dataResultadoFormatado";

    public ExameRequisicao() {
        super();
    }

    public enum SituacaoLaboratorio implements IEnum {

        PENDENTE(1L, Bundle.getStringApplication("rotulo_pendente")),
        CONCLUIDO(2L, Bundle.getStringApplication("rotulo_concluido")),
        CANCELADO(4L, Bundle.getStringApplication("rotulo_cancelado"));
        private Long value;
        private String descricao;

        private SituacaoLaboratorio(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }
        
        public static SituacaoLaboratorio valeuOf(Long value) {
            for (SituacaoLaboratorio situacaoLaboratorio : SituacaoLaboratorio.values()) {
                if (situacaoLaboratorio.value().equals(value)) {
                    return situacaoLaboratorio;
                }
            }
            return null;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }
    }

    public String getSituacaoFormatada() {
        StringBuilder sb = new StringBuilder();

        if (getSolicitacaoAgendamento() == null) {
            return Bundle.getStringApplication("rotulo_status_pendente");
        }

        Long status = getSolicitacaoAgendamento().getStatus();
        String dataAgendamento = getSolicitacaoAgendamento().getDataAgendamento() != null
                ? DataUtil.getFormatarDiaMesAno(getSolicitacaoAgendamento().getDataAgendamento()) : "";

        if (SolicitacaoAgendamento.STATUS_REGULACAO_PENDENTE.equals(status) || SolicitacaoAgendamento.STATUS_FILA_ESPERA.equals(status)) {
            sb.append(Bundle.getStringApplication("rotulo_fila_espera"));
        } else if (SolicitacaoAgendamento.STATUS_AGENDADO.equals(status) || SolicitacaoAgendamento.STATUS_REGULACAO_AGENDADO.equals(status) || SolicitacaoAgendamento.STATUS_AGENDADO_FORA_REDE.equals(status)) {
            sb.append(Bundle.getStringApplication("rotulo_regulacao_agendada", dataAgendamento));
        } else if (SolicitacaoAgendamento.STATUS_REGULACAO_NEGADO.equals(status)) {
            sb.append(Bundle.getStringApplication("rotulo_regulacao_negada"));
        } else if (SolicitacaoAgendamento.STATUS_REGULACAO_DEVOLVIDO.equals(status) || SolicitacaoAgendamento.STATUS_DEVOLVIDO.equals(status)) {
            sb.append(Bundle.getStringApplication("rotulo_devolvido"));
        } else if (SolicitacaoAgendamento.STATUS_CANCELADO.equals(status)) {
            sb.append(Bundle.getStringApplication("rotulo_status_cancelado"));
        } else if (SolicitacaoAgendamento.STATUS_AGUARDANDO_ANALISE.equals(status) || SolicitacaoAgendamento.STATUS_AGUARDANDO_AUTORIZACAO.equals(status)) {
            sb.append(Bundle.getStringApplication("rotulo_aguardando"));
        } else if (SolicitacaoAgendamento.STATUS_BLOQUEADO.equals(status)) {
            sb.append(Bundle.getStringApplication("rotulo_bloqueado"));
        } else {
            sb.append(Bundle.getStringApplication("rotulo_situacao_desconhecida"));
        }
        return sb.toString();
    }

    public enum ExameExterno implements IEnum {

        NAO(0L, Bundle.getStringApplication("rotulo_nao")),
        SIM(1L, Bundle.getStringApplication("rotulo_sim"));
        private Long value;
        private String descricao;

        private ExameExterno(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }
    }
    
    public enum Status implements IEnum {

        ABERTO(1L, Bundle.getStringApplication("rotulo_aberto")),
        CONCLUIDO(2L, Bundle.getStringApplication("rotulo_concluido")),
        NAO_REALIZADO(3L, Bundle.getStringApplication("rotulo_nao_realizado")),
        CANCELADO(4L, Bundle.getStringApplication("rotulo_cancelado"));
        private Long value;
        private String descricao;

        private Status(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }
        
        public static Status valeuOf(Long value) {
            for (Status motivoAlta : Status.values()) {
                if (motivoAlta.value().equals(value)) {
                    return motivoAlta;
                }
            }
            return null;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }
    }

    /*
     * [CONSTRUCTOR MARKER BEGIN]
	public ExameRequisicao () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public ExameRequisicao (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public ExameRequisicao (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.prontuario.basico.Exame exame,
		br.com.ksisolucoes.vo.prontuario.basico.ExameProcedimento exameProcedimento,
		java.lang.Long quantidade,
		java.lang.Double valorProcedimento,
		java.util.Date dataConfirmacaoPrestador) {

		super (
			codigo,
			exame,
			exameProcedimento,
			quantidade,
			valorProcedimento,
			dataConfirmacaoPrestador);
	}

     /* [CONSTRUCTOR MARKER END]
     */
    public void setCodigoManager(Serializable key) {
        this.setCodigo((java.lang.Long) key);
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    public Double getValorProcedimentoTotal() {
        return Coalesce.asLong(this.getQuantidade()) * Coalesce.asDouble(this.getValorProcedimento());
    }
    
    public String getStatusFormatado(){
        Status status = Status.valeuOf(getStatus());
        if (status != null && status.descricao != null) {
            return status.descricao();
        } 
        return "";
    }
    
    public String getSituacaoLaboratorioFormatado(){
        SituacaoLaboratorio situacaoLaboratorio = SituacaoLaboratorio.valeuOf(getSituacaoLaboratorio());
        if (situacaoLaboratorio != null && situacaoLaboratorio.descricao != null) {
            return situacaoLaboratorio.descricao();
        } 
        return "";
    }
    
    public String getDataResultadoFormatado(){
        if(getDataResultado()!= null){
            return new SimpleDateFormat("dd/MM/yyyy").format(getDataResultado());
        }
        return "";
    }

    public String getDescricaoResultadoFormatado() {
        if (Coalesce.asString(getDescricaoResultado()).length() > 0) {
            return getDescricaoResultado();
        } else{
            return Bundle.getStringApplication("rotulo_nao_realizado");
        }
    }
}
