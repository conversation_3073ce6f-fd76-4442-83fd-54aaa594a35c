<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.entradas.recebimento" >
	<class name="RegistroItemNotaFiscalProdutoSolicitado" table="registro_itnf_pro_solicitado">

		<composite-id class="RegistroItemNotaFiscalProdutoSolicitadoPK" name="id">
			<key-many-to-one class="RegistroItemNotaFiscal" name="registroItemNotaFiscal">
				<column name="cd_reg_it_nf" />
			</key-many-to-one>
			
			<key-many-to-one class="br.com.ksisolucoes.vo.cadsus.UsuarioCadsus" name="usuarioCadsus">
				<column name="cd_usu_cadsus" />
			</key-many-to-one>
			
			<key-property
				column="lote"
				name="lote"
				type="java.lang.String"
	            length="20"
			 />
			
		</composite-id> <version column="version" name="version" type="long" />

        <property
			column="qtdade"
			name="quantidade"
			type="java.lang.Double"
            not-null="true"
		 />
		 
		 <property
			column="gerar_reserva"
			name="gerarReserva"
			type="java.lang.String"
            not-null="true"
            length="1"
		 />
		 
		
	</class>
</hibernate-mapping>
