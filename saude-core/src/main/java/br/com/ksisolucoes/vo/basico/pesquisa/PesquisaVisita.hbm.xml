<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.basico.pesquisa"  >
    <class name="PesquisaVisita" table="pesquisa_visita">
        <id
            column="cd_pesq_visita"
            name="codigo"
            type="java.lang.Long"
        >
            <generator class="assigned" />
        </id> 
        <version column="version" name="version" type="long" />

        <many-to-one
            class="br.com.ksisolucoes.vo.basico.pesquisa.PerguntaResposta"
            name="perguntaResposta"
        >
            <column name="cd_resposta" />
        </many-to-one>
        
        <many-to-one
            class="br.com.ksisolucoes.vo.cadsus.VisitaDomiciliar"
            name="visitaDomiciliar"
        >
            <column name="cd_visita" />
        </many-to-one>

        <many-to-one
            class="br.com.ksisolucoes.vo.basico.pesquisa.PesquisaPergunta"
            name="pesquisaPergunta"
        >
            <column name="cd_pesq_pergunta" />
        </many-to-one>

        <property
            column="version_all"
            name="versionAll"
            not-null="true"
            type="java.lang.Long"
        />

		<property
            column="idade"
            name="idade"
            type="java.lang.Long"
        />

		<property
            column="sexo"
            name="sexo"
            type="java.lang.String"
        />

		<many-to-one
            class="br.com.ksisolucoes.vo.basico.EquipeMicroArea"
            name="equipeMicroArea"
        >
            <column name="cd_eqp_micro_area" />
        </many-to-one>
    </class>
</hibernate-mapping>
