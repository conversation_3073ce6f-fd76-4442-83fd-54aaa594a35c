package br.com.ksisolucoes.vo.atividadegrupo.base;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;

import java.io.Serializable;


/**
 * This is an object that contains data related to the atividade_grupo_paciente table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="atividade_grupo_paciente"
 */

public abstract class BaseAtividadeGrupoPaciente extends BaseRootVO implements Serializable {

	public static String REF = "AtividadeGrupoPaciente";
	public static final String PROP_PROFISSIONAL_EVOLUCAO = "profissionalEvolucao";
	public static final String PROP_CESSOU_HABITO_FUMAR = "cessouHabitoFumar";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_ATIVIDADE_GRUPO = "atividadeGrupo";
	public static final String PROP_SITUACAO = "situacao";
	public static final String PROP_GESTANTE = "gestante";
	public static final String PROP_EVOLUCAO = "evolucao";
	public static final String PROP_DUM = "dum";
	public static final String PROP_PRESSAO_ARTERIAL_DIASTOLICA = "pressaoArterialDiastolica";
	public static final String PROP_ABANDONOU_GRUPO = "abandonouGrupo";
	public static final String PROP_PRESSAO_ARTERIAL_SISTOLICA = "pressaoArterialSistolica";
	public static final String PROP_DATA_NASCIMENTO = "dataNascimento";
	public static final String PROP_PESO = "peso";
	public static final String PROP_ALTURA = "altura";
	public static final String PROP_INDICE_IMC = "indiceImc";
	public static final String PROP_ENDERECO_USUARIO_CADSUS = "enderecoUsuarioCadsus";
	public static final String PROP_USUARIO_CADSUS = "usuarioCadsus";
	public static final String PROP_SEXO = "sexo";
	public static final String PROP_AVALIACAO_ALTERADA = "avaliacaoAlterada";
	public static final String PROP_CNS = "cns";
	public static final String PROP_CPF = "cpf";


	// constructors
	public BaseAtividadeGrupoPaciente () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseAtividadeGrupoPaciente (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseAtividadeGrupoPaciente (
		java.lang.Long codigo,
		java.lang.Long situacao) {

		this.setCodigo(codigo);
		this.setSituacao(situacao);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Long situacao;
	private java.lang.Long cns;
	private java.util.Date dataNascimento;
	private java.lang.Long avaliacaoAlterada;
	private java.lang.Long pressaoArterialDiastolica;
	private java.lang.Long pressaoArterialSistolica;
	private java.lang.Double peso;
	private java.lang.Double altura;
	private java.lang.Long cessouHabitoFumar;
	private java.lang.Long abandonouGrupo;
	private java.lang.String evolucao;
	private java.lang.String sexo;
	private java.lang.Long gestante;
	private java.util.Date dum;
	private java.lang.String cpf;

	// many to one
	private br.com.ksisolucoes.vo.atividadegrupo.AtividadeGrupo atividadeGrupo;
	private br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus;
	private br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus enderecoUsuarioCadsus;
	private br.com.ksisolucoes.vo.cadsus.Profissional profissionalEvolucao;
	private br.com.ksisolucoes.vo.basico.IndiceImc indiceImc;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_atv_grupo_paciente"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: situacao
	 */
	public java.lang.Long getSituacao () {
		return getPropertyValue(this, situacao, PROP_SITUACAO); 
	}

	/**
	 * Set the value related to the column: situacao
	 * @param situacao the situacao value
	 */
	public void setSituacao (java.lang.Long situacao) {
//        java.lang.Long situacaoOld = this.situacao;
		this.situacao = situacao;
//        this.getPropertyChangeSupport().firePropertyChange ("situacao", situacaoOld, situacao);
	}



	/**
	 * Return the value associated with the column: cns
	 */
	public java.lang.Long getCns () {
		return getPropertyValue(this, cns, PROP_CNS); 
	}

	/**
	 * Set the value related to the column: cns
	 * @param cns the cns value
	 */
	public void setCns (java.lang.Long cns) {
//        java.lang.Long cnsOld = this.cns;
		this.cns = cns;
//        this.getPropertyChangeSupport().firePropertyChange ("cns", cnsOld, cns);
	}



	/**
	 * Return the value associated with the column: dt_nascimento
	 */
	public java.util.Date getDataNascimento () {
		return getPropertyValue(this, dataNascimento, PROP_DATA_NASCIMENTO); 
	}

	/**
	 * Set the value related to the column: dt_nascimento
	 * @param dataNascimento the dt_nascimento value
	 */
	public void setDataNascimento (java.util.Date dataNascimento) {
//        java.util.Date dataNascimentoOld = this.dataNascimento;
		this.dataNascimento = dataNascimento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataNascimento", dataNascimentoOld, dataNascimento);
	}



	/**
	 * Return the value associated with the column: avaliacao_alterada
	 */
	public java.lang.Long getAvaliacaoAlterada () {
		return getPropertyValue(this, avaliacaoAlterada, PROP_AVALIACAO_ALTERADA); 
	}

	/**
	 * Set the value related to the column: avaliacao_alterada
	 * @param avaliacaoAlterada the avaliacao_alterada value
	 */
	public void setAvaliacaoAlterada (java.lang.Long avaliacaoAlterada) {
//        java.lang.Long avaliacaoAlteradaOld = this.avaliacaoAlterada;
		this.avaliacaoAlterada = avaliacaoAlterada;
//        this.getPropertyChangeSupport().firePropertyChange ("avaliacaoAlterada", avaliacaoAlteradaOld, avaliacaoAlterada);
	}



	/**
	 * Return the value associated with the column: pad
	 */
	public java.lang.Long getPressaoArterialDiastolica () {
		return getPropertyValue(this, pressaoArterialDiastolica, PROP_PRESSAO_ARTERIAL_DIASTOLICA); 
	}

	/**
	 * Set the value related to the column: pad
	 * @param pressaoArterialDiastolica the pad value
	 */
	public void setPressaoArterialDiastolica (java.lang.Long pressaoArterialDiastolica) {
//        java.lang.Long pressaoArterialDiastolicaOld = this.pressaoArterialDiastolica;
		this.pressaoArterialDiastolica = pressaoArterialDiastolica;
//        this.getPropertyChangeSupport().firePropertyChange ("pressaoArterialDiastolica", pressaoArterialDiastolicaOld, pressaoArterialDiastolica);
	}



	/**
	 * Return the value associated with the column: pas
	 */
	public java.lang.Long getPressaoArterialSistolica () {
		return getPropertyValue(this, pressaoArterialSistolica, PROP_PRESSAO_ARTERIAL_SISTOLICA); 
	}

	/**
	 * Set the value related to the column: pas
	 * @param pressaoArterialSistolica the pas value
	 */
	public void setPressaoArterialSistolica (java.lang.Long pressaoArterialSistolica) {
//        java.lang.Long pressaoArterialSistolicaOld = this.pressaoArterialSistolica;
		this.pressaoArterialSistolica = pressaoArterialSistolica;
//        this.getPropertyChangeSupport().firePropertyChange ("pressaoArterialSistolica", pressaoArterialSistolicaOld, pressaoArterialSistolica);
	}



	/**
	 * Return the value associated with the column: peso
	 */
	public java.lang.Double getPeso () {
		return getPropertyValue(this, peso, PROP_PESO); 
	}

	/**
	 * Set the value related to the column: peso
	 * @param peso the peso value
	 */
	public void setPeso (java.lang.Double peso) {
//        java.lang.Double pesoOld = this.peso;
		this.peso = peso;
//        this.getPropertyChangeSupport().firePropertyChange ("peso", pesoOld, peso);
	}



	/**
	 * Return the value associated with the column: altura
	 */
	public java.lang.Double getAltura () {
		return getPropertyValue(this, altura, PROP_ALTURA); 
	}

	/**
	 * Set the value related to the column: altura
	 * @param altura the altura value
	 */
	public void setAltura (java.lang.Double altura) {
		this.altura = altura;
	}



	/**
	 * Return the value associated with the column: cessouhabitofumar
	 */
	public java.lang.Long getCessouHabitoFumar () {
		return getPropertyValue(this, cessouHabitoFumar, PROP_CESSOU_HABITO_FUMAR); 
	}

	/**
	 * Set the value related to the column: cessouhabitofumar
	 * @param cessouHabitoFumar the cessouhabitofumar value
	 */
	public void setCessouHabitoFumar (java.lang.Long cessouHabitoFumar) {
//        java.lang.Long cessouHabitoFumarOld = this.cessouHabitoFumar;
		this.cessouHabitoFumar = cessouHabitoFumar;
//        this.getPropertyChangeSupport().firePropertyChange ("cessouHabitoFumar", cessouHabitoFumarOld, cessouHabitoFumar);
	}



	/**
	 * Return the value associated with the column: abandonougrupo
	 */
	public java.lang.Long getAbandonouGrupo () {
		return getPropertyValue(this, abandonouGrupo, PROP_ABANDONOU_GRUPO); 
	}

	/**
	 * Set the value related to the column: abandonougrupo
	 * @param abandonouGrupo the abandonougrupo value
	 */
	public void setAbandonouGrupo (java.lang.Long abandonouGrupo) {
//        java.lang.Long abandonouGrupoOld = this.abandonouGrupo;
		this.abandonouGrupo = abandonouGrupo;
//        this.getPropertyChangeSupport().firePropertyChange ("abandonouGrupo", abandonouGrupoOld, abandonouGrupo);
	}



	/**
	 * Return the value associated with the column: evolucao
	 */
	public java.lang.String getEvolucao () {
		return getPropertyValue(this, evolucao, PROP_EVOLUCAO); 
	}

	/**
	 * Set the value related to the column: evolucao
	 * @param evolucao the evolucao value
	 */
	public void setEvolucao (java.lang.String evolucao) {
//        java.lang.String evolucaoOld = this.evolucao;
		this.evolucao = evolucao;
//        this.getPropertyChangeSupport().firePropertyChange ("evolucao", evolucaoOld, evolucao);
	}



	/**
	 * Return the value associated with the column: sexo
	 */
	public java.lang.String getSexo () {
		return getPropertyValue(this, sexo, PROP_SEXO); 
	}

	/**
	 * Set the value related to the column: sexo
	 * @param sexo the sexo value
	 */
	public void setSexo (java.lang.String sexo) {
//        java.lang.String sexoOld = this.sexo;
		this.sexo = sexo;
//        this.getPropertyChangeSupport().firePropertyChange ("sexo", sexoOld, sexo);
	}



	/**
	 * Return the value associated with the column: gestante
	 */
	public java.lang.Long getGestante () {
		return getPropertyValue(this, gestante, PROP_GESTANTE); 
	}

	/**
	 * Set the value related to the column: gestante
	 * @param gestante the gestante value
	 */
	public void setGestante (java.lang.Long gestante) {
//        java.lang.Long gestanteOld = this.gestante;
		this.gestante = gestante;
//        this.getPropertyChangeSupport().firePropertyChange ("gestante", gestanteOld, gestante);
	}



	/**
	 * Return the value associated with the column: dum
	 */
	public java.util.Date getDum () {
		return getPropertyValue(this, dum, PROP_DUM); 
	}

	/**
	 * Set the value related to the column: dum
	 * @param dum the dum value
	 */
	public void setDum (java.util.Date dum) {
//        java.util.Date dumOld = this.dum;
		this.dum = dum;
//        this.getPropertyChangeSupport().firePropertyChange ("dum", dumOld, dum);
	}



	/**
	 * Return the value associated with the column: cd_atv_grupo
	 */
	public br.com.ksisolucoes.vo.atividadegrupo.AtividadeGrupo getAtividadeGrupo () {
		return getPropertyValue(this, atividadeGrupo, PROP_ATIVIDADE_GRUPO); 
	}

	/**
	 * Set the value related to the column: cd_atv_grupo
	 * @param atividadeGrupo the cd_atv_grupo value
	 */
	public void setAtividadeGrupo (br.com.ksisolucoes.vo.atividadegrupo.AtividadeGrupo atividadeGrupo) {
//        br.com.ksisolucoes.vo.atividadegrupo.AtividadeGrupo atividadeGrupoOld = this.atividadeGrupo;
		this.atividadeGrupo = atividadeGrupo;
//        this.getPropertyChangeSupport().firePropertyChange ("atividadeGrupo", atividadeGrupoOld, atividadeGrupo);
	}



	/**
	 * Return the value associated with the column: cd_usu_cadsus
	 */
	public br.com.ksisolucoes.vo.cadsus.UsuarioCadsus getUsuarioCadsus () {
		return getPropertyValue(this, usuarioCadsus, PROP_USUARIO_CADSUS); 
	}

	/**
	 * Set the value related to the column: cd_usu_cadsus
	 * @param usuarioCadsus the cd_usu_cadsus value
	 */
	public void setUsuarioCadsus (br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus) {
//        br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsusOld = this.usuarioCadsus;
		this.usuarioCadsus = usuarioCadsus;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCadsus", usuarioCadsusOld, usuarioCadsus);
	}



	/**
	 * Return the value associated with the column: cd_endereco
	 */
	public br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus getEnderecoUsuarioCadsus () {
		return getPropertyValue(this, enderecoUsuarioCadsus, PROP_ENDERECO_USUARIO_CADSUS); 
	}

	/**
	 * Set the value related to the column: cd_endereco
	 * @param enderecoUsuarioCadsus the cd_endereco value
	 */
	public void setEnderecoUsuarioCadsus (br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus enderecoUsuarioCadsus) {
//        br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus enderecoUsuarioCadsusOld = this.enderecoUsuarioCadsus;
		this.enderecoUsuarioCadsus = enderecoUsuarioCadsus;
//        this.getPropertyChangeSupport().firePropertyChange ("enderecoUsuarioCadsus", enderecoUsuarioCadsusOld, enderecoUsuarioCadsus);
	}



	/**
	 * Return the value associated with the column: cd_profissional_evolucao
	 */
	public br.com.ksisolucoes.vo.cadsus.Profissional getProfissionalEvolucao () {
		return getPropertyValue(this, profissionalEvolucao, PROP_PROFISSIONAL_EVOLUCAO); 
	}

	/**
	 * Set the value related to the column: cd_profissional_evolucao
	 * @param profissionalEvolucao the cd_profissional_evolucao value
	 */
	public void setProfissionalEvolucao (br.com.ksisolucoes.vo.cadsus.Profissional profissionalEvolucao) {
//        br.com.ksisolucoes.vo.cadsus.Profissional profissionalEvolucaoOld = this.profissionalEvolucao;
		this.profissionalEvolucao = profissionalEvolucao;
//        this.getPropertyChangeSupport().firePropertyChange ("profissionalEvolucao", profissionalEvolucaoOld, profissionalEvolucao);
	}



	/**
	 * Return the value associated with the column: cd_indice_imc
	 */
	public br.com.ksisolucoes.vo.basico.IndiceImc getIndiceImc () {
		return getPropertyValue(this, indiceImc, PROP_INDICE_IMC); 
	}

	/**
	 * Set the value related to the column: cd_indice_imc
	 * @param indiceImc the cd_indice_imc value
	 */
	public void setIndiceImc (br.com.ksisolucoes.vo.basico.IndiceImc indiceImc) {
//        br.com.ksisolucoes.vo.basico.IndiceImc indiceImcOld = this.indiceImc;
		this.indiceImc = indiceImc;
//        this.getPropertyChangeSupport().firePropertyChange ("indiceImc", indiceImcOld, indiceImc);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.atividadegrupo.AtividadeGrupoPaciente)) return false;
		else {
			br.com.ksisolucoes.vo.atividadegrupo.AtividadeGrupoPaciente atividadeGrupoPaciente = (br.com.ksisolucoes.vo.atividadegrupo.AtividadeGrupoPaciente) obj;
			if (null == this.getCodigo() || null == atividadeGrupoPaciente.getCodigo()) return false;
			else return (this.getCodigo().equals(atividadeGrupoPaciente.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }


	/**
	 * Return the value associated with the column: cpf
	 */
	public java.lang.String getCpf () {
		return getPropertyValue(this, cpf, PROP_CPF);
	}

	/**
	 * Set the value related to the column: cpf
	 * @param cpf the cpf value
	 */
	public void setCpf (java.lang.String cpf) {
		this.cpf = cpf;
	}
}