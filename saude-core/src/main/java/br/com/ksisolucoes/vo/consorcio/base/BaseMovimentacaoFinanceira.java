package br.com.ksisolucoes.vo.consorcio.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the movimentacao_financeira table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="movimentacao_financeira"
 */

public abstract class BaseMovimentacaoFinanceira extends BaseRootVO implements Serializable {

	public static String REF = "MovimentacaoFinanceira";
	public static final String PROP_USUARIO = "usuario";
	public static final String PROP_DATA_MOVIMENTACAO = "dataMovimentacao";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_VALIDADOR = "validador";
	public static final String PROP_SALDO_ANTERIOR_ANO = "saldoAnteriorAno";
	public static final String PROP_TIPO_MOVIMENTACAO = "tipoMovimentacao";
	public static final String PROP_SUB_CONTA = "subConta";
	public static final String PROP_EMPRESA_MOVIMENTACAO = "empresaMovimentacao";
	public static final String PROP_VALOR = "valor";
	public static final String PROP_FECHAMENTO_ANUAL = "fechamentoAnual";
	public static final String PROP_SUB_CONTA_ANO = "subContaAno";
	public static final String PROP_DESCRICAO_MOVIMENTACAO = "descricaoMovimentacao";
	public static final String PROP_SALDO_ANTERIOR = "saldoAnterior";


	// constructors
	public BaseMovimentacaoFinanceira () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseMovimentacaoFinanceira (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseMovimentacaoFinanceira (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.consorcio.TipoMovimentacao tipoMovimentacao,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		br.com.ksisolucoes.vo.basico.Empresa empresaMovimentacao,
		java.lang.Double valor,
		java.lang.Double saldoAnterior,
		java.util.Date dataMovimentacao,
		java.lang.String validador,
		java.lang.Double saldoAnteriorAno) {

		this.setCodigo(codigo);
		this.setTipoMovimentacao(tipoMovimentacao);
		this.setUsuario(usuario);
		this.setEmpresaMovimentacao(empresaMovimentacao);
		this.setValor(valor);
		this.setSaldoAnterior(saldoAnterior);
		this.setDataMovimentacao(dataMovimentacao);
		this.setValidador(validador);
		this.setSaldoAnteriorAno(saldoAnteriorAno);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String descricaoMovimentacao;
	private java.lang.Double valor;
	private java.lang.Double saldoAnterior;
	private java.util.Date dataMovimentacao;
	private java.lang.String validador;
	private java.lang.Double saldoAnteriorAno;

	// many to one
	private br.com.ksisolucoes.vo.consorcio.TipoMovimentacao tipoMovimentacao;
	private br.com.ksisolucoes.vo.controle.Usuario usuario;
	private br.com.ksisolucoes.vo.basico.Empresa empresaMovimentacao;
	private br.com.ksisolucoes.vo.consorcio.SubConta subConta;
	private br.com.ksisolucoes.vo.consorcio.FechamentoAnual fechamentoAnual;
	private br.com.ksisolucoes.vo.consorcio.SubContaAno subContaAno;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_movimentacao"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: ds_movimentacao
	 */
	public java.lang.String getDescricaoMovimentacao () {
		return getPropertyValue(this, descricaoMovimentacao, PROP_DESCRICAO_MOVIMENTACAO); 
	}

	/**
	 * Set the value related to the column: ds_movimentacao
	 * @param descricaoMovimentacao the ds_movimentacao value
	 */
	public void setDescricaoMovimentacao (java.lang.String descricaoMovimentacao) {
//        java.lang.String descricaoMovimentacaoOld = this.descricaoMovimentacao;
		this.descricaoMovimentacao = descricaoMovimentacao;
//        this.getPropertyChangeSupport().firePropertyChange ("descricaoMovimentacao", descricaoMovimentacaoOld, descricaoMovimentacao);
	}



	/**
	 * Return the value associated with the column: valor
	 */
	public java.lang.Double getValor () {
		return getPropertyValue(this, valor, PROP_VALOR); 
	}

	/**
	 * Set the value related to the column: valor
	 * @param valor the valor value
	 */
	public void setValor (java.lang.Double valor) {
//        java.lang.Double valorOld = this.valor;
		this.valor = valor;
//        this.getPropertyChangeSupport().firePropertyChange ("valor", valorOld, valor);
	}



	/**
	 * Return the value associated with the column: saldo_anterior
	 */
	public java.lang.Double getSaldoAnterior () {
		return getPropertyValue(this, saldoAnterior, PROP_SALDO_ANTERIOR); 
	}

	/**
	 * Set the value related to the column: saldo_anterior
	 * @param saldoAnterior the saldo_anterior value
	 */
	public void setSaldoAnterior (java.lang.Double saldoAnterior) {
//        java.lang.Double saldoAnteriorOld = this.saldoAnterior;
		this.saldoAnterior = saldoAnterior;
//        this.getPropertyChangeSupport().firePropertyChange ("saldoAnterior", saldoAnteriorOld, saldoAnterior);
	}



	/**
	 * Return the value associated with the column: dt_movimentacao
	 */
	public java.util.Date getDataMovimentacao () {
		return getPropertyValue(this, dataMovimentacao, PROP_DATA_MOVIMENTACAO); 
	}

	/**
	 * Set the value related to the column: dt_movimentacao
	 * @param dataMovimentacao the dt_movimentacao value
	 */
	public void setDataMovimentacao (java.util.Date dataMovimentacao) {
//        java.util.Date dataMovimentacaoOld = this.dataMovimentacao;
		this.dataMovimentacao = dataMovimentacao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataMovimentacao", dataMovimentacaoOld, dataMovimentacao);
	}



	/**
	 * Return the value associated with the column: validador
	 */
	public java.lang.String getValidador () {
		return getPropertyValue(this, validador, PROP_VALIDADOR); 
	}

	/**
	 * Set the value related to the column: validador
	 * @param validador the validador value
	 */
	public void setValidador (java.lang.String validador) {
//        java.lang.String validadorOld = this.validador;
		this.validador = validador;
//        this.getPropertyChangeSupport().firePropertyChange ("validador", validadorOld, validador);
	}



	/**
	 * Return the value associated with the column: saldo_anterior_ano
	 */
	public java.lang.Double getSaldoAnteriorAno () {
		return getPropertyValue(this, saldoAnteriorAno, PROP_SALDO_ANTERIOR_ANO);
	}

	/**
	 * Set the value related to the column: saldo_anterior_ano
	 * @param saldoAnteriorAno the saldo_anterior_ano value
	 */
	public void setSaldoAnteriorAno (java.lang.Double saldoAnteriorAno) {
//        java.lang.Double saldoAnteriorAnoOld = this.saldoAnteriorAno;
		this.saldoAnteriorAno = saldoAnteriorAno;
//        this.getPropertyChangeSupport().firePropertyChange ("saldoAnteriorAno", saldoAnteriorAnoOld, saldoAnteriorAno);
	}



	/**
	 * Return the value associated with the column: cd_tp_movimentacao
	 */
	public br.com.ksisolucoes.vo.consorcio.TipoMovimentacao getTipoMovimentacao () {
		return getPropertyValue(this, tipoMovimentacao, PROP_TIPO_MOVIMENTACAO); 
	}

	/**
	 * Set the value related to the column: cd_tp_movimentacao
	 * @param tipoMovimentacao the cd_tp_movimentacao value
	 */
	public void setTipoMovimentacao (br.com.ksisolucoes.vo.consorcio.TipoMovimentacao tipoMovimentacao) {
//        br.com.ksisolucoes.vo.consorcio.TipoMovimentacao tipoMovimentacaoOld = this.tipoMovimentacao;
		this.tipoMovimentacao = tipoMovimentacao;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoMovimentacao", tipoMovimentacaoOld, tipoMovimentacao);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuario the cd_usuario value
	 */
	public void setUsuario (br.com.ksisolucoes.vo.controle.Usuario usuario) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}



	/**
	 * Return the value associated with the column: empresa_movimentacao
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getEmpresaMovimentacao () {
		return getPropertyValue(this, empresaMovimentacao, PROP_EMPRESA_MOVIMENTACAO); 
	}

	/**
	 * Set the value related to the column: empresa_movimentacao
	 * @param empresaMovimentacao the empresa_movimentacao value
	 */
	public void setEmpresaMovimentacao (br.com.ksisolucoes.vo.basico.Empresa empresaMovimentacao) {
//        br.com.ksisolucoes.vo.basico.Empresa empresaMovimentacaoOld = this.empresaMovimentacao;
		this.empresaMovimentacao = empresaMovimentacao;
//        this.getPropertyChangeSupport().firePropertyChange ("empresaMovimentacao", empresaMovimentacaoOld, empresaMovimentacao);
	}



	/**
	 * Return the value associated with the column: cd_subconta
	 */
	public br.com.ksisolucoes.vo.consorcio.SubConta getSubConta () {
		return getPropertyValue(this, subConta, PROP_SUB_CONTA); 
	}

	/**
	 * Set the value related to the column: cd_subconta
	 * @param subConta the cd_subconta value
	 */
	public void setSubConta (br.com.ksisolucoes.vo.consorcio.SubConta subConta) {
//        br.com.ksisolucoes.vo.consorcio.SubConta subContaOld = this.subConta;
		this.subConta = subConta;
//        this.getPropertyChangeSupport().firePropertyChange ("subConta", subContaOld, subConta);
	}



	/**
	 * Return the value associated with the column: cd_fechamento_anual
	 */
	public br.com.ksisolucoes.vo.consorcio.FechamentoAnual getFechamentoAnual () {
		return getPropertyValue(this, fechamentoAnual, PROP_FECHAMENTO_ANUAL);
	}

	/**
	 * Set the value related to the column: cd_fechamento_anual
	 * @param fechamentoAnual the cd_fechamento_anual value
	 */
	public void setFechamentoAnual (br.com.ksisolucoes.vo.consorcio.FechamentoAnual fechamentoAnual) {
//        br.com.ksisolucoes.vo.consorcio.FechamentoAnual fechamentoAnualOld = this.fechamentoAnual;
		this.fechamentoAnual = fechamentoAnual;
//        this.getPropertyChangeSupport().firePropertyChange ("fechamentoAnual", fechamentoAnualOld, fechamentoAnual);
	}



	/**
	 * Return the value associated with the column: cd_subconta_ano
	 */
	public br.com.ksisolucoes.vo.consorcio.SubContaAno getSubContaAno () {
		return getPropertyValue(this, subContaAno, PROP_SUB_CONTA_ANO);
	}

	/**
	 * Set the value related to the column: cd_subconta_ano
	 * @param subContaAno the cd_subconta_ano value
	 */
	public void setSubContaAno (br.com.ksisolucoes.vo.consorcio.SubContaAno subContaAno) {
//        br.com.ksisolucoes.vo.consorcio.SubContaAno subContaAnoOld = this.subContaAno;
		this.subContaAno = subContaAno;
//        this.getPropertyChangeSupport().firePropertyChange ("subContaAno", subContaAnoOld, subContaAno);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.consorcio.MovimentacaoFinanceira)) return false;
		else {
			br.com.ksisolucoes.vo.consorcio.MovimentacaoFinanceira movimentacaoFinanceira = (br.com.ksisolucoes.vo.consorcio.MovimentacaoFinanceira) obj;
			if (null == this.getCodigo() || null == movimentacaoFinanceira.getCodigo()) return false;
			else return (this.getCodigo().equals(movimentacaoFinanceira.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}