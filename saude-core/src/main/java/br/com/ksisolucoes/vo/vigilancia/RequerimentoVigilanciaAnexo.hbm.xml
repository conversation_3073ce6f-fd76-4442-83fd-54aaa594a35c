<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.vigilancia"  >
    <class name="RequerimentoVigilanciaAnexo" table="requerimento_vigilancia_anexo">
        <id
            column="cd_req_vigilancia_anexo"
            name="codigo"
            type="java.lang.Long"
        >
            <generator class="assigned" />
        </id> 
        <version column="version" name="version" type="long" />

        <many-to-one
            class="br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia"
            column="cd_req_vigilancia"
            not-null="false"
            name="requerimentoVigilancia"
        />

        <many-to-one
            class="br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo"
            column="cd_gerenciador_arquivo"
            not-null="true"
            name="gerenciadorArquivo"
        />
                      
        <property
            column="descricao"
            name="descricao"
            type="java.lang.String"
            length="50"
        />

        <property
            column="status"
            name="status"
            not-null="true"
            type="java.lang.Long"
        />

        <property
            name="dataIntegracao"
            column="dt_integracao"
            type="timestamp"
            not-null="false"
        />
        
        <property
            column="observacao"
            name="observacao"
            type="java.lang.String"
            length="500"
        />

        <property
            column="visualizado"
            name="visualizado"
            type="java.lang.Long"
            not-null="false"
        />

        <property
            column="tp_assinatura_anexo"
            name="tipoAssinaturaAnexo"
            type="java.lang.Long"
            not-null="false"
        />

        <many-to-one
            class="br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoLicencaTransporteVeiculo"
            column="cd_requerimento_licenca_transporte_veiculo"
            not-null="false"
            name="requerimentoLicencaTransporteVeiculo"
        />

        <many-to-one
            class="br.com.ksisolucoes.vo.vigilancia.autointimacao.AutoIntimacao"
            column="cd_auto_intimacao"
            not-null="false"
            name="autoIntimacao"
        />

        <many-to-one
            class="br.com.ksisolucoes.vo.vigilancia.autointimacao.AutoIntimacaoExigencia"
            column="cd_auto_intimacao_exigencia"
            not-null="false"
            name="autoIntimacaoExigencia"
        />

        <many-to-one
            class="br.com.ksisolucoes.vo.vigilancia.RelatorioInspecao"
            column="cd_relatorio_inspecao"
            not-null="false"
            name="relatorioInspecao"
        />

        <many-to-one
            class="br.com.ksisolucoes.vo.vigilancia.autoinfracao.AutoInfracao"
            column="cd_auto_infracao"
            not-null="false"
            name="autoInfracao"
        />

        <many-to-one
            class="br.com.ksisolucoes.vo.vigilancia.processoadministrativo.ProcessoAdministrativo"
            column="cd_processo_adm"
            not-null="false"
            name="processoAdministrativo"
        />

        <many-to-one
            class="br.com.ksisolucoes.vo.vigilancia.processoadministrativo.ProcessoAdministrativoParecer"
            column="cd_processo_adm_parecer"
            not-null="false"
            name="processoAdministrativoParecer"
        />

        <many-to-one
            class="br.com.ksisolucoes.vo.vigilancia.processoadministrativo.ProcessoAdministrativoDecisao"
            column="cd_processo_adm_decisao"
            not-null="false"
            name="processoAdministrativoDecisao"
        />
        
         <many-to-one
            class="br.com.ksisolucoes.vo.vigilancia.processoadministrativo.ProcessoAdministrativoRecurso"
            column="cd_processo_adm_recurso"
            not-null="false"
            name="processoAdministrativoRecurso"
        />

        <many-to-one
            class="br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilanciaParecer"
            column="cd_req_vig_parecer"
            not-null="false"
            name="requerimentoVigilanciaParecer"
        />

        <many-to-one
            class="br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoAnaliseProjetoParecer"
            column="cd_req_ana_pro_parecer"
            not-null="false"
            name="requerimentoAnaliseProjetoParecer"
        />

        <many-to-one
            class="br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoVistoriaPBAConformidadeTecnica"
            column="cd_req_ana_pba_conf_tec"
            not-null="false"
            name="requerimentoVistoriaPBAConformidadeTecnica"
        />

        <many-to-one
            class="br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoProjetoHidrossanitarioParecer"
            column="cd_req_pro_hidros_parecer"
            not-null="false"
            name="requerimentoProjetoHidrossanitarioParecer"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoProjetoHidrossanitarioDeclaratorioParecer"
                column="cd_req_projeto_hidro_declaratorio_parecer"
                not-null="false"
                name="requerimentoProjetoHidrossanitarioDeclaratorioParecer"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoVistoriaHidrossanitarioDeclaratorioParecer"
                column="cd_req_vistoria_hidro_declaratorio_parecer"
                not-null="false"
                name="requerimentoVistoriaHidrossanitarioDeclaratorioParecer"
        />

        <many-to-one
            class="br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoVistoriaHidrossanitarioParecer"
            column="cd_req_vistoria_hidro_parecer"
            not-null="false"
            name="requerimentoVistoriaHidrossanitarioParecer"
        />


        <many-to-one
                class="br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoAnaliseProjetoParecerResposta"
                column="cd_req_ana_pro_parecer_resp"
                not-null="false"
                name="requerimentoAnaliseProjetoParecerResposta"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoVistoriaPBAConformidadeTecnicaResposta"
                column="cd_req_ana_pba_conf_tec_resp"
                not-null="false"
                name="requerimentoVistoriaPBAConformidadeTecnicaResposta"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoProjetoHidrossanitarioParecerResposta"
                column="cd_req_proj_hidro_parecer_resp"
                not-null="false"
                name="requerimentoProjetoHidrossanitarioParecerResposta"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoProjetoHidrossanitarioDeclaratorioParecerResposta"
                column="cd_req_proj_hidro_declaratorio_parecer_resp"
                not-null="false"
                name="requerimentoProjetoHidrossanitarioDeclaratorioParecerResposta"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoVistoriaHidrossanitarioDeclaratorioParecerResposta"
                column="cd_req_vistoria_hidro_declaratorio_parecer_resp"
                not-null="false"
                name="requerimentoVistoriaHidrossanitarioDeclaratorioParecerResposta"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoVistoriaHidrossanitarioParecerResposta"
                column="cd_req_vistoria_hidro_parecer_resp"
                not-null="false"
                name="requerimentoVistoriaHidrossanitarioParecerResposta"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilanciaParecerResposta"
                column="cd_req_vig_parecer_resposta"
                not-null="false"
                name="requerimentoVigilanciaParecerResposta"
        />
        <many-to-one
            class="br.com.ksisolucoes.vo.vigilancia.automulta.AutoMulta"
            column="cd_auto_multa"
            not-null="false"
            name="autoMulta"
        />
        <property
                name="dataCadastro"
                column="dt_cadastro"
                type="timestamp"
                not-null="false"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoProjetoArquitetonicoParecer"
                column="cd_req_projeto_arquitetonico_parecer"
                not-null="false"
                name="requerimentoProjetoArquitetonicoParecer"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoProjetoArquitetonicoParecerResposta"
                column="cd_req_projeto_arquitetonico_parecer_resp"
                not-null="false"
                name="requerimentoProjetoArquitetonicoParecerResposta"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.vigilancia.termoajustamentoconduta.TermoAjustamentoConduta"
                column="cd_termo_ajustamento_conduta"
                not-null="false"
                name="termoAjustamentoConduta"
        />

    </class>
</hibernate-mapping>
