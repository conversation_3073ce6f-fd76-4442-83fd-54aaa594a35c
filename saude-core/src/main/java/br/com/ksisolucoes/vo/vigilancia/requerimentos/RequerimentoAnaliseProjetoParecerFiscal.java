package br.com.ksisolucoes.vo.vigilancia.requerimentos;

import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.base.BaseRequerimentoAnaliseProjetoParecerFiscal;

import java.io.Serializable;



public class RequerimentoAnaliseProjetoParecerFiscal extends BaseRequerimentoAnaliseProjetoParecerFiscal implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public RequerimentoAnaliseProjetoParecerFiscal () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public RequerimentoAnaliseProjetoParecerFiscal (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public RequerimentoAnaliseProjetoParecerFiscal (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoAnaliseProjetoParecer requerimentoAnaliseProjetoParecer,
		br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilanciaFiscal requerimentoVigilanciaFiscal) {

		super (
			codigo,
			requerimentoAnaliseProjetoParecer,
			requerimentoVigilanciaFiscal);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}