package br.com.ksisolucoes.vo.vigilancia.base;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;

import java.io.Serializable;


/**
 * This is an object that contains data related to the requerimento_vig_parecer_resposta table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="requerimento_vig_parecer_resposta"
 */

public abstract class BaseRequerimentoVigilanciaParecerResposta extends BaseRootVO implements Serializable {

	public static String REF = "RequerimentoVigilanciaParecerResposta";
	public static final String PROP_DATA_USUARIO = "dataUsuario";
	public static final String PROP_DESCRICAO_RESPOSTA = "descricaoResposta";
	public static final String PROP_USUARIO = "usuario";
	public static final String PROP_DATA_RESPOSTA = "dataResposta";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_SITUACAO = "situacao";
	public static final String PROP_REQUERIMENTO_VIGILANCIA_PARECER = "requerimentoVigilanciaParecer";


	// constructors
	public BaseRequerimentoVigilanciaParecerResposta () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseRequerimentoVigilanciaParecerResposta (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseRequerimentoVigilanciaParecerResposta (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilanciaParecer requerimentoVigilanciaParecer,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		java.util.Date dataResposta,
		java.util.Date dataUsuario) {

		this.setCodigo(codigo);
		this.setRequerimentoVigilanciaParecer(requerimentoVigilanciaParecer);
		this.setUsuario(usuario);
		this.setDataResposta(dataResposta);
		this.setDataUsuario(dataUsuario);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String descricaoResposta;
	private java.util.Date dataResposta;
	private java.lang.Long situacao;
	private java.util.Date dataUsuario;

	// many to one
	private br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilanciaParecer requerimentoVigilanciaParecer;
	private br.com.ksisolucoes.vo.controle.Usuario usuario;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_req_vig_parecer_resposta"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: ds_resposta
	 */
	public java.lang.String getDescricaoResposta () {
		return getPropertyValue(this, descricaoResposta, PROP_DESCRICAO_RESPOSTA); 
	}

	/**
	 * Set the value related to the column: ds_resposta
	 * @param descricaoResposta the ds_resposta value
	 */
	public void setDescricaoResposta (java.lang.String descricaoResposta) {
//        java.lang.String descricaoRespostaOld = this.descricaoResposta;
		this.descricaoResposta = descricaoResposta;
//        this.getPropertyChangeSupport().firePropertyChange ("descricaoResposta", descricaoRespostaOld, descricaoResposta);
	}



	/**
	 * Return the value associated with the column: dt_resposta
	 */
	public java.util.Date getDataResposta () {
		return getPropertyValue(this, dataResposta, PROP_DATA_RESPOSTA); 
	}

	/**
	 * Set the value related to the column: dt_resposta
	 * @param dataResposta the dt_resposta value
	 */
	public void setDataResposta (java.util.Date dataResposta) {
//        java.util.Date dataRespostaOld = this.dataResposta;
		this.dataResposta = dataResposta;
//        this.getPropertyChangeSupport().firePropertyChange ("dataResposta", dataRespostaOld, dataResposta);
	}



	/**
	 * Return the value associated with the column: situacao
	 */
	public java.lang.Long getSituacao () {
		return getPropertyValue(this, situacao, PROP_SITUACAO); 
	}

	/**
	 * Set the value related to the column: situacao
	 * @param situacao the situacao value
	 */
	public void setSituacao (java.lang.Long situacao) {
//        java.lang.Long situacaoOld = this.situacao;
		this.situacao = situacao;
//        this.getPropertyChangeSupport().firePropertyChange ("situacao", situacaoOld, situacao);
	}



	/**
	 * Return the value associated with the column: dt_usuario
	 */
	public java.util.Date getDataUsuario () {
		return getPropertyValue(this, dataUsuario, PROP_DATA_USUARIO); 
	}

	/**
	 * Set the value related to the column: dt_usuario
	 * @param dataUsuario the dt_usuario value
	 */
	public void setDataUsuario (java.util.Date dataUsuario) {
//        java.util.Date dataUsuarioOld = this.dataUsuario;
		this.dataUsuario = dataUsuario;
//        this.getPropertyChangeSupport().firePropertyChange ("dataUsuario", dataUsuarioOld, dataUsuario);
	}



	/**
	 * Return the value associated with the column: cd_req_vig_parecer
	 */
	public br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilanciaParecer getRequerimentoVigilanciaParecer () {
		return getPropertyValue(this, requerimentoVigilanciaParecer, PROP_REQUERIMENTO_VIGILANCIA_PARECER); 
	}

	/**
	 * Set the value related to the column: cd_req_vig_parecer
	 * @param requerimentoVigilanciaParecer the cd_req_vig_parecer value
	 */
	public void setRequerimentoVigilanciaParecer (br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilanciaParecer requerimentoVigilanciaParecer) {
//        br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilanciaParecer requerimentoVigilanciaParecerOld = this.requerimentoVigilanciaParecer;
		this.requerimentoVigilanciaParecer = requerimentoVigilanciaParecer;
//        this.getPropertyChangeSupport().firePropertyChange ("requerimentoVigilanciaParecer", requerimentoVigilanciaParecerOld, requerimentoVigilanciaParecer);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuario the cd_usuario value
	 */
	public void setUsuario (br.com.ksisolucoes.vo.controle.Usuario usuario) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilanciaParecerResposta)) return false;
		else {
			br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilanciaParecerResposta requerimentoVigilanciaParecerResposta = (br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilanciaParecerResposta) obj;
			if (null == this.getCodigo() || null == requerimentoVigilanciaParecerResposta.getCodigo()) return false;
			else return (this.getCodigo().equals(requerimentoVigilanciaParecerResposta.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}