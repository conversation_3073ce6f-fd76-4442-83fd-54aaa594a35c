package br.com.ksisolucoes.vo.geral;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;

import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Coalesce;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.geral.base.BaseEstruturaEquipamentoRevisao;
import br.com.ksisolucoes.vo.geral.interfaces.EstruturaEquipamentoInterface;
import br.com.ksisolucoes.vo.geral.interfaces.EstruturaEquipamentoPKInterface;
import br.com.ksisolucoes.vo.geral.interfaces.EstruturaEquipamentoRevisaoInterface;
import br.com.ksisolucoes.vo.geral.interfaces.EstruturaEquipamentoRevisaoPKInterface;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class EstruturaEquipamentoRevisao extends BaseEstruturaEquipamentoRevisao implements CodigoManager, EstruturaEquipamentoRevisaoInterface {
	private static final long serialVersionUID = 1L;

        public static final String PROP_DESCRICAO_MOVIMENTACAO = "descricaoMovimentacao";
        /*
         * CONSTANTES
         * ---------
         *---------------------------------------------------------------------*/
        public static final Long STATUS_INICIADO = 1L;
        public static final Long STATUS_FINALIZADO = 2L;
        public static final Long STATUS_CANCELADO = 3L;
        /*---------------------------------------------------------------------*/
        /*
         * MOVIMENTACAO
         * ---------
         *---------------------------------------------------------------------*/
        public static final String INSERIR = "I";
        public static final String MODIFICADO = "M";
        public static final String CALCULO_QUANTIDADE_EXTRA = "E";
        public static final String APAGADO = "A";
        public static final String SUBSTITUIDO = "S";
        /*---------------------------------------------------------------------*/

/*[CONSTRUCTOR MARKER BEGIN]*/
	public EstruturaEquipamentoRevisao () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public EstruturaEquipamentoRevisao (br.com.ksisolucoes.vo.geral.EstruturaEquipamentoRevisaoPK id) {
		super(id);
	}

	/**
	 * Constructor for required fields
	 */
	public EstruturaEquipamentoRevisao (
		br.com.ksisolucoes.vo.geral.EstruturaEquipamentoRevisaoPK id,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		java.lang.Double quantidade,
		java.util.Date dataLancamento,
		java.util.Date dataUsuario) {

		super (
			id,
			usuario,
			quantidade,
			dataLancamento,
			dataUsuario);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setId( (br.com.ksisolucoes.vo.geral.EstruturaEquipamentoRevisaoPK)key );
    }

    public Serializable getCodigoManager() {
        return this.getId();
    }

    public void setId(EstruturaEquipamentoRevisaoPKInterface id) {
        this.setId( (EstruturaEquipamentoRevisaoPK)id );
    }

    private boolean novo = false;
    private Produto produtoAntigo;
    private Long itemAntigo;

    public boolean isNovo() {
        return novo;
    }

    public void setNovo(boolean novo) {
        this.novo = novo;
    }

    public Produto getProdutoAntigo() {
        return produtoAntigo;
    }

    public void setProdutoAntigo(Produto produtoAntigo) {
        this.produtoAntigo = produtoAntigo;
    }

    public Long getItemAntigo() {
        return itemAntigo;
    }

    public void setItemAntigo(Long itemAntigo) {
        this.itemAntigo = itemAntigo;
    }

    public EstruturaEquipamentoRevisao convertEstruturaEquipamentoToEstruturaEquipamentoRevisao(EstruturaEquipamentoInterface estrutura) {
        EstruturaEquipamentoPKInterface pk = estrutura.getId();
        EstruturaEquipamentoRevisaoPK revisaoPk = new EstruturaEquipamentoRevisaoPK();
        revisaoPk.setComponente(pk.getComponente());
        revisaoPk.setItem(pk.getItem());
        revisaoPk.setProduto(pk.getProduto());
//        revisaoPk.setRevisao(pk.getProduto().getRevisaoEstrutura());

        EstruturaEquipamentoRevisao estruturaEquipamentoRevisao = new EstruturaEquipamentoRevisao(revisaoPk);

        estruturaEquipamentoRevisao.setQuantidade(estrutura.getQuantidade());
        estruturaEquipamentoRevisao.setDataLancamento(estrutura.getDataLancamento());
        estruturaEquipamentoRevisao.setUsuario(estrutura.getUsuario());
        estruturaEquipamentoRevisao.setDataUsuario(estrutura.getDataUsuario());
        estruturaEquipamentoRevisao.setFlagMovimentacao(estrutura.getFlagMovimentacao());

        return estruturaEquipamentoRevisao;
    }

    public Set<EstruturaEquipamentoRevisao> convertEstruturaEquipamentoSetToEstruturaEquipamentoRevisaoSet(Set<EstruturaEquipamento> estruturaSet) {
        Set<EstruturaEquipamentoRevisao> revisaoSet = new LinkedHashSet<EstruturaEquipamentoRevisao>();
        for (EstruturaEquipamento estrutura : estruturaSet) {
            revisaoSet.add(convertEstruturaEquipamentoToEstruturaEquipamentoRevisao(estrutura));
        }
        return revisaoSet;
    }

    public List<EstruturaEquipamentoRevisao> convertEstruturaEquipamentoListToEstruturaEquipamentoRevisaoList(List<EstruturaEquipamento> estruturaList) {
        List<EstruturaEquipamentoRevisao> revisaoList = new ArrayList<EstruturaEquipamentoRevisao>();
        for (EstruturaEquipamento estrutura : estruturaList) {
            revisaoList.add(convertEstruturaEquipamentoToEstruturaEquipamentoRevisao(estrutura));
        }
        return revisaoList;
    }

    public EstruturaEquipamentoRevisao convertEstruturaEquipamentoToEstruturaEquipamentoRevisao(EstruturaEquipamento estrutura) {
        return convertEstruturaEquipamentoToEstruturaEquipamentoRevisao(estrutura);
    }

    /**
     * Converte uma EstruturaEquipamento para uma EstruturaEquipamentoRevisao
     * @param estrutura EstruturaEquipamento a ser convertida
     * @return EstruturaEquipamentoRevisao
     */
    public static EstruturaEquipamentoRevisao convertEstruturaEquipamentoToRevisao(EstruturaEquipamento estrutura){
        EstruturaEquipamentoPK pk = estrutura.getId();
        EstruturaEquipamentoRevisaoPK revisaoPk = new EstruturaEquipamentoRevisaoPK();
        revisaoPk.setComponente(pk.getComponente());
        revisaoPk.setItem(pk.getItem());
        revisaoPk.setProduto(pk.getProduto());
        if(pk.getProduto().isEstruturaEmRevisao()){
        	revisaoPk.setRevisao(Coalesce.asLong(pk.getProduto().getRevisaoEstrutura()) + 1);
        }else{
        	revisaoPk.setRevisao(Coalesce.asLong(pk.getProduto().getRevisaoEstrutura()));
        }

        EstruturaEquipamentoRevisao estruturaEquipamentoRevisao = new EstruturaEquipamentoRevisao(revisaoPk);

        estruturaEquipamentoRevisao.setQuantidade(estrutura.getQuantidade());
        estruturaEquipamentoRevisao.setDataLancamento(estrutura.getDataLancamento());
        estruturaEquipamentoRevisao.setUsuario(estrutura.getUsuario());
        estruturaEquipamentoRevisao.setDataUsuario(estrutura.getDataUsuario());
        estruturaEquipamentoRevisao.setFlagMovimentacao(estrutura.getFlagMovimentacao());

        return estruturaEquipamentoRevisao;
    }

    public String toString() {
        StringBuffer st = new StringBuffer();
        if ( this.getId() != null ){

//            if ( this.getFormula() ) {
//                if ( this.getId().getComponente() != null ){
//                    st.append( "(" + this.getId().getComponente().getCodigoFormula() + ") - " + this.getId().getComponente().getDescricao() );
//                }
//            }else{
                if ( this.getId().getItem() != null ){
                    st.append( this.getId().getItem() );
                    st.append( " - " );
                }

                if ( this.getId().getComponente() != null ){
                    st.append( this.getId().getComponente().getDescricaoFormatado() );
                }
//            }
        } else{
            st.append( super.toString() );
        }

        return st.toString();
    }

    public String getDescricaoMovimentacao() {
        if ( EstruturaEquipamentoRevisao.INSERIR.equals(this.getFlagMovimentacao()) ){
            return Bundle.getStringApplication( "rotulo_novo" );
        }else if ( EstruturaEquipamentoRevisao.APAGADO.equals(this.getFlagMovimentacao()) ){
            return Bundle.getStringApplication("rotulo_apagado");
        }else if ( EstruturaEquipamentoRevisao.MODIFICADO.equals(this.getFlagMovimentacao()) ){
            return Bundle.getStringApplication("rotulo_modificado");
        }else if ( EstruturaEquipamentoRevisao.SUBSTITUIDO.equals(this.getFlagMovimentacao()) ){
            return Bundle.getStringApplication( "rotulo_substituido" );
        }else if ( EstruturaEquipamentoRevisao.CALCULO_QUANTIDADE_EXTRA.equals(this.getFlagMovimentacao())){
            return Bundle.getStringApplication( "rotulo_calcula_extra_abv");
        }else{
            return "";
        }
    }
}