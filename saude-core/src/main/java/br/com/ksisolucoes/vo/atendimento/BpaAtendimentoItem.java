package br.com.ksisolucoes.vo.atendimento;

import java.io.Serializable;

import br.com.ksisolucoes.vo.atendimento.base.BaseBpaAtendimentoItem;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class BpaAtendimentoItem extends BaseBpaAtendimentoItem implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public BpaAtendimentoItem () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public BpaAtendimentoItem (java.lang.Long codigo) {
		super(codigo);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (Long) key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}