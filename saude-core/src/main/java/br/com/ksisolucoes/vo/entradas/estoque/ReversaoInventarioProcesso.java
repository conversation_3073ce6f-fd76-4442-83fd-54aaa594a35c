package br.com.ksisolucoes.vo.entradas.estoque;

import java.io.Serializable;

import br.com.ksisolucoes.vo.entradas.estoque.base.BaseReversaoInventarioProcesso;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class ReversaoInventarioProcesso extends BaseReversaoInventarioProcesso implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public ReversaoInventarioProcesso () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public ReversaoInventarioProcesso (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public ReversaoInventarioProcesso (
		java.lang.Long codigo,
		java.util.Date dataGeracao) {

		super (
			codigo,
			dataGeracao);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}