package br.com.ksisolucoes.vo.entradas.estoque;

import java.io.Serializable;

import br.com.ksisolucoes.vo.entradas.estoque.base.BaseControleImportacaoProdutoBrasindice;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class ControleImportacaoProdutoBrasindice extends BaseControleImportacaoProdutoBrasindice implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public ControleImportacaoProdutoBrasindice () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public ControleImportacaoProdutoBrasindice (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public ControleImportacaoProdutoBrasindice (
		java.lang.Long codigo,
		java.util.Date dataImportacao) {

		super (
			codigo,
			dataImportacao);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}