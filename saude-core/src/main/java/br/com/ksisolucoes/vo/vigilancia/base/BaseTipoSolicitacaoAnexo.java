package br.com.ksisolucoes.vo.vigilancia.base;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;

import java.io.Serializable;


/**
 * This is an object that contains data related to the tipo_solicitacao_anexo table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="tipo_solicitacao_anexo"
 */

public abstract class BaseTipoSolicitacaoAnexo extends BaseRootVO implements Serializable {

	public static String REF = "TipoSolicitacaoAnexo";
	public static final String PROP_TIPO_DOCUMENTO = "tipoDocumento";
	public static final String PROP_DESCRICAO = "descricao";
	public static final String PROP_REGIAO_ABASTECIDA_AGUA = "regiaoAbastecidaAgua";
	public static final String PROP_OBSERVACAO = "observacao";
	public static final String PROP_USO_EDIFICACAO = "usoEdificacao";
	public static final String PROP_FLAG_PERMITE_REPETIR = "flagPermiteRepetir";
	public static final String PROP_ATIVIDADE_ESTABELECIMENTO = "atividadeEstabelecimento";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_REGIAO_COBERTA_REDE_ESGOTO = "regiaoCobertaRedeEsgoto";
	public static final String PROP_OBRIGATORIO = "obrigatorio";
	public static final String PROP_TIPO_SOLICITACAO = "tipoSolicitacao";


	// constructors
	public BaseTipoSolicitacaoAnexo () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseTipoSolicitacaoAnexo (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseTipoSolicitacaoAnexo (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.TipoSolicitacao tipoSolicitacao,
		java.lang.String descricao,
		java.lang.Long obrigatorio,
		java.lang.Long flagPermiteRepetir) {

		this.setCodigo(codigo);
		this.setTipoSolicitacao(tipoSolicitacao);
		this.setDescricao(descricao);
		this.setObrigatorio(obrigatorio);
		this.setFlagPermiteRepetir(flagPermiteRepetir);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String descricao;
	private java.lang.Long obrigatorio;
	private java.lang.Long tipoDocumento;
	private java.lang.String observacao;
	private java.lang.Long regiaoAbastecidaAgua;
	private java.lang.Long regiaoCobertaRedeEsgoto;
	private java.lang.Long usoEdificacao;
	private java.lang.Long flagPermiteRepetir;

	// many to one
	private br.com.ksisolucoes.vo.vigilancia.TipoSolicitacao tipoSolicitacao;
	private br.com.ksisolucoes.vo.vigilancia.AtividadeEstabelecimento atividadeEstabelecimento;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_tipo_solicitacao_anexo"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: descricao
	 */
	public java.lang.String getDescricao () {
		return getPropertyValue(this, descricao, PROP_DESCRICAO); 
	}

	/**
	 * Set the value related to the column: descricao
	 * @param descricao the descricao value
	 */
	public void setDescricao (java.lang.String descricao) {
//        java.lang.String descricaoOld = this.descricao;
		this.descricao = descricao;
//        this.getPropertyChangeSupport().firePropertyChange ("descricao", descricaoOld, descricao);
	}



	/**
	 * Return the value associated with the column: obrigatorio
	 */
	public java.lang.Long getObrigatorio () {
		return getPropertyValue(this, obrigatorio, PROP_OBRIGATORIO); 
	}

	/**
	 * Set the value related to the column: obrigatorio
	 * @param obrigatorio the obrigatorio value
	 */
	public void setObrigatorio (java.lang.Long obrigatorio) {
//        java.lang.Long obrigatorioOld = this.obrigatorio;
		this.obrigatorio = obrigatorio;
//        this.getPropertyChangeSupport().firePropertyChange ("obrigatorio", obrigatorioOld, obrigatorio);
	}



	/**
	 * Return the value associated with the column: tipo_documento
	 */
	public java.lang.Long getTipoDocumento () {
		return getPropertyValue(this, tipoDocumento, PROP_TIPO_DOCUMENTO); 
	}

	/**
	 * Set the value related to the column: tipo_documento
	 * @param tipoDocumento the tipo_documento value
	 */
	public void setTipoDocumento (java.lang.Long tipoDocumento) {
//        java.lang.Long tipoDocumentoOld = this.tipoDocumento;
		this.tipoDocumento = tipoDocumento;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoDocumento", tipoDocumentoOld, tipoDocumento);
	}



	/**
	 * Return the value associated with the column: observacao
	 */
	public java.lang.String getObservacao () {
		return getPropertyValue(this, observacao, PROP_OBSERVACAO); 
	}

	/**
	 * Set the value related to the column: observacao
	 * @param observacao the observacao value
	 */
	public void setObservacao (java.lang.String observacao) {
//        java.lang.String observacaoOld = this.observacao;
		this.observacao = observacao;
//        this.getPropertyChangeSupport().firePropertyChange ("observacao", observacaoOld, observacao);
	}



	/**
	 * Return the value associated with the column: regiao_abastecida_agua
	 */
	public java.lang.Long getRegiaoAbastecidaAgua () {
		return getPropertyValue(this, regiaoAbastecidaAgua, PROP_REGIAO_ABASTECIDA_AGUA); 
	}

	/**
	 * Set the value related to the column: regiao_abastecida_agua
	 * @param regiaoAbastecidaAgua the regiao_abastecida_agua value
	 */
	public void setRegiaoAbastecidaAgua (java.lang.Long regiaoAbastecidaAgua) {
//        java.lang.Long regiaoAbastecidaAguaOld = this.regiaoAbastecidaAgua;
		this.regiaoAbastecidaAgua = regiaoAbastecidaAgua;
//        this.getPropertyChangeSupport().firePropertyChange ("regiaoAbastecidaAgua", regiaoAbastecidaAguaOld, regiaoAbastecidaAgua);
	}



	/**
	 * Return the value associated with the column: regiao_coberta_rede_esgoto
	 */
	public java.lang.Long getRegiaoCobertaRedeEsgoto () {
		return getPropertyValue(this, regiaoCobertaRedeEsgoto, PROP_REGIAO_COBERTA_REDE_ESGOTO); 
	}

	/**
	 * Set the value related to the column: regiao_coberta_rede_esgoto
	 * @param regiaoCobertaRedeEsgoto the regiao_coberta_rede_esgoto value
	 */
	public void setRegiaoCobertaRedeEsgoto (java.lang.Long regiaoCobertaRedeEsgoto) {
//        java.lang.Long regiaoCobertaRedeEsgotoOld = this.regiaoCobertaRedeEsgoto;
		this.regiaoCobertaRedeEsgoto = regiaoCobertaRedeEsgoto;
//        this.getPropertyChangeSupport().firePropertyChange ("regiaoCobertaRedeEsgoto", regiaoCobertaRedeEsgotoOld, regiaoCobertaRedeEsgoto);
	}



	/**
	 * Return the value associated with the column: uso_edificacao
	 */
	public java.lang.Long getUsoEdificacao () {
		return getPropertyValue(this, usoEdificacao, PROP_USO_EDIFICACAO); 
	}

	/**
	 * Set the value related to the column: uso_edificacao
	 * @param usoEdificacao the uso_edificacao value
	 */
	public void setUsoEdificacao (java.lang.Long usoEdificacao) {
//        java.lang.Long usoEdificacaoOld = this.usoEdificacao;
		this.usoEdificacao = usoEdificacao;
//        this.getPropertyChangeSupport().firePropertyChange ("usoEdificacao", usoEdificacaoOld, usoEdificacao);
	}



	/**
	 * Return the value associated with the column: flag_permite_repetir
	 */
	public java.lang.Long getFlagPermiteRepetir () {
		return getPropertyValue(this, flagPermiteRepetir, PROP_FLAG_PERMITE_REPETIR); 
	}

	/**
	 * Set the value related to the column: flag_permite_repetir
	 * @param flagPermiteRepetir the flag_permite_repetir value
	 */
	public void setFlagPermiteRepetir (java.lang.Long flagPermiteRepetir) {
//        java.lang.Long flagPermiteRepetirOld = this.flagPermiteRepetir;
		this.flagPermiteRepetir = flagPermiteRepetir;
//        this.getPropertyChangeSupport().firePropertyChange ("flagPermiteRepetir", flagPermiteRepetirOld, flagPermiteRepetir);
	}



	/**
	 * Return the value associated with the column: cd_tipo_solicitacao
	 */
	public br.com.ksisolucoes.vo.vigilancia.TipoSolicitacao getTipoSolicitacao () {
		return getPropertyValue(this, tipoSolicitacao, PROP_TIPO_SOLICITACAO); 
	}

	/**
	 * Set the value related to the column: cd_tipo_solicitacao
	 * @param tipoSolicitacao the cd_tipo_solicitacao value
	 */
	public void setTipoSolicitacao (br.com.ksisolucoes.vo.vigilancia.TipoSolicitacao tipoSolicitacao) {
//        br.com.ksisolucoes.vo.vigilancia.TipoSolicitacao tipoSolicitacaoOld = this.tipoSolicitacao;
		this.tipoSolicitacao = tipoSolicitacao;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoSolicitacao", tipoSolicitacaoOld, tipoSolicitacao);
	}



	/**
	 * Return the value associated with the column: cd_atividade_estabelecimento
	 */
	public br.com.ksisolucoes.vo.vigilancia.AtividadeEstabelecimento getAtividadeEstabelecimento () {
		return getPropertyValue(this, atividadeEstabelecimento, PROP_ATIVIDADE_ESTABELECIMENTO); 
	}

	/**
	 * Set the value related to the column: cd_atividade_estabelecimento
	 * @param atividadeEstabelecimento the cd_atividade_estabelecimento value
	 */
	public void setAtividadeEstabelecimento (br.com.ksisolucoes.vo.vigilancia.AtividadeEstabelecimento atividadeEstabelecimento) {
//        br.com.ksisolucoes.vo.vigilancia.AtividadeEstabelecimento atividadeEstabelecimentoOld = this.atividadeEstabelecimento;
		this.atividadeEstabelecimento = atividadeEstabelecimento;
//        this.getPropertyChangeSupport().firePropertyChange ("atividadeEstabelecimento", atividadeEstabelecimentoOld, atividadeEstabelecimento);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.vigilancia.TipoSolicitacaoAnexo)) return false;
		else {
			br.com.ksisolucoes.vo.vigilancia.TipoSolicitacaoAnexo tipoSolicitacaoAnexo = (br.com.ksisolucoes.vo.vigilancia.TipoSolicitacaoAnexo) obj;
			if (null == this.getCodigo() || null == tipoSolicitacaoAnexo.getCodigo()) return false;
			else return (this.getCodigo().equals(tipoSolicitacaoAnexo.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}