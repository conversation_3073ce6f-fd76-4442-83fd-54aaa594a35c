package br.com.ksisolucoes.vo.vigilancia.agravo.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the integracao_agravo table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="integracao_agravo"
 */

public abstract class BaseIntegracaoAgravoNotificacao extends BaseRootVO implements Serializable {

	public static String REF = "IntegracaoAgravoNotificacao";
	public static final String PROP_TIPO_REQUISICAO = "tipoRequisicao";
	public static final String PROP_RESPOSTA_JSON = "respostaJson";
	public static final String PROP_RESPOSTA_STATUS = "respostaStatus";
	public static final String PROP_DATA_INTEGRACAO = "dataIntegracao";
	public static final String PROP_ID = "id";


	// constructors
	public BaseIntegracaoAgravoNotificacao () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseIntegracaoAgravoNotificacao (br.com.ksisolucoes.vo.vigilancia.agravo.IntegracaoAgravoPK id) {
		this.setId(id);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseIntegracaoAgravoNotificacao (
		br.com.ksisolucoes.vo.vigilancia.agravo.IntegracaoAgravoPK id,
		java.util.Date dataIntegracao) {

		this.setId(id);
		this.setDataIntegracao(dataIntegracao);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private br.com.ksisolucoes.vo.vigilancia.agravo.IntegracaoAgravoPK id;

	// fields
	private java.lang.Long respostaStatus;
	private java.lang.String respostaJson;
	private java.lang.Long tipoRequisicao;
	private java.util.Date dataIntegracao;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     */
	public br.com.ksisolucoes.vo.vigilancia.agravo.IntegracaoAgravoPK getId () {
	    return getPropertyValue(this,  id, "id" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param id the new ID
	 */
	public void setId (br.com.ksisolucoes.vo.vigilancia.agravo.IntegracaoAgravoPK id) {
		this.id = id;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: resposta_status
	 */
	public java.lang.Long getRespostaStatus () {
		return getPropertyValue(this, respostaStatus, PROP_RESPOSTA_STATUS); 
	}

	/**
	 * Set the value related to the column: resposta_status
	 * @param respostaStatus the resposta_status value
	 */
	public void setRespostaStatus (java.lang.Long respostaStatus) {
//        java.lang.Long respostaStatusOld = this.respostaStatus;
		this.respostaStatus = respostaStatus;
//        this.getPropertyChangeSupport().firePropertyChange ("respostaStatus", respostaStatusOld, respostaStatus);
	}



	/**
	 * Return the value associated with the column: resposta_json
	 */
	public java.lang.String getRespostaJson () {
		return getPropertyValue(this, respostaJson, PROP_RESPOSTA_JSON); 
	}

	/**
	 * Set the value related to the column: resposta_json
	 * @param respostaJson the resposta_json value
	 */
	public void setRespostaJson (java.lang.String respostaJson) {
//        java.lang.String respostaJsonOld = this.respostaJson;
		this.respostaJson = respostaJson;
//        this.getPropertyChangeSupport().firePropertyChange ("respostaJson", respostaJsonOld, respostaJson);
	}



	/**
	 * Return the value associated with the column: tp_requisicao
	 */
	public java.lang.Long getTipoRequisicao () {
		return getPropertyValue(this, tipoRequisicao, PROP_TIPO_REQUISICAO); 
	}

	/**
	 * Set the value related to the column: tp_requisicao
	 * @param tipoRequisicao the tp_requisicao value
	 */
	public void setTipoRequisicao (java.lang.Long tipoRequisicao) {
//        java.lang.Long tipoRequisicaoOld = this.tipoRequisicao;
		this.tipoRequisicao = tipoRequisicao;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoRequisicao", tipoRequisicaoOld, tipoRequisicao);
	}



	/**
	 * Return the value associated with the column: dt_integracao
	 */
	public java.util.Date getDataIntegracao () {
		return getPropertyValue(this, dataIntegracao, PROP_DATA_INTEGRACAO); 
	}

	/**
	 * Set the value related to the column: dt_integracao
	 * @param dataIntegracao the dt_integracao value
	 */
	public void setDataIntegracao (java.util.Date dataIntegracao) {
//        java.util.Date dataIntegracaoOld = this.dataIntegracao;
		this.dataIntegracao = dataIntegracao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataIntegracao", dataIntegracaoOld, dataIntegracao);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.vigilancia.agravo.IntegracaoAgravoNotificacao)) return false;
		else {
			br.com.ksisolucoes.vo.vigilancia.agravo.IntegracaoAgravoNotificacao integracaoAgravoNotificacao = (br.com.ksisolucoes.vo.vigilancia.agravo.IntegracaoAgravoNotificacao) obj;
			if (null == this.getId() || null == integracaoAgravoNotificacao.getId()) return false;
			else return (this.getId().equals(integracaoAgravoNotificacao.getId()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getId()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getId().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}