<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.prontuario.procedimento"  >
    <class name="SolicitacaoProcedimentos" table="solicitacao_procedimentos" >
        <id
            name="codigo"
            type="java.lang.Long"
            column="cd_sol_proced"
        >
            <generator class="assigned" />
        </id> 
        <version column="version" name="version" type="long" />

        <many-to-one 
            class="br.com.ksisolucoes.vo.prontuario.basico.Atendimento"
            name="atendimento"
            column="nr_atendimento"
            not-null="true"
        />
        
        <property
            name="justificativa"
            column="justificativa"
            type="java.lang.String"
            not-null="true"
        />
        
        <property 
            name="dataSolicitacao"
            column="data_solicitacao"
            type="date"
            not-null="true"
        />
        
        <many-to-one 
            class="br.com.ksisolucoes.vo.cadsus.Profissional"
            name="profissionalSolicitante"
            column="cd_profissional_solicitante"
        />
        
        <many-to-one 
            class="br.com.ksisolucoes.vo.prontuario.hospital.Aih"
            name="aih"
            column="cd_aut_intern_hosp"
        />

    </class>	
</hibernate-mapping>