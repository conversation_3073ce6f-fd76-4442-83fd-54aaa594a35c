package br.com.ksisolucoes.vo.prontuario.emergencia.base;

import java.io.Serializable;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;


/**
 * This is an object that contains data related to the prontuario_eventos table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="prontuario_eventos"
 */

public abstract class BaseProntuarioEventos extends BaseRootVO implements Serializable {

	public static String REF = "ProntuarioEventos";
	public static final String PROP_DATA = "data";
	public static final String PROP_EMPRESA_ATENDIMENTO = "empresaAtendimento";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_TIPO_ATENDIMENTO = "tipoAtendimento";
	public static final String PROP_USUARIO_CADSUS = "usuarioCadsus";
	public static final String PROP_PRONTUARIO_EVENTOS_PRINCIPAL = "prontuarioEventosPrincipal";
	public static final String PROP_OBSERVACAO = "observacao";


	// constructors
	public BaseProntuarioEventos () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseProntuarioEventos (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseProntuarioEventos (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.basico.Empresa empresaAtendimento,
		br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus,
		java.lang.Long tipoAtendimento,
		java.util.Date data) {

		this.setCodigo(codigo);
		this.setEmpresaAtendimento(empresaAtendimento);
		this.setUsuarioCadsus(usuarioCadsus);
		this.setTipoAtendimento(tipoAtendimento);
		this.setData(data);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Long tipoAtendimento;
	private java.util.Date data;
	private java.lang.String observacao;

	// many to one
	private br.com.ksisolucoes.vo.basico.Empresa empresaAtendimento;
	private br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus;
	private br.com.ksisolucoes.vo.prontuario.emergencia.ProntuarioEventos prontuarioEventosPrincipal;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="id_prontuario_evento"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: tipo_atendimento
	 */
	public java.lang.Long getTipoAtendimento () {
		return getPropertyValue(this, tipoAtendimento, PROP_TIPO_ATENDIMENTO); 
	}

	/**
	 * Set the value related to the column: tipo_atendimento
	 * @param tipoAtendimento the tipo_atendimento value
	 */
	public void setTipoAtendimento (java.lang.Long tipoAtendimento) {
//        java.lang.Long tipoAtendimentoOld = this.tipoAtendimento;
		this.tipoAtendimento = tipoAtendimento;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoAtendimento", tipoAtendimentoOld, tipoAtendimento);
	}



	/**
	 * Return the value associated with the column: data
	 */
	public java.util.Date getData () {
		return getPropertyValue(this, data, PROP_DATA); 
	}

	/**
	 * Set the value related to the column: data
	 * @param data the data value
	 */
	public void setData (java.util.Date data) {
//        java.util.Date dataOld = this.data;
		this.data = data;
//        this.getPropertyChangeSupport().firePropertyChange ("data", dataOld, data);
	}



	/**
	 * Return the value associated with the column: observacao
	 */
	public java.lang.String getObservacao () {
		return getPropertyValue(this, observacao, PROP_OBSERVACAO); 
	}

	/**
	 * Set the value related to the column: observacao
	 * @param observacao the observacao value
	 */
	public void setObservacao (java.lang.String observacao) {
//        java.lang.String observacaoOld = this.observacao;
		this.observacao = observacao;
//        this.getPropertyChangeSupport().firePropertyChange ("observacao", observacaoOld, observacao);
	}



	/**
	 * Return the value associated with the column: empresa_atendimento
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getEmpresaAtendimento () {
		return getPropertyValue(this, empresaAtendimento, PROP_EMPRESA_ATENDIMENTO); 
	}

	/**
	 * Set the value related to the column: empresa_atendimento
	 * @param empresaAtendimento the empresa_atendimento value
	 */
	public void setEmpresaAtendimento (br.com.ksisolucoes.vo.basico.Empresa empresaAtendimento) {
//        br.com.ksisolucoes.vo.basico.Empresa empresaAtendimentoOld = this.empresaAtendimento;
		this.empresaAtendimento = empresaAtendimento;
//        this.getPropertyChangeSupport().firePropertyChange ("empresaAtendimento", empresaAtendimentoOld, empresaAtendimento);
	}



	/**
	 * Return the value associated with the column: cd_usu_cadsus
	 */
	public br.com.ksisolucoes.vo.cadsus.UsuarioCadsus getUsuarioCadsus () {
		return getPropertyValue(this, usuarioCadsus, PROP_USUARIO_CADSUS); 
	}

	/**
	 * Set the value related to the column: cd_usu_cadsus
	 * @param usuarioCadsus the cd_usu_cadsus value
	 */
	public void setUsuarioCadsus (br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus) {
//        br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsusOld = this.usuarioCadsus;
		this.usuarioCadsus = usuarioCadsus;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCadsus", usuarioCadsusOld, usuarioCadsus);
	}



	/**
	 * Return the value associated with the column: id_evento_principal
	 */
	public br.com.ksisolucoes.vo.prontuario.emergencia.ProntuarioEventos getProntuarioEventosPrincipal () {
		return getPropertyValue(this, prontuarioEventosPrincipal, PROP_PRONTUARIO_EVENTOS_PRINCIPAL); 
	}

	/**
	 * Set the value related to the column: id_evento_principal
	 * @param prontuarioEventosPrincipal the id_evento_principal value
	 */
	public void setProntuarioEventosPrincipal (br.com.ksisolucoes.vo.prontuario.emergencia.ProntuarioEventos prontuarioEventosPrincipal) {
//        br.com.ksisolucoes.vo.prontuario.emergencia.ProntuarioEventos prontuarioEventosPrincipalOld = this.prontuarioEventosPrincipal;
		this.prontuarioEventosPrincipal = prontuarioEventosPrincipal;
//        this.getPropertyChangeSupport().firePropertyChange ("prontuarioEventosPrincipal", prontuarioEventosPrincipalOld, prontuarioEventosPrincipal);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.emergencia.ProntuarioEventos)) return false;
		else {
			br.com.ksisolucoes.vo.prontuario.emergencia.ProntuarioEventos prontuarioEventos = (br.com.ksisolucoes.vo.prontuario.emergencia.ProntuarioEventos) obj;
			if (null == this.getCodigo() || null == prontuarioEventos.getCodigo()) return false;
			else return (this.getCodigo().equals(prontuarioEventos.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }

    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}