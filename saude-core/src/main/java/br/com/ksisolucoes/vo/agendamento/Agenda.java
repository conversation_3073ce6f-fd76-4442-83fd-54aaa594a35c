package br.com.ksisolucoes.vo.agendamento;

import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.agendamento.base.BaseAgenda;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;

import java.io.Serializable;
import java.util.List;



public class Agenda extends BaseAgenda implements CodigoManager {
	private static final long serialVersionUID = 1L;
    public static final String PROP_DESCRICAO_STATUS = "descricaoStatus";
    public static final String PROP_DESCRICAO_TIPO_AGENDA = "DescricaoTipoAgenda";

    private List<AgendaGrade> agendaGradeList;
    public static final long STATUS_ABERTO = 1L;
    public static final long STATUS_CONFIRMADO = 2L;
    public static final long STATUS_CANCELADO = 3L;
    public static final long STATUS_INATIVA = 4L;
    public static final long STATUS_PENDENTE = 5L;
    public static final long STATUS_NAO_APROVADA = 6L;

    public static final String TIPO_AGENDA_LOCAL = "L";
    public static final String TIPO_AGENDA_COMPARTILHADA = "C";


/*[CONSTRUCTOR MARKER BEGIN]*/
	public Agenda () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public Agenda (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public Agenda (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.basico.Empresa empresa,
		br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento tipoProcedimento,
		java.lang.Long status,
		java.lang.Long visibilidadeAgenda,
		java.lang.Long tempoTotalAgenda,
		java.lang.Long flagRequerAprovacao) {

		super (
			codigo,
			empresa,
			tipoProcedimento,
			status,
			visibilidadeAgenda,
			tempoTotalAgenda,
			flagRequerAprovacao);
	}

/*[CONSTRUCTOR MARKER END]*/



    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

//    public List<AgendaGrade> getAgendaGradeList() {
//        return agendaGradeList;
//    }
//
//    public void setAgendaGradeList(List<AgendaGrade> agendaGradeList) {
//        this.agendaGradeList = agendaGradeList;
//    }

    public String getDescricaoStatus(){
        if(getStatus() != null){
            if(STATUS_ABERTO == getStatus()){
                return Bundle.getStringApplication("rotulo_nao_aprovado");
            }else if(STATUS_CANCELADO == getStatus()){
                return Bundle.getStringApplication("rotulo_cancelado");
            }else if(STATUS_CONFIRMADO == getStatus()){
                return Bundle.getStringApplication("rotulo_confirmado");
            }else if(STATUS_INATIVA == getStatus()){
                return Bundle.getStringApplication("rotulo_inativa");
            }else if(STATUS_PENDENTE == getStatus()){
                return Bundle.getStringApplication("rotulo_pendente");
            }else if(STATUS_NAO_APROVADA == getStatus()){
                return Bundle.getStringApplication("rotulo_nao_aprovada");
            }
        }
        return "";
    }

    public String getDescricaoTipoAgenda(){
        if(TIPO_AGENDA_LOCAL.equals(getTipoAgenda())){
            return Bundle.getStringApplication("rotulo_local");
        }
        return Bundle.getStringApplication("rotulo_compartilhada");
    }

    public boolean criterioIdadeZerado(){
        return Long.valueOf(0).equals(idade(getIdadeInicio())) && Long.valueOf(0).equals(idade(getIdadeFim()));
    }

    public boolean criterioIdadeEntre(Long idade){
        return idade >= idade(getIdadeInicio()) && idade <= idade(getIdadeFim());
    }

    private Long idade(Long idade){
        return idade != null ? idade : 0L;
    }

    public String getSexoFormatado() {
        if (RepositoryComponentDefault.SEXO_MASCULINO.equals(getSexo())) {
            return Bundle.getStringApplication("rotulo_masculino");
        } else if (RepositoryComponentDefault.SEXO_FEMININO.equals(getSexo())) {
            return Bundle.getStringApplication("rotulo_feminino");
        }
        return "";
    }

    public Long getIdadeInicioFormatada(){
        return getIdadeInicio() != null ? getIdadeInicio() : 0L;
    }

    public Long getIdadeFimFormatada(){
        return getIdadeFim() != null ? getIdadeFim() : 0L;
    }

    public boolean isTipoAgendaCompartilhada(){
        return Agenda.TIPO_AGENDA_COMPARTILHADA.equals(getTipoAgenda());
    }

    public String getVisibilidadeFormatado() {
        return getVisibilidadeAgenda() + " semanas";
    }

}