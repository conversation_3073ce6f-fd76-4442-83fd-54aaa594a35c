<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >
	
<hibernate-mapping package="br.com.ksisolucoes.vo.agendamento"  >
    <class 
        name="AgendamentoProcedimento"
        table="agendamento_procedimento"
    >

        <id
            name="codigo"
            type="java.lang.Long"
            column="cd_ag_procedimento"
        >
            <generator class="sequence">
                <param name="sequence">seq_agendamento_procedimento</param>
            </generator>
        </id> 
        <version column="version" name="version" type="long" />


        <many-to-one class="AgendaGradeAtendimentoHorario"
         name="agendaGradeAtendimentoHorario"
         not-null="true">
            <column name="cd_ag_gra_ate_hor"/>
        </many-to-one>

 
        <many-to-one
            class="br.com.ksisolucoes.vo.prontuario.basico.ExameProcedimento"
            name="exameProcedimento"
            not-null="true"
        >
            <column name="cd_exame_procedimento" />
        </many-to-one>

    </class>
</hibernate-mapping>
