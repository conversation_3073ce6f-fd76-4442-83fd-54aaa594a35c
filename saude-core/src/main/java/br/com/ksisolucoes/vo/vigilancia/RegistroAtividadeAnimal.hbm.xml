<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.vigilancia"  >
    <class name="RegistroAtividadeAnimal" table="registro_atividade_animal">
        <id
            column="cd_reg_atv_animal"
            name="codigo"
            type="java.lang.Long"
        >
            <generator class="assigned" />
        </id> 
        <version column="version" name="version" type="long" />
                
        <many-to-one
            class="br.com.ksisolucoes.vo.vigilancia.RegistroAtividadeVeterinaria"
            column="cd_reg_atv_vet"
            name="registroAtividadeVeterinaria"
            not-null="true"
        />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.vigilancia.EspecieAnimal"
            column="cd_especie_animal"
            name="especieAnimal"
            not-null="true"
        />

        <property
            column="sexo"
            name="sexo"
            not-null="true"
            type="java.lang.Long"
        />
        
        <property
            column="qtdade"
            name="quantidade"
            not-null="true"
            type="java.lang.Long"
        />
    </class>
</hibernate-mapping>
