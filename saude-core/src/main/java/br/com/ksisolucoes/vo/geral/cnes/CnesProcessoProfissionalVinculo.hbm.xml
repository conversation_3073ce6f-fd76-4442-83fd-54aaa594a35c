<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.geral.cnes"  >
    <class name="CnesProcessoProfissionalVinculo" table="cnes_processo_prof_vinculo" >
        <id
                name="codigo"
                type="java.lang.Long"
                column="cd_cnes_processo_prof_vinculo"
        >
            <generator class="assigned" />
        </id>

        <version column="version" name="version" type="long" />

        <many-to-one
                class="br.com.ksisolucoes.vo.geral.cnes.CnesProcessoProfissional"
                name="cnesProcessoProfissional"
                not-null="true"
        >
            <column name="cd_cnes_processo_profissional"/>
        </many-to-one>

        <property
                name="codigoCbo"
                column="cod_cbo"
                type="java.lang.String"
                not-null="true"
                length="10"
        />

        <property
                name="indicaVinculacao"
                column="ind_vinc"
                type="java.lang.String"
                not-null="true"
                length="10"
        />

        <property
                name="quantidadeCargaHorariaOutro"
                column="qtd_carga_hora_outro"
                type="java.lang.Long"
                not-null="false"
        />

        <property
                name="quantidadeCargaHorariaAmbulatorial"
                column="qtd_carga_hora_ambu"
                type="java.lang.Long"
                not-null="false"
        />

        <property
                name="quantidadeCargaHorariaHospital"
                column="qtd_carga_hora_hosp"
                type="java.lang.Long"
                not-null="false"
        />

        <property
                name="codigoConselho"
                column="cod_conselho"
                type="java.lang.String"
                not-null="false"
                length="5"
        />

        <property
                name="numeroRegistro"
                column="num_registro"
                type="java.lang.String"
                not-null="false"
                length="60"
        />

        <property
                name="vinculoSus"
                column="vinculo_sus"
                type="java.lang.String"
                not-null="false"
                length="5"
        />

        <property
                name="usuario"
                column="usuario"
                type="java.lang.String"
                not-null="true"
                length="100"
        />

        <property
                name="unidadeId"
                column="unidade_id"
                type="java.lang.String"
                not-null="true"
                length="50"
        />

    </class>
</hibernate-mapping>