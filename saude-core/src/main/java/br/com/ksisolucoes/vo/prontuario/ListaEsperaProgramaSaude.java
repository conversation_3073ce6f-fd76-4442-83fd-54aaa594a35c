package br.com.ksisolucoes.vo.prontuario;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.prontuario.base.BaseListaEsperaProgramaSaude;

public class ListaEsperaProgramaSaude extends BaseListaEsperaProgramaSaude implements CodigoManager, ICancelarVO {

    private static final long serialVersionUID = 1L;

    public static final String PROP_HORA_CHEGADA_EXIBICAO = "horaChegadaExibicao";
    public static final String PROP_DESCRICAO_TIPO_PROGRAMA_SAUDE = "descricaoTipoProgramaSaude";

    public static final Long STATUS_AGUARDANDO_ATENDIMENTO = 0L;
    public static final Long STATUS_FINALIZADO_ATENDIMENTO = 3L;
    public static final Long STATUS_ATENDIMENTO_CANCELADO = 4L;

    public static final String TIPO_PROGRAMA_SAUDE_TESTE_PEZINHO = "P";
    public static final String TIPO_PROGRAMA_SAUDE_HIPERDIA = "H";
    public static final String TIPO_PROGRAMA_SAUDE_PREVENTIVO = "V";
    public static final String TIPO_PROGRAMA_SAUDE_SIAB = "B";
    public static final String TIPO_PROGRAMA_SAUDE_SIM = "M";
    public static final String TIPO_PROGRAMA_SAUDE_SINASC = "C";
    public static final String TIPO_PROGRAMA_SAUDE_SINAN = "N";
    public static final String TIPO_PROGRAMA_SAUDE_SIS_PRENATAL = "T";
    public static final String TIPO_PROGRAMA_SAUDE_PLANEJAMENTO_FAMILIAR = "F";

    /*[CONSTRUCTOR MARKER BEGIN]*/
	public ListaEsperaProgramaSaude () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public ListaEsperaProgramaSaude (br.com.ksisolucoes.vo.prontuario.ListaEsperaProgramaSaudePK id) {
		super(id);
	}

	/**
	 * Constructor for required fields
	 */
	public ListaEsperaProgramaSaude (
		br.com.ksisolucoes.vo.prontuario.ListaEsperaProgramaSaudePK id,
		br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		java.lang.String tipoProgramaSaude,
		java.util.Date dataChegada,
		java.lang.Long status) {

		super (
			id,
			usuarioCadsus,
			usuario,
			tipoProgramaSaude,
			dataChegada,
			status);
	}

    /*[CONSTRUCTOR MARKER END]*/
    public void setCodigoManager(Serializable key) {
        this.setId((br.com.ksisolucoes.vo.prontuario.ListaEsperaProgramaSaudePK) key);
    }

    public Serializable getCodigoManager() {
        return this.getId();
    }

    public String getDescricaoTipoProgramaSaude() {
        return getDescricaoTipoProgramaSaude(getTipoProgramaSaude());
    }

    public static String getDescricaoTipoProgramaSaude(String tipoProgramaSaude) {
        String descricao = Bundle.getStringApplication("rotulo_invalido");
        if (TIPO_PROGRAMA_SAUDE_HIPERDIA.equals(tipoProgramaSaude)) {
            descricao = Bundle.getStringApplication("rotulo_hiperdia");
        } else if (TIPO_PROGRAMA_SAUDE_PREVENTIVO.equals(tipoProgramaSaude)) {
            descricao = Bundle.getStringApplication("rotulo_preventivo");
        } else if (TIPO_PROGRAMA_SAUDE_TESTE_PEZINHO.equals(tipoProgramaSaude)) {
            descricao = Bundle.getStringApplication("rotulo_teste_pezinho");
        } else if (TIPO_PROGRAMA_SAUDE_SIAB.equals(tipoProgramaSaude)) {
            descricao = Bundle.getStringApplication("rotulo_siab");
        } else if (TIPO_PROGRAMA_SAUDE_SIM.equals(tipoProgramaSaude)) {
            descricao = Bundle.getStringApplication("rotulo_psf_sim");
        } else if (TIPO_PROGRAMA_SAUDE_SINASC.equals(tipoProgramaSaude)) {
            descricao = Bundle.getStringApplication("rotulo_sinasc");
        } else if (TIPO_PROGRAMA_SAUDE_SINAN.equals(tipoProgramaSaude)) {
            descricao = Bundle.getStringApplication("rotulo_sinan");
        } else if (TIPO_PROGRAMA_SAUDE_SIS_PRENATAL.equals(tipoProgramaSaude)) {
            descricao = Bundle.getStringApplication("rotulo_sis_prenatal");
        } else if (TIPO_PROGRAMA_SAUDE_PLANEJAMENTO_FAMILIAR.equals(tipoProgramaSaude)) {
            descricao = Bundle.getStringApplication("rotulo_planejamento_familiar");
        }
        return descricao;
    }

    public static Map<String, String> getTipoProgramaSaudeMap() {
        Map<String, String> map = new HashMap<String, String>();

        map.put(TIPO_PROGRAMA_SAUDE_HIPERDIA, getDescricaoTipoProgramaSaude(TIPO_PROGRAMA_SAUDE_HIPERDIA));
        map.put(TIPO_PROGRAMA_SAUDE_PREVENTIVO, getDescricaoTipoProgramaSaude(TIPO_PROGRAMA_SAUDE_PREVENTIVO));
        map.put(TIPO_PROGRAMA_SAUDE_TESTE_PEZINHO, getDescricaoTipoProgramaSaude(TIPO_PROGRAMA_SAUDE_TESTE_PEZINHO));
        map.put(TIPO_PROGRAMA_SAUDE_SIAB, getDescricaoTipoProgramaSaude(TIPO_PROGRAMA_SAUDE_SIAB));
        map.put(TIPO_PROGRAMA_SAUDE_SIM, getDescricaoTipoProgramaSaude(TIPO_PROGRAMA_SAUDE_SIM));
        map.put(TIPO_PROGRAMA_SAUDE_SINAN, getDescricaoTipoProgramaSaude(TIPO_PROGRAMA_SAUDE_SINAN));
        map.put(TIPO_PROGRAMA_SAUDE_SINASC, getDescricaoTipoProgramaSaude(TIPO_PROGRAMA_SAUDE_SINASC));
        map.put(TIPO_PROGRAMA_SAUDE_SIS_PRENATAL, getDescricaoTipoProgramaSaude(TIPO_PROGRAMA_SAUDE_SIS_PRENATAL));
        map.put(TIPO_PROGRAMA_SAUDE_PLANEJAMENTO_FAMILIAR, getDescricaoTipoProgramaSaude(TIPO_PROGRAMA_SAUDE_PLANEJAMENTO_FAMILIAR));

        return map;
    }

    public String getHoraChegadaExibicao() {
        return Bundle.getStringApplication("format_hour_minute", this.getDataChegada());
    }
}
