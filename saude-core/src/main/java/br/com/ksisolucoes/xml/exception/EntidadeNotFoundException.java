/*
 * EntidadeNotFoundException.java
 *
 * Created on 19 de Maio de 2004, 09:43
 */

package br.com.ksisolucoes.xml.exception;

import br.com.ksisolucoes.util.Bundle;

/**
 * Representa uma excesso para quando no encontra uma determinada entidade no XML.
 * <AUTHOR>
 * @since 1.4
 */
public class EntidadeNotFoundException extends XMLException {
    
    /**
     * Cria uma nova instncia de <code>EntidadeNotFoundException</code> sem detalhes na mensagem.
     */
    public EntidadeNotFoundException() {
        super( Bundle.getStringApplication( "msg_entidade_nao_encontrada" ) );
    }
    
    
    /**
     * Cria uma nova instncia de <code>EntidadeNotFoundException</code> com detalhes na mensagem.
     * @param entidade entidade no encontrada
     */
    public EntidadeNotFoundException(String entidade) {
        super( Bundle.getStringApplication( "msg_entidade_nao_encontrada" ) + " - " + entidade );
    }
}
