/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.system.consulta;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

import br.com.ksisolucoes.bo.command.BuilderQueryCustom.QueryParameter;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom.QuerySorter;

/**
 *
 * <AUTHOR>
 */
public interface ICustomizeConsultaQuery extends Serializable{
    
    Class getClassConsulta();
    
    String[] getProperties();

    boolean isDistinct();

    void consultaCustomizeParameters(List<QueryParameter> parameters);
    
    void consultaCustomizeSorters(List<QuerySorter> sorters);
    
    void consultaCustomizeViewProperties(Map<String, String> properties);
    
    void consultaCustomizeFilterProperties(Map<String, QueryParameter> filterProperties);
    
}
