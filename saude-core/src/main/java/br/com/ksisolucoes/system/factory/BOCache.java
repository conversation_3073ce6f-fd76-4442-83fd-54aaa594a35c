/*
 * Created on 17/03/2005
 *
 */
package br.com.ksisolucoes.system.factory;


import java.util.HashMap;
import java.util.Hashtable;
import java.util.Map;

import javax.naming.Context;
import javax.naming.InitialContext;

import br.com.ksisolucoes.bo.interfaces.FacadeBO;
import br.com.ksisolucoes.reflection.ClassesCache;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;

/**
 * <AUTHOR>
 * 
 * Faz cache de instancias de BO.
 */
public class BOCache {

    private static Map<String, FacadeBO> cache = new HashMap<String, FacadeBO>();

    public static FacadeBO getBO(String className) throws Exception {
        FacadeBO facadeBO = (FacadeBO) cache.get(className);

        if (facadeBO == null) {
            if (!SessaoAplicacaoImp.isExecucaoLocal()) {
                Hashtable env = new Hashtable();
                env.put(Context.INITIAL_CONTEXT_FACTORY, "org.jnp.interfaces.NamingContextFactory");
                env.put(Context.PROVIDER_URL, "localhost:1099");
                env.putAll(System.getProperties());

                InitialContext context = new InitialContext(env);
                String[] path = className.split("\\.");
                String bo = path[path.length-1];
                System.out.println(bo);

                facadeBO = (FacadeBO) context.lookup("saude-ear/" + bo + "/remote");
            } else {
                facadeBO = (FacadeBO) ClassesCache.getClass(className).newInstance();
            }

            cache.put(className, facadeBO);
        }

        return facadeBO;
    }
}
