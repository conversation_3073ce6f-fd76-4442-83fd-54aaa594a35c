/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.system.servicelocator;

import br.com.celk.util.SSLUtils;
import br.com.ksisolucoes.util.log.Loggable;

import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;

/**
 *
 * <AUTHOR>
 */
public class AuthenticationUtil {

/**
 * Método para aceitar automaticamento qualquer certificado utilizado, inclusive auto-assinado.
 */
    public static void acceptSSL() {
//        System.setProperty("javax.net.debug", "all");
//        System.setProperty("java.protocol.handler.pkgs", "javax.net.ssl");
        try {
            SSLContext sc = SSLUtils.getSSLContext();
            HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());

            //utilizado para validar o hostname do certificado com o servidor
            //vide: http://rezulto.com.br/?p=17
//            HttpsURLConnection.setDefaultHostnameVerifier(new HostnameVerifier() {
//                @Override
//                public boolean verify(String string, SSLSession ssls) {
//                    return true;
//                }
//            });

        } catch (Exception e) {
            Loggable.log.error(e.getMessage(), e);
        }
    }

}
