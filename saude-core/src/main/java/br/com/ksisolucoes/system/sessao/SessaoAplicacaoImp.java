/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.system.sessao;

import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.interfaces.PesquisaObjectInterface;
import java.lang.reflect.Method;
import java.util.Locale;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class SessaoAplicacaoImp extends AbstractSessaoAplicacao {

    public static AbstractSessaoAplicacao getInstance() {
        try {
            if (AbstractSessaoAplicacao.isServer()) {
                AbstractSessaoAplicacao retorno = SessaoAplicacaoContainer.getInstance();
                return retorno;
            } else {
                Class sessaoAplicacao = Class.forName("br.com.ksisolucoes.system.sessao.SessaoAplicacaoCliente");
                Method method = sessaoAplicacao.getMethod("getInstance");
                return (AbstractSessaoAplicacao) method.invoke(null);
            }
        } catch (ClassNotFoundException ex) {
            Loggable.log.error(ex.getMessage(),ex);
            Loggable.log.error("Sessao aplicacao nao implementado");
        } catch (NullPointerException ex) {
            Loggable.log.error(ex.getMessage(),ex);
        } catch (Exception ex) {
            Loggable.log.error(ex.getMessage(),ex);
        }

        return null;
    }

    @Override
    public boolean isDevelopmentSession() {
        return getInstance().isDevelopmentSession();
    }

    @Override
    public void setDevelopmentSession(boolean developmentSession) {
        getInstance().setDevelopmentSession(developmentSession);
    }

    @Override
    public Locale getLocale() {
        return getInstance().getLocale();
    }

    @Override
    public <T extends Usuario> T getUsuario() {
        return (T)getInstance().getUsuario();
    }

    @Override
    public <T extends Empresa> T getEmpresa() {
        return (T)getInstance().getEmpresa();
    }

    @Override
    public ReportSessaoAplicacao getReportSessaoAplicacao() {
        return getInstance().getReportSessaoAplicacao();
    }

    public String getName() {
        return ((PesquisaObjectInterface)getInstance().getUsuario()).getDescricaoVO();
    }

    @Override
    public Map<String, String> getIpMac() {
        return getInstance().getIpMac();
    }
}
