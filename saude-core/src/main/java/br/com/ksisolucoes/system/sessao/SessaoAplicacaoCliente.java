/*
 * Created on 06/09/2004
 *
 */
package br.com.ksisolucoes.system.sessao;

import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.vo.basico.Cidade;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.EmpresaMaterial;
import br.com.ksisolucoes.vo.basico.Estado;
import br.com.ksisolucoes.vo.controle.Usuario;

import java.io.Serializable;
import java.net.Inet4Address;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.util.*;

/**
 * <AUTHOR>
 *
 */
public class SessaoAplicacaoCliente extends AbstractSessaoAplicacao implements Serializable, Loggable {

    private static final long serialVersionUID = 1L;
    private static SessaoAplicacaoCliente instanceOld;
    private static SessaoAplicacaoCliente instance;
    private Usuario usuario;
    private Locale locale;
    public boolean developmentSession = false;
    public Map<String,String> mapIpMac;

    /**
     *
     * @param usuario
     * @param empresa
     * @param locale
     */
    private SessaoAplicacaoCliente(Usuario usuario, Empresa empresa, Locale locale) {
        this.usuario = usuario;
        this.codigoEmpresa = empresa.getCodigo();
        this.codigoUsuario = usuario.getCodigo();
        this.locale = locale;
    }

    public static SessaoAplicacaoCliente getInstance() {
        return instance;
    }

    public SessaoAplicacaoCliente restaurarAutenticacao() {
        if(instanceOld != null){
            instance = instanceOld;
            
            instance.usuario = instanceOld.usuario;
            instance.locale = instanceOld.locale;
            instance.developmentSession = instanceOld.developmentSession;
            instance.mapIpMac = instanceOld.mapIpMac;
            
            instanceOld = null;
        }
        
        return instance;
    }
    
    public SessaoAplicacaoCliente reAutenticar(Usuario usuario, Empresa empresa, Locale locale) {
        instanceOld = getInstance();
        
        instance = new SessaoAplicacaoCliente(usuario, empresa, locale);
        instance.getIpMac();//metodo chamado para garantir q o ip sera o do cliente!!

        return instance;
    }
    
    public static SessaoAplicacaoCliente getInstance(Usuario usuario, Empresa empresa, Locale locale) {
        instance = new SessaoAplicacaoCliente(usuario, empresa, locale);
        instance.getIpMac();//metodo chamado para garantir q o ip sera o do cliente!!

        return instance;
    }

    /**
     * @return Returns the usuario.
     */
    public Usuario getUsuario() {
        return usuario;
    }

    /**
     * @return Returns the empresa.
     */
    private Empresa empresaCarregada;
    private Date dataUltimaAtualizacao = null;

    public Empresa getEmpresa() {
        boolean carregar = false;
        if( this.empresaCarregada == null ) {
            carregar = true;
        } else {
            if(dataUltimaAtualizacao == null || (Data.getDataAtual().getTime() - dataUltimaAtualizacao.getTime()) > 10000 ){
                Date dataAtualizacao = LoadManager.getInstance(Empresa.class)
                    .setLazyMode(true)
                    .setId(getCodigoEmpresa())
                    .addProperties(Empresa.PROP_DATA_ATUALIZACAO)
                    .start().<Empresa>getVO().getDataAtualizacao();
                Date dataAtual = this.empresaCarregada.getDataAtualizacao();
                if(dataAtualizacao!=null){
                    carregar = false;
                }
                if( dataAtual == null || dataAtual.before( dataAtualizacao ) ) {
                    carregar = true;
                }
                dataUltimaAtualizacao = Data.getDataAtual();
            }
        }

        if( carregar ) {
            this.empresaCarregada = this.getEmpresa( getCodigoEmpresa() );
        }

        return empresaCarregada;
    }


    private  Empresa getEmpresa(Long codigoEmpresa) {
        try {
            return LoadManager.getInstance(Empresa.class)
                    .setLazyMode(true)
                    .addProperties(new HQLProperties(Empresa.class).getProperties())
                    .addProperties(new HQLProperties(EmpresaMaterial.class,Empresa.PROP_EMPRESA_MATERIAL).getProperties())
                    .addProperties(new HQLProperties(Cidade.class,Empresa.PROP_CIDADE).getProperties())
                    .addProperties(new HQLProperties(Estado.class,VOUtils.montarPath(Empresa.PROP_CIDADE, Cidade.PROP_ESTADO)).getProperties())
                    .setId(codigoEmpresa)
                    .start()
                    .getVO();
        } catch (Exception e) {
            log.fatal(e.getMessage(), e);
        }

        return null;
    }

    /* (non-Javadoc)
     * @see java.lang.Object#toString()
     */
    @Override
    public String toString() {
        String retorno = "Usurio: " + this.getUsuario().getCodigo() + " - " + this.getUsuario().getNome() +
                "\nEmpresa: " + this.getEmpresa().getCodigo() + " - " + this.getEmpresa().getDescricao() +
                "\nLocale: " + this.getLocale() +
                "\nDevelopment Session: " + this.isDevelopmentSession();
        return retorno;
    }

    /**
     * Retorna <code>true</code> para quando a sessao atual for de desenvolvimento.
     * @return <code>true</code> paa sesso de desenvolvimento.
     */
    public boolean isDevelopmentSession() {
        return developmentSession;
    }

    /**
     * Define se a sesso atual , ou no, de desenvolvimento.
     * @param <code>true</code> para definir a sesso como de desenvolvimento.
     */
    public void setDevelopmentSession(boolean developmentSession) {
        this.developmentSession = developmentSession;
    }

    /**
     * Retorna o <code>Locale</code> o qual a sesso oi iniciada.
     * @return 
     */
    public Locale getLocale() {
        return locale;
    }

    @Override
    public ReportSessaoAplicacao getReportSessaoAplicacao() {
        final Empresa empresa = getEmpresa();
        
        return new ReportSessaoAplicacaoImp(empresa, usuario);
    }

    public String getName() {
        return getUsuario().getLogin();
    }

    public static void logout(){
        instance = null;
    }

    @Override
    public Map<String, String> getIpMac() {
        if (mapIpMac == null) {
            mapIpMac = new HashMap<String, String>();
            try {
                Enumeration<NetworkInterface> nis = NetworkInterface.getNetworkInterfaces();
                while (nis.hasMoreElements()) {
                    NetworkInterface ni = nis.nextElement();
                    Enumeration<InetAddress> ias = ni.getInetAddresses();

                    while (ias.hasMoreElements()) {
                        InetAddress ia = ias.nextElement();
                        if ((!ni.getName().equals("lo") && ni.isUp())) {
                            if (ia instanceof Inet4Address) {
                                String mac = "";
                                String ip = "IP_ND";
                                byte[] bytes = ni.getHardwareAddress();
                                if (bytes != null) {
                                    for (int k = 0; k < bytes.length; k++) {
                                        mac += String.format("%02X%s", bytes[k], (k < bytes.length - 1) ? "-" : "").toString();
                                    }
                                }
                                ip = ia.getHostAddress() + "/" + ia.getHostName();

                                mapIpMac.put(ip, mac);
                            }
                        }
                    }
                }
            } catch (SocketException e) {
                log.fatal(e.getMessage(), e);
            }
        }
        return mapIpMac;
    }
}
