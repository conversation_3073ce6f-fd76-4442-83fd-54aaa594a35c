package br.com.ksisolucoes.util.esus;

import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.util.LoadManagerUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.cds.EsusFichaAtendDomiciliarItem;
import br.com.ksisolucoes.vo.cadsus.cds.EsusFichaAtendDomiciliarItemProcedimento;
import br.com.ksisolucoes.vo.esus.ProcedimentoEloEsus;
import br.com.ksisolucoes.vo.esus.ProcedimentoEsus;
import br.com.ksisolucoes.vo.esus.dto.EsusValidacoesFichasDTOParam;
import br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoFormaOrganizacao;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoFormaOrganizacaoPK;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import static br.com.ksisolucoes.util.LoadManagerUtils.mockLoadManager;
import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.powermock.api.mockito.PowerMockito.mockStatic;

@RunWith(PowerMockRunner.class)
@PrepareForTest({LoadManager.class})
public class EsusValidacoesFichaAtendimentoDomiciliarItemProcedimentoHelperTest {

    @Mock
    private LoadManager loadManagerProcedimento;

    @Mock
    private LoadManager loadManagerProcedimentoEloEsus;

    EsusValidacoesFichasDTOParam validationParam = new EsusValidacoesFichasDTOParam();

    @Before
    public void setup() throws Exception {
        mockStatic(LoadManager.class);

        mockLoadManager(loadManagerProcedimento, Procedimento.class);
        mockLoadManager(loadManagerProcedimentoEloEsus, ProcedimentoEloEsus.class);

        validationParam = new EsusValidacoesFichasDTOParam();
        validationParam.setRetorno(EsusValidacoesFichasDTOParam.Retorno.INCONSISTENCIA);
        validationParam.setEsusFichaAtendimentoDomiciliarItemProcedimento(new EsusFichaAtendDomiciliarItemProcedimento());
        validationParam.getEsusFichaAtendimentoDomiciliarItemProcedimento().setEsusFichaAtendDomiciliarItem(new EsusFichaAtendDomiciliarItem());
    }

    @Test
    public void validateTipoAtendimentoProcedimento() throws ValidacaoException {
        validationParam.getEsusFichaAtendimentoDomiciliarItemProcedimento().getEsusFichaAtendDomiciliarItem().setTipoAtendimento((Long) EsusFichaAtendDomiciliarItem.TipoAtendimento.VISITA_DOMICILIAR_POS_OBITO.value());
        validationParam.getEsusFichaAtendimentoDomiciliarItemProcedimento().setProcedimentoEsus(new ProcedimentoEsus());
        assertEquals("should not set Tipo Atendimento Procedimento pós-obito", "Não pode ser inserido Procedimento e-SUS para Tipo de Atendimento: Visita domiciliar pós-óbito.</br>", EsusValidacoesFichaAtendimentoDomiciliarItemProcedimentoHelper.validateTipoAtendimentoProcedimento(validationParam));

        validationParam.getEsusFichaAtendimentoDomiciliarItemProcedimento().setProcedimentoEsus(null);
        validationParam.getEsusFichaAtendimentoDomiciliarItemProcedimento().setProcedimento(new Procedimento());
        assertEquals("should not set Tipo Atendimento Procedimento pós-obito", "Não pode ser inserido Procedimento para Tipo de Atendimento: Visita domiciliar pós-óbito.</br>", EsusValidacoesFichaAtendimentoDomiciliarItemProcedimentoHelper.validateTipoAtendimentoProcedimento(validationParam));

        validationParam.getEsusFichaAtendimentoDomiciliarItemProcedimento().setProcedimento(null);
        assertEquals("should validate Tipo Atendimento Procedimento pós-obito", "", EsusValidacoesFichaAtendimentoDomiciliarItemProcedimentoHelper.validateTipoAtendimentoProcedimento(validationParam));
    }

    @Test
    public void validateProcedures() {
        Procedimento procedimento = new Procedimento();

        UsuarioCadsus usuarioCadsus = new UsuarioCadsus();
        usuarioCadsus.setCodigo(1L);
        usuarioCadsus.setNome("teste");
        validationParam.setEsusFichaAtendimentoDomiciliarItem(new EsusFichaAtendDomiciliarItem());
        validationParam.getEsusFichaAtendimentoDomiciliarItem().setUsuarioCadsus(usuarioCadsus);

        when(loadManagerProcedimento.getVO()).thenReturn(procedimento);
        when(loadManagerProcedimentoEloEsus.exists()).thenReturn(false);

        validationParam.getEsusFichaAtendimentoDomiciliarItemProcedimento().setProcedimento(new Procedimento());

        assertEquals("should validate Procedures SIGTAP", "Só é permitido inserir procedimentos SIGTAP cujo grupo é igual a Ações de promoção e prevenção em saúde, Procedimentos com finalidade diagnóstica, Procedimentos clínicos ou Procedimentos cirúrgicos."  + usuarioCadsus.getDescricaoFormatado()  + "</br>", EsusValidacoesFichaAtendimentoDomiciliarItemProcedimentoHelper.validateProcedures(validationParam));

        procedimento.setProcedimentoFormaOrganizacao(new ProcedimentoFormaOrganizacao());
        procedimento.getProcedimentoFormaOrganizacao().setId(new ProcedimentoFormaOrganizacaoPK());
        procedimento.getProcedimentoFormaOrganizacao().getId().setCodigoProcedimentoGrupo(5L);
        when(loadManagerProcedimento.getVO()).thenReturn(procedimento);

        assertEquals("should validate Procedures SIGTAP", "Só é permitido inserir procedimentos SIGTAP cujo grupo é igual a Ações de promoção e prevenção em saúde, Procedimentos com finalidade diagnóstica, Procedimentos clínicos ou Procedimentos cirúrgicos." + usuarioCadsus.getDescricaoFormatado()  + "</br>", EsusValidacoesFichaAtendimentoDomiciliarItemProcedimentoHelper.validateProcedures(validationParam));

        procedimento.getProcedimentoFormaOrganizacao().getId().setCodigoProcedimentoGrupo(4L);
        procedimento.setCodigo(301050104L);
        when(loadManagerProcedimento.getVO()).thenReturn(procedimento);

        assertEquals("should not set Procedures pós-obito", "Não é permitido inserir o procedimentos Visita Domiciliar Pós-Óbito.</br>", EsusValidacoesFichaAtendimentoDomiciliarItemProcedimentoHelper.validateProcedures(validationParam));

        procedimento.setCodigo(null);
        procedimento.setReferencia("301050104");
        when(loadManagerProcedimento.getVO()).thenReturn(procedimento);

        assertEquals("should not set Procedures pós-obito", "Não é permitido inserir o procedimentos Visita Domiciliar Pós-Óbito.</br>", EsusValidacoesFichaAtendimentoDomiciliarItemProcedimentoHelper.validateProcedures(validationParam));

        procedimento.setReferencia(null);
        when(loadManagerProcedimento.getVO()).thenReturn(procedimento);

        assertEquals("should validate Procedures", "", EsusValidacoesFichaAtendimentoDomiciliarItemProcedimentoHelper.validateProcedures(validationParam));
    }
}