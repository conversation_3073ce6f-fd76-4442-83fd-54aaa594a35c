package br.com.ksisolucoes.util.esus;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.cds.EsusFichaProcedimento;
import br.com.ksisolucoes.vo.cadsus.cds.EsusFichaVacina;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.esus.CboFichaEsusItem;
import br.com.ksisolucoes.vo.esus.dto.EsusValidacoesFichasDTOParam;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetencia;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import br.com.ksisolucoes.vo.vacina.*;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;
import static org.powermock.api.mockito.PowerMockito.mockStatic;

@RunWith(PowerMockRunner.class)
@PrepareForTest({LoadManager.class, EsusValidacoesFichaPadraoHelper.class})
public class EsusValidacoesFichaVacinaHelperTest {

    @Mock
    private LoadManager loadManager;

    EsusValidacoesFichasDTOParam validationParam;

    @Before
    public void setup() throws ValidacaoException {
        mockStatic(LoadManager.class);
        mockStatic(EsusValidacoesFichaPadraoHelper.class);

        when(LoadManager.getInstance(any())).thenReturn(loadManager);
        when(EsusValidacoesFichaPadraoHelper.validateProfessional(any())).thenReturn("");
        when(loadManager.addProperty(anyString())).thenReturn(loadManager);
        when(loadManager.addParameter(any())).thenReturn(loadManager);
        when(loadManager.startLeitura()).thenReturn(loadManager);
        when(loadManager.start()).thenReturn(loadManager);

        this.validationParam = new EsusValidacoesFichasDTOParam();
        this.validationParam.setEsusFichaProcedimento(new EsusFichaProcedimento());
        this.validationParam.setRetorno(EsusValidacoesFichasDTOParam.Retorno.INCONSISTENCIA);
        this.validationParam.setVacinaAplicacao(new VacinaAplicacao());
        this.validationParam.getVacinaAplicacao().setVacinaCalendario(new VacinaCalendario());
        this.validationParam.getVacinaAplicacao().getVacinaCalendario().setCalendario(new Calendario());
        this.validationParam.setEsusFichaVacina(new EsusFichaVacina());
        this.validationParam.getEsusFichaVacina().setVacinaAplicacao(new VacinaAplicacao());
        this.validationParam.setProfissional(new Profissional());
        this.validationParam.getProfissional().setNome("Professional Name");
        this.validationParam.getEsusFichaVacina().setUsuarioCadsus(new UsuarioCadsus());
        this.validationParam.getEsusFichaVacina().getVacinaAplicacao().setTipoVacina(new TipoVacina());
        this.validationParam.getEsusFichaVacina().getVacinaAplicacao().setDose((Long) VacinaCalendario.Doses.DOSE_UNICA.value());
        this.validationParam.getEsusFichaVacina().getVacinaAplicacao().setVacinaCalendario(new VacinaCalendario());
        this.validationParam.getEsusFichaVacina().getVacinaAplicacao().getVacinaCalendario().setCalendario(new Calendario());
        this.validationParam.getEsusFichaVacina().getVacinaAplicacao().getVacinaCalendario().setDose((Long) VacinaCalendario.Doses.DOSE_UNICA.value());
    }

    @Test
    public void shouldValidateEstrategiaEsus() throws ValidacaoException {
        assertEquals("should validate Estrategia Esus", "Estratégia e-sus não definida.</br>", EsusValidacoesFichaVacinaHelper.validateEstrategiaEsus(validationParam));

        this.validationParam.getVacinaAplicacao().getVacinaCalendario().getCalendario().setEstrategiaEsus(Calendario.EstrategiaEsusPniRnds.ESPECIAL.getCodigoEsusPni());
        assertEquals("should validate Estrategia Esus", "", EsusValidacoesFichaVacinaHelper.validateEstrategiaEsus(validationParam));
    }

    @Test
    public void shouldValidateEndDate() throws Exception {
        assertEquals("should validate End Date", "Data/hora final não foi definida.</br>", EsusValidacoesFichaVacinaHelper.validateEndDate(validationParam));

        this.validationParam.getEsusFichaVacina().getVacinaAplicacao().setDataAplicacaoFim(DataUtil.stringToDate("13/01/2021"));
        assertEquals("should validate End Date", "", EsusValidacoesFichaVacinaHelper.validateEndDate(validationParam));
    }

    @Test
    public void shouldValidateCboProfissional() {
        assertEquals("should validate CBO Professional", "Professional Name com CBO não definido.</br>", EsusValidacoesFichaVacinaHelper.validateCboProfissional(validationParam));

        this.validationParam.setTabelaCbo(new TabelaCbo());
        when(loadManager.exists()).thenReturn(false);
        assertEquals("should validate CBO Professional", "CBO null do profissional Professional Name não permitido.</br>", EsusValidacoesFichaVacinaHelper.validateCboProfissional(validationParam));

        when(loadManager.exists()).thenReturn(true);
        assertEquals("should validate CBO Professional", "", EsusValidacoesFichaVacinaHelper.validateCboProfissional(validationParam));

        this.validationParam.setCboFichaEsusItemList(new ArrayList<>());
        this.validationParam.getCboFichaEsusItemList().add(new CboFichaEsusItem());
        this.validationParam.getTabelaCbo().setCbo("123456");
        assertEquals("should validate CBO Professional", "CBO null do profissional Professional Name não permitido.</br>", EsusValidacoesFichaVacinaHelper.validateCboProfissional(validationParam));

        this.validationParam.getCboFichaEsusItemList().get(0).setTabelaCbo(new TabelaCbo());
        this.validationParam.getCboFichaEsusItemList().get(0).getTabelaCbo().setCbo("123456");
        assertEquals("should validate CBO Professional", "", EsusValidacoesFichaVacinaHelper.validateCboProfissional(validationParam));
    }

    @Test
    public void shouldValidateBirthdate() throws Exception {
        assertEquals("should validate BirthDate", "Ficha e-SUS: Data de Nascimento é obrigatória.</br>", EsusValidacoesFichaVacinaHelper.validateBirthdate(validationParam));

        this.validationParam.getEsusFichaVacina().getUsuarioCadsus().setDataNascimento(DataUtil.stringToDate("13/01/2000"));
        this.validationParam.getVacinaAplicacao().setDataAplicacao(DataUtil.stringToDate("12/01/2000"));
        assertEquals("should validate BirthDate", "Ficha e-SUS: Data de Nascimento não pode ser posterior à Data de Atendimento.</br>", EsusValidacoesFichaVacinaHelper.validateBirthdate(validationParam));

        this.validationParam.getVacinaAplicacao().setDataAplicacao(DataUtil.stringToDate("14/01/2921"));
        assertEquals("should validate BirthDate", "Ficha e-SUS: Data de Nascimento não pode ser anterior a 130 anos a partir da Data de Atendimento.</br>", EsusValidacoesFichaVacinaHelper.validateBirthdate(validationParam));

        this.validationParam.getVacinaAplicacao().setDataAplicacao(DataUtil.stringToDate("14/01/2021"));
        assertEquals("should validate BirthDate", "", EsusValidacoesFichaVacinaHelper.validateBirthdate(validationParam));
    }

    @Test
    public void shouldValidateServiceLocation() {
        assertEquals("should validate Service Location", "Ficha e-SUS: Local do Atendimento deve ser informado.</br>", EsusValidacoesFichaVacinaHelper.validateServiceLocation(validationParam));

        this.validationParam.getEsusFichaVacina().setLocalAtendimento(999L);
        assertEquals("should validate Service Location", "Ficha e-SUS: Local do Atendimento inválido: 999.</br>", EsusValidacoesFichaVacinaHelper.validateServiceLocation(validationParam));

        this.validationParam.getEsusFichaVacina().setLocalAtendimento(VacinaAplicacao.LocalAtendimento.UBS.value());
        assertEquals("should validate Service Location", "", EsusValidacoesFichaVacinaHelper.validateServiceLocation(validationParam));
    }

    @Test
    public void shouldValidateComunicanteHanseniase() {
        assertEquals("should validate Comunicante Hanseniase", "", EsusValidacoesFichaVacinaHelper.validateComunicanteHanseniase(validationParam));

        this.validationParam.getEsusFichaVacina().getVacinaAplicacao().setComunicanteHanseniase(RepositoryComponentDefault.SIM_LONG);
        assertEquals("should validate Comunicante Hanseniase", "Ficha e-SUS: O campo Comunicante de Hanseníase somente pode ser preenchido se for registrada uma vacinação com o imunobiológico 15 - BCG</br>", EsusValidacoesFichaVacinaHelper.validateComunicanteHanseniase(validationParam));

        this.validationParam.getEsusFichaVacina().getVacinaAplicacao().getTipoVacina().setTipoEsus(TipoVacina.TipoEsus.VACINA_DIFTERIA_E_TETANO_INFANTIL.value());
        assertEquals("should validate Comunicante Hanseniase", "Ficha e-SUS: O campo Comunicante de Hanseníase somente pode ser preenchido se for registrada uma vacinação com o imunobiológico 15 - BCG</br>", EsusValidacoesFichaVacinaHelper.validateComunicanteHanseniase(validationParam));

        this.validationParam.getEsusFichaVacina().getVacinaAplicacao().getTipoVacina().setTipoEsus(TipoVacina.TipoEsus.VACINA_BCG.value());
        assertEquals("should validate Comunicante Hanseniase", "", EsusValidacoesFichaVacinaHelper.validateComunicanteHanseniase(validationParam));
    }

    @Test
    public void shouldValidateImunibiologico() {
        assertEquals("should validate Imunibiologico", "Ficha e-SUS: Defina o código do imunobiológico</br>", EsusValidacoesFichaVacinaHelper.validateImunibiologico(validationParam));

        this.validationParam.getEsusFichaVacina().getVacinaAplicacao().getTipoVacina().setTipoEsus(TipoVacina.TipoEsus.VACINA_BCG.value());
        assertEquals("should validate Imunibiologico", "", EsusValidacoesFichaVacinaHelper.validateImunibiologico(validationParam));
    }

    @Test
    public void shouldValidateBatch() {
        assertEquals("should validate Batch", "Ficha e-SUS: Lote é obrigatório</br>", EsusValidacoesFichaVacinaHelper.validateLote(validationParam));

        this.validationParam.getEsusFichaVacina().getVacinaAplicacao().setLote("123456");
        assertEquals("should validate Batch", "", EsusValidacoesFichaVacinaHelper.validateLote(validationParam));
    }

    @Test
    public void shouldValidateGender() {
        assertEquals("should validate Gender", "Ficha e-SUS: Sexo do paciente é obrigatório</br>", EsusValidacoesFichaVacinaHelper.validateGender(validationParam));

        this.validationParam.getEsusFichaVacina().setSexo(ProcedimentoCompetencia.SEXO_MASCULINO);
        assertEquals("should validate Gender", "", EsusValidacoesFichaVacinaHelper.validateGender(validationParam));
    }

    @Test
    public void shouldValidateManufacturer() {
        assertEquals("should validate Manufacturer", "Ficha e-SUS: Fabricante E-SUS é obrigatório</br>", EsusValidacoesFichaVacinaHelper.validateFabricante(validationParam));

        this.validationParam.getEsusFichaVacina().getVacinaAplicacao().setProdutoVacina(new ProdutoVacina());
        assertEquals("should validate Manufacturer", "Ficha e-SUS: Fabricante E-SUS é obrigatório</br>", EsusValidacoesFichaVacinaHelper.validateFabricante(validationParam));

        this.validationParam.getEsusFichaVacina().getVacinaAplicacao().getProdutoVacina().setProduto(new Produto());
        assertEquals("should validate Manufacturer", "Ficha e-SUS: Fabricante E-SUS é obrigatório</br>", EsusValidacoesFichaVacinaHelper.validateFabricante(validationParam));

        this.validationParam.getEsusFichaVacina().getVacinaAplicacao().getProdutoVacina().getProduto().setFabricanteEsus("SINOVAC/BUTANTAN");
        assertEquals("should validate Manufacturer", "", EsusValidacoesFichaVacinaHelper.validateFabricante(validationParam));
    }

    @Test
    public void shouldValidateBCG() {
        this.validationParam.getEsusFichaVacina().getVacinaAplicacao().getTipoVacina().setTipoEsus(TipoVacina.TipoEsus.VACINA_BCG.value());
        this.validationParam.getEsusFichaVacina().getVacinaAplicacao().setVacinaCalendario(null);
        assertEquals("should validate BCG", "Não é possível registrar o imunobiológico VACINA BCG sem calendário cadastrado.</br>", EsusValidacoesFichaVacinaHelper.validateVaccines(validationParam));

        this.validationParam.getEsusFichaVacina().getVacinaAplicacao().setVacinaCalendario(new VacinaCalendario());
        this.validationParam.getEsusFichaVacina().getVacinaAplicacao().getVacinaCalendario().setCalendario(new Calendario());
        this.validationParam.getEsusFichaVacina().getVacinaAplicacao().setDose(null);
        this.validationParam.getEsusFichaVacina().getVacinaAplicacao().getVacinaCalendario().setDose(null);

        assertEquals("should validate BCG", "Ficha e-SUS: Não é possível registrar o imunobiológico (15) - VACINA BCG com a estratégia  e dose .</br>", EsusValidacoesFichaVacinaHelper.validateVaccines(validationParam));

        this.validationParam.getEsusFichaVacina().getVacinaAplicacao().getVacinaCalendario().getCalendario().setEstrategiaEsus(Calendario.EstrategiaEsusPniRnds.ESPECIAL.getCodigoEsusPni());
        assertEquals("should validate BCG", "Ficha e-SUS: Não é possível registrar o imunobiológico (15) - VACINA BCG com a estratégia Especial e dose .</br>", EsusValidacoesFichaVacinaHelper.validateVaccines(validationParam));

        this.validationParam.getEsusFichaVacina().getVacinaAplicacao().getVacinaCalendario().getCalendario().setEstrategiaEsus(Calendario.EstrategiaEsusPniRnds.INTENSIFICACAO.getCodigoEsusPni());
        assertEquals("should validate BCG", "Ficha e-SUS: Não é possível registrar o imunobiológico (15) - VACINA BCG com a estratégia Intensificação e dose .</br>", EsusValidacoesFichaVacinaHelper.validateVaccines(validationParam));

        this.validationParam.getEsusFichaVacina().getVacinaAplicacao().setDose((Long) VacinaCalendario.Doses.DOSE_UNICA.value());
        assertEquals("should validate BCG", "", EsusValidacoesFichaVacinaHelper.validateVaccines(validationParam));

        this.validationParam.getEsusFichaVacina().getVacinaAplicacao().setDose((Long) VacinaCalendario.Doses.DOSE_UNICA.value());
        assertEquals("should validate BCG", "", EsusValidacoesFichaVacinaHelper.validateVaccines(validationParam));

    }

    @Test
    public void shouldCallValidate() throws Exception {
        when(loadManager.exists()).thenReturn(true);

        this.validationParam.setCboFichaEsusItemList(new ArrayList<>());
        this.validationParam.getCboFichaEsusItemList().add(new CboFichaEsusItem());
        this.validationParam.setTabelaCbo(new TabelaCbo());
        this.validationParam.getTabelaCbo().setCbo("123456");
        this.validationParam.getCboFichaEsusItemList().get(0).setTabelaCbo(new TabelaCbo());
        this.validationParam.getCboFichaEsusItemList().get(0).getTabelaCbo().setCbo("123456");
        if (this.validationParam.getEsusFichaVacina().getUsuarioCadsus() == null)
            this.validationParam.setUsuarioCadsus(new UsuarioCadsus());
        this.validationParam.getEsusFichaVacina().getUsuarioCadsus().setDataNascimento(DataUtil.stringToDate("13/01/2000"));
        this.validationParam.getEsusFichaVacina().getUsuarioCadsus().setCpf("76867124003");
        this.validationParam.getEsusFichaVacina().getUsuarioCadsus().cnsCarregado = "76867124003";
        this.validationParam.getVacinaAplicacao().setDataAplicacao(DataUtil.stringToDate("12/01/2021"));
        this.validationParam.getEsusFichaVacina().setLocalAtendimento(VacinaAplicacao.LocalAtendimento.UBS.value());
        this.validationParam.getEsusFichaVacina().getVacinaAplicacao().getTipoVacina().setTipoEsus(TipoVacina.TipoEsus.VACINA_BCG.value());
        this.validationParam.getEsusFichaVacina().getVacinaAplicacao().getVacinaCalendario().getCalendario().setEstrategiaEsus(Calendario.EstrategiaEsusPniRnds.INTENSIFICACAO.getCodigoEsusPni());
        this.validationParam.getEsusFichaVacina().getVacinaAplicacao().setDose((Long) VacinaCalendario.Doses.DOSE_UNICA.value());
        this.validationParam.getEsusFichaVacina().setSexo(ProcedimentoCompetencia.SEXO_MASCULINO);
        this.validationParam.getEsusFichaVacina().getVacinaAplicacao().setLote("123456");
        this.validationParam.getEsusFichaVacina().getVacinaAplicacao().setProdutoVacina(new ProdutoVacina());
        this.validationParam.getEsusFichaVacina().getVacinaAplicacao().getProdutoVacina().setProduto(new Produto());
        this.validationParam.getEsusFichaVacina().getVacinaAplicacao().getProdutoVacina().getProduto().setFabricanteEsus("Description");
        this.validationParam.getEsusFichaVacina().getVacinaAplicacao().setDataAplicacaoFim(DataUtil.stringToDate("13/01/2021"));

        assertEquals("should call validate", "", EsusValidacoesFichaVacinaHelper.validate(validationParam));
    }
}