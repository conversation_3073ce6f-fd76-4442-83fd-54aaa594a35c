package br.com.ksisolucoes.util.esus;


import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.atividadegrupo.AtividadeGrupoProfissional;
import br.com.ksisolucoes.vo.basico.Cidade;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.EquipeProfissional;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.esus.dto.EsusFichaAtividadeGrupoDTO;
import br.com.ksisolucoes.vo.esus.dto.EsusValidacoesFichasDTOParam;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;
import static org.powermock.api.mockito.PowerMockito.mockStatic;

@RunWith(PowerMockRunner.class)
@PrepareForTest({LoadManager.class})
public class EsusValidacoesFichaAtividadeGrupoProfissionalHelperTest {

    @Mock
    private LoadManager loadManager;

    EsusValidacoesFichasDTOParam validationParam = new EsusValidacoesFichasDTOParam();

    @Before
    public void setup() {
        mockStatic(LoadManager.class);

        when(LoadManager.getInstance(any())).thenReturn(loadManager);
        when(loadManager.addProperty(anyString())).thenReturn(loadManager);
        when(loadManager.addParameter(any())).thenReturn(loadManager);
        when(loadManager.startLeitura()).thenReturn(loadManager);
        when(loadManager.start()).thenReturn(loadManager);
        when(loadManager.setMaxResults(any())).thenReturn(loadManager);

        validationParam.setRetorno(EsusValidacoesFichasDTOParam.Retorno.INCONSISTENCIA);
        validationParam.setAtividadeGrupoDTO(new EsusFichaAtividadeGrupoDTO());
        validationParam.getAtividadeGrupoDTO().setAtividadeGrupoProfissional(new AtividadeGrupoProfissional());
        validationParam.getAtividadeGrupoDTO().getAtividadeGrupoProfissional().setProfissional(new Profissional());
    }

    @Test
    public void shouldValidateProfessional() {
        validationParam.getAtividadeGrupoDTO().getAtividadeGrupoProfissional().getProfissional().setCodigoCns(null);
        Assert.assertTrue("should validate professional cns", EsusValidacoesFichaAtividadeGrupoProfissionalHelper.validateProfessional(validationParam).contains(" sem CNS definido."));

        validationParam.getAtividadeGrupoDTO().getAtividadeGrupoProfissional().getProfissional().setCodigoCns("123123312");
        Assert.assertFalse("should validate professional cns", EsusValidacoesFichaAtividadeGrupoProfissionalHelper.validateProfessional(validationParam).contains(" sem CNS definido."));

        validationParam.getAtividadeGrupoDTO().getAtividadeGrupoProfissional().getProfissional().setCidade(null);
        Assert.assertTrue("should validate professional city", EsusValidacoesFichaAtividadeGrupoProfissionalHelper.validateProfessional(validationParam).contains(" sem Cidade definida."));

        validationParam.getAtividadeGrupoDTO().getAtividadeGrupoProfissional().getProfissional().setCidade(new Cidade());
        Assert.assertFalse("should validate professional city", EsusValidacoesFichaAtividadeGrupoProfissionalHelper.validateProfessional(validationParam).contains(" sem Cidade definida."));
    }

    @Test
    public void shouldValidateProfessionalTeam() {
        List<Object> equipeProfissionalList = EsusValidacoesFichaAtividadeGrupoProfissionalHelperTestHelper.getEquipeProfissionalList();
        equipeProfissionalList.add(equipeProfissionalList.get(0));
        when(loadManager.getList()).thenReturn(equipeProfissionalList);

        validationParam.getAtividadeGrupoDTO().getAtividadeGrupoProfissional().setProfissional(new Profissional());
        validationParam.getAtividadeGrupoDTO().getAtividadeGrupoProfissional().getProfissional().setNome("Professional");
        validationParam.setEmpresa(new Empresa());
        validationParam.getEmpresa().setDescricao("Description");

        Assert.assertTrue("should validate professional in more than one team", EsusValidacoesFichaAtividadeGrupoProfissionalHelper.validateProfessionalTeam(validationParam).contains("Professional está vinculado em mais de uma equipe. Estabelecimento: Description"));

        EquipeProfissional equipeProfissional = EsusValidacoesFichaAtividadeGrupoProfissionalHelperTestHelper.getEquipeProfissional();
        equipeProfissional.getEquipe().setEquipeCnes("12345");

        when(loadManager.getList()).thenReturn(Collections.singletonList(equipeProfissional));
        Assert.assertTrue("should validate professional's team have a valid CNES code'", EsusValidacoesFichaAtividadeGrupoProfissionalHelper.validateProfessionalTeam(validationParam).contains("O Código INE da equipe Ref da área de Description pertencente ao estabelecimento Description é inválido, devendo este possuir 10 dígitos.</br>"));
    }

    @Test
    public void shouldValidateProfessionalCBO() {
        Assert.assertTrue("should validate professional's CBO", EsusValidacoesFichaAtividadeGrupoProfissionalHelper.validateProfessionalCBO(validationParam).contains("CBO do profissional null não informado.</br>"));

        when(loadManager.getVO()).thenReturn(null);
        validationParam.setTabelaCbo(new TabelaCbo());
        Assert.assertTrue("should validate if professional's CBO is valid", EsusValidacoesFichaAtividadeGrupoProfissionalHelper.validateProfessionalCBO(validationParam).contains("CBO null do profissional null não permitido.</br>"));

        when(loadManager.getVO()).thenReturn(EsusValidacoesFichaAtividadeGrupoProfissionalHelperTestHelper.getCboFichaEsusItem());
        validationParam.getTabelaCbo().setCbo("123");
        validationParam.setCboFichaEsusItemList(Collections.singletonList(EsusValidacoesFichaAtividadeGrupoProfissionalHelperTestHelper.getCboFichaEsusItem()));
        Assert.assertTrue("should validate if professional's CBO is valid", EsusValidacoesFichaAtividadeGrupoProfissionalHelper.validateProfessionalCBO(validationParam).contains("CBO null do profissional null não permitido.</br>"));

        validationParam.getTabelaCbo().setCbo("123456");
        Assert.assertFalse("should validate if professional's CBO is valid", EsusValidacoesFichaAtividadeGrupoProfissionalHelper.validateProfessionalCBO(validationParam).contains("CBO null do profissional null não permitido.</br>"));
    }

    @Test
    public void shouldRunValidate() throws ValidacaoException {
        when(loadManager.getVO()).thenReturn(EsusValidacoesFichaAtividadeGrupoProfissionalHelperTestHelper.getCboFichaEsusItem());

        validationParam.getAtividadeGrupoDTO().getAtividadeGrupoProfissional().getProfissional().setCodigoCns("123123312");
        validationParam.getAtividadeGrupoDTO().getAtividadeGrupoProfissional().getProfissional().setCidade(new Cidade());
        validationParam.setTabelaCbo(new TabelaCbo());
        validationParam.getTabelaCbo().setCbo("123456");
        validationParam.setCboFichaEsusItemList(Collections.singletonList(EsusValidacoesFichaAtividadeGrupoProfissionalHelperTestHelper.getCboFichaEsusItem()));

        Assert.assertEquals("should validate professional's CBO", "", EsusValidacoesFichaAtividadeGrupoProfissionalHelper.validate(validationParam));

    }
}