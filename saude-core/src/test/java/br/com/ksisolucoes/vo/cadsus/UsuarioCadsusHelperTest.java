package br.com.ksisolucoes.vo.cadsus;

import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.junit.Before;
import org.junit.Test;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;

public class UsuarioCadsusHelperTest {
    UsuarioCadsus usuarioCadsus;

    @Before
    public void setUp() throws Exception {
        usuarioCadsus = new UsuarioCadsus();
    }

    @Test
    public void getPatientPhoneShouldReturnAPhoneGivenItsPriority() {

        usuarioCadsus.setTelefone4("**********");
        assertEquals("telephone4 should have the fifth priority", "**********", UsuarioCadsusHelper.getPatientPhone(usuarioCadsus));

        usuarioCadsus.setTelefone3("**********");
        assertEquals("telephone3 should have the fourth priority", "**********", UsuarioCadsusHelper.getPatientPhone(usuarioCadsus));

        usuarioCadsus.setTelefone2("**********");
        assertEquals("telephone2 should have the third priority", "**********", UsuarioCadsusHelper.getPatientPhone(usuarioCadsus));

        usuarioCadsus.setCelular("**********");
        assertEquals("celular should have the second priority", "**********", UsuarioCadsusHelper.getPatientPhone(usuarioCadsus));

        usuarioCadsus.setTelefone("**********");
        assertEquals("telephone should have the first priority", "**********", UsuarioCadsusHelper.getPatientPhone(usuarioCadsus));

        usuarioCadsus.setTelefone4(null);
        usuarioCadsus.setTelefone3(null);
        usuarioCadsus.setTelefone2(null);
        usuarioCadsus.setCelular(null);

        usuarioCadsus.setTelefone(null);
        assertNull("telephone should return null when telephone is invalid", UsuarioCadsusHelper.getPatientPhone(usuarioCadsus));

        usuarioCadsus.setTelefone("telephone");
        assertNull("telephone should return null when telephone is invalid", UsuarioCadsusHelper.getPatientPhone(usuarioCadsus));

        usuarioCadsus.setTelefone("123456789");
        assertNull("telephone should return null when telephone is invalid", UsuarioCadsusHelper.getPatientPhone(usuarioCadsus));

        usuarioCadsus.setTelefone("**********12");
        assertNull("telephone should return null when telephone is invalid", UsuarioCadsusHelper.getPatientPhone(usuarioCadsus));

        usuarioCadsus.setTelefone("1 23456789");
        assertNull("telephone should return null when telephone is invalid", UsuarioCadsusHelper.getPatientPhone(usuarioCadsus));

        usuarioCadsus.setTelefone("**********");
        assertEquals("telephone should be valid", "**********", UsuarioCadsusHelper.getPatientPhone(usuarioCadsus));

        usuarioCadsus.setTelefone("(12)3456-7890");
        assertEquals("telephone should be valid", "**********", UsuarioCadsusHelper.getPatientPhone(usuarioCadsus));
    }

    @Test(expected = ValidacaoException.class)
    public void shouldThrowValidacaoExceptionForBrazilianWithoutPhone() throws ValidacaoException {
        usuarioCadsus.setNacionalidade(UsuarioCadsus.Nacionalidade.BRASILEIRO.value());

        UsuarioCadsusHelper.validatePhoneForBrazilianOrNaturalized(usuarioCadsus);
    }

    @Test
    public void shouldNotThrowValidacaoExceptionForBrazilianWithPhone() throws ValidacaoException {
        usuarioCadsus.setNacionalidade(UsuarioCadsus.Nacionalidade.BRASILEIRO.value());
        usuarioCadsus.setTelefone("99999999999");

        UsuarioCadsusHelper.validatePhoneForBrazilianOrNaturalized(usuarioCadsus);
    }

    @Test
    public void shouldNotThrowValidacaoExceptionForBrazilianWithPhone2() throws ValidacaoException {
        usuarioCadsus.setNacionalidade(UsuarioCadsus.Nacionalidade.BRASILEIRO.value());
        usuarioCadsus.setTelefone2("99999999999");

        UsuarioCadsusHelper.validatePhoneForBrazilianOrNaturalized(usuarioCadsus);
    }

    @Test
    public void shouldNotThrowValidacaoExceptionForBrazilianWithPhone3() throws ValidacaoException {
        usuarioCadsus.setNacionalidade(UsuarioCadsus.Nacionalidade.BRASILEIRO.value());
        usuarioCadsus.setTelefone3("99999999999");

        UsuarioCadsusHelper.validatePhoneForBrazilianOrNaturalized(usuarioCadsus);
    }

    @Test
    public void shouldNotThrowValidacaoExceptionForBrazilianWithPhone4() throws ValidacaoException {
        usuarioCadsus.setNacionalidade(UsuarioCadsus.Nacionalidade.BRASILEIRO.value());
        usuarioCadsus.setTelefone4("99999999999");

        UsuarioCadsusHelper.validatePhoneForBrazilianOrNaturalized(usuarioCadsus);
    }

    @Test
    public void shouldNotThrowValidacaoExceptionForBrazilianWithCellPhone() throws ValidacaoException {
        usuarioCadsus.setNacionalidade(UsuarioCadsus.Nacionalidade.BRASILEIRO.value());
        usuarioCadsus.setCelular("99999999999");

        UsuarioCadsusHelper.validatePhoneForBrazilianOrNaturalized(usuarioCadsus);
    }

    @Test(expected = ValidacaoException.class)
    public void shouldThrowValidacaoExceptionForNaturalizedWithoutPhone() throws ValidacaoException {
        usuarioCadsus.setNacionalidade(UsuarioCadsus.Nacionalidade.NATURALIZADO.value());

        UsuarioCadsusHelper.validatePhoneForBrazilianOrNaturalized(usuarioCadsus);
    }

    @Test
    public void shouldNotThrowValidacaoExceptionForNaturalizedWithPhone() throws ValidacaoException {
        usuarioCadsus.setNacionalidade(UsuarioCadsus.Nacionalidade.NATURALIZADO.value());
        usuarioCadsus.setTelefone("99999999999");

        UsuarioCadsusHelper.validatePhoneForBrazilianOrNaturalized(usuarioCadsus);
    }

    @Test
    public void shouldNotThrowValidacaoExceptionForNaturalizedWithPhone2() throws ValidacaoException {
        usuarioCadsus.setNacionalidade(UsuarioCadsus.Nacionalidade.NATURALIZADO.value());
        usuarioCadsus.setTelefone2("99999999999");

        UsuarioCadsusHelper.validatePhoneForBrazilianOrNaturalized(usuarioCadsus);
    }

    @Test
    public void shouldNotThrowValidacaoExceptionForNaturalizedWithPhone3() throws ValidacaoException {
        usuarioCadsus.setNacionalidade(UsuarioCadsus.Nacionalidade.NATURALIZADO.value());
        usuarioCadsus.setTelefone3("99999999999");

        UsuarioCadsusHelper.validatePhoneForBrazilianOrNaturalized(usuarioCadsus);
    }

    @Test
    public void shouldNotThrowValidacaoExceptionForNaturalizedWithPhone4() throws ValidacaoException {
        usuarioCadsus.setNacionalidade(UsuarioCadsus.Nacionalidade.NATURALIZADO.value());
        usuarioCadsus.setTelefone4("99999999999");

        UsuarioCadsusHelper.validatePhoneForBrazilianOrNaturalized(usuarioCadsus);
    }

    @Test
    public void shouldNotThrowValidacaoExceptionForNaturalizedWithCellPhone() throws ValidacaoException {
        usuarioCadsus.setNacionalidade(UsuarioCadsus.Nacionalidade.NATURALIZADO.value());
        usuarioCadsus.setCelular("99999999999");

        UsuarioCadsusHelper.validatePhoneForBrazilianOrNaturalized(usuarioCadsus);
    }

    @Test()
    public void shouldNotThrowValidacaoExceptionForForeignWithoutPhone() throws ValidacaoException {
        usuarioCadsus.setNacionalidade(UsuarioCadsus.Nacionalidade.ESTRANGEIRO.value());

        UsuarioCadsusHelper.validatePhoneForBrazilianOrNaturalized(usuarioCadsus);
    }

    @Test
    public void shouldNotThrowValidacaoExceptionForForeignWithPhone() throws ValidacaoException {
        usuarioCadsus.setNacionalidade(UsuarioCadsus.Nacionalidade.ESTRANGEIRO.value());
        usuarioCadsus.setTelefone("99999999999");

        UsuarioCadsusHelper.validatePhoneForBrazilianOrNaturalized(usuarioCadsus);
    }

    @Test
    public void shouldNotThrowValidacaoExceptionForForeignWithPhone2() throws ValidacaoException {
        usuarioCadsus.setNacionalidade(UsuarioCadsus.Nacionalidade.ESTRANGEIRO.value());
        usuarioCadsus.setTelefone2("99999999999");

        UsuarioCadsusHelper.validatePhoneForBrazilianOrNaturalized(usuarioCadsus);
    }

    @Test
    public void shouldNotThrowValidacaoExceptionForForeignWithPhone3() throws ValidacaoException {
        usuarioCadsus.setNacionalidade(UsuarioCadsus.Nacionalidade.ESTRANGEIRO.value());
        usuarioCadsus.setTelefone3("99999999999");

        UsuarioCadsusHelper.validatePhoneForBrazilianOrNaturalized(usuarioCadsus);
    }

    @Test
    public void shouldNotThrowValidacaoExceptionForForeignWithPhone4() throws ValidacaoException {
        usuarioCadsus.setNacionalidade(UsuarioCadsus.Nacionalidade.ESTRANGEIRO.value());
        usuarioCadsus.setTelefone4("99999999999");

        UsuarioCadsusHelper.validatePhoneForBrazilianOrNaturalized(usuarioCadsus);
    }

    @Test
    public void shouldNotThrowValidacaoExceptionForForeignWithCellPhone() throws ValidacaoException {
        usuarioCadsus.setNacionalidade(UsuarioCadsus.Nacionalidade.ESTRANGEIRO.value());
        usuarioCadsus.setCelular("99999999999");

        UsuarioCadsusHelper.validatePhoneForBrazilianOrNaturalized(usuarioCadsus);
    }
}