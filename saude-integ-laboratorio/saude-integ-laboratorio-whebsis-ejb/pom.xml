<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>br.com.celk.integracao.laboratorio</groupId>
        <artifactId>saude-integ-laboratorio</artifactId>
<version>3.1.285.1-SNAPSHOT</version>
    </parent>

    <artifactId>saude-integ-laboratorio-whebsis-ejb</artifactId>
    <packaging>ejb</packaging>
    <name>saude-integ-laboratorio-whebsis-ejb</name>

    <dependencies>
        <dependency>
            <groupId>br.com.celk.integracao.laboratorio</groupId>
            <artifactId>saude-integ-laboratorio-whebsis-connect</artifactId>
        </dependency>

        <dependency>
            <groupId>br.com.celk</groupId>
            <artifactId>saude-ejb-utils</artifactId>
        </dependency>
        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-core</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.jboss.spec.javax.ejb</groupId>
            <artifactId>jboss-ejb-api_3.2_spec</artifactId>
            <scope>provided</scope>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.zeroturnaround</groupId>
                <artifactId>jrebel-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-ejb-plugin</artifactId>
            </plugin>

        </plugins>
    </build>
</project>
