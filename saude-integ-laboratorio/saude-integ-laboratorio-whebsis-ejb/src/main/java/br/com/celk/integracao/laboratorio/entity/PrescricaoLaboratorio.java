package br.com.celk.integracao.laboratorio.entity;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "prescr_laboratorio_v")
public class PrescricaoLaboratorio implements Serializable {

    public static final String PROP_CHAVE_INTEGRACAO = "chaveIntegracao";
    
    @Id
    @Column(name="id")
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "seq_prescr_laboratorio_v")
    @SequenceGenerator(name = "seq_prescr_laboratorio_v", sequenceName = "seq_prescr_laboratorio_v")
    private Long codigo;

    //<editor-fold defaultstate="collapsed" desc="COLUMNS">
    @Column(name="nr_prescricao", nullable = false)
    private Long numeroPrescricao;
    
    @Column(name="nr_sequencia", nullable = false)
    private Long numeroSequencia;
    
    @Column(name="cd_procedimento", nullable = false)
    private Long codigoProcedimento;
    
    @Column(name="ds_procedimento", nullable = false, length = 250)
    private String descricaoProcedimento;
    
    @Column(name="qt_procedimento", nullable = false, precision = 3)
    private Double quantidadeProcedimento;
    
    @Column(name="dt_atualizacao", nullable = false)
    @Temporal(TemporalType.TIMESTAMP)
    private Date dataAtualizacao;
    
    @Column(name="nm_usuario", nullable = false, length = 15)
    private String nomeUsuario;
    
    @Column(name="ds_observacao")
    private String observacao;
    
    @Column(name="ie_suspenso", length = 1)
    private String flagSuspenso;
    
    @Column(name="nr_atendimento")
    private Long numeroAtendimento;
    
    @Column(name="cd_medico", nullable = false, length = 10)
    private String codigoMedico;
    
    @Column(name="dt_prescricao", nullable = false)
    @Temporal(TemporalType.TIMESTAMP)
    private Date dataPrescricao;
    
    @Column(name="cd_setor_paciente", nullable = false)
    private Long codigoSetorPaciente;
    
    @Column(name="nm_paciente", length = 150)
    private String nomePaciente;
    
    @Column(name="dt_nascimento")
    @Temporal(TemporalType.DATE)
    private Date dataNascimento;
    
    @Column(name="ie_sexo", length = 1)
    private String sexo;
    
    @Column(name="nr_cpf", length = 11)
    private String cpf;
    
    @Column(name="nm_medico", length = 60, nullable = false)
    private String nomeMedico;
    
    @Column(name="nr_crm", length = 20)
    private String crm;
    
    @Column(name="cd_convenio", nullable = false)
    private Long codigoConvenio;
    
    @Column(name="cd_categoria", length = 10, nullable = false)
    private String categoria;
    
    @Column(name="ds_convenio", length = 40, nullable = false)
    private String descricaoConvenio;
    
    @Column(name="ds_endereco", length = 40)
    private String endereco;
    
    @Column(name="nr_endereco")
    private Long numeroEndereco;
    
    @Column(name="ds_complemento", length = 40)
    private String complemento;
    
    @Column(name="ds_bairro", length = 40)
    private String bairro;
    
    @Column(name="ds_municipio", length = 40)
    private String municipio;
    
    @Column(name="sg_estado", length = 2)
    private String siglaEstado;
    
    @Column(name="nr_telefone", length = 15)
    private String telefone;
    
    @Column(name="cd_cep", length = 15)
    private String cep;
    
    @Column(name="ds_setor_paciente", length = 100, nullable = false)
    private String descricaoSetorPaciente;
    
    @Column(name="vl_procedimento", precision = 2)
    private Double valorProcedimento;

    @Column(name="chave_integracao")
    private Long chaveIntegracao;

    //</editor-fold>
    
    //<editor-fold defaultstate="collapsed" desc="getters and setters">
    
    public Long getChaveIntegracao() {
        return chaveIntegracao;
    }

    public void setChaveIntegracao(Long chaveIntegracao) {
        this.chaveIntegracao = chaveIntegracao;
    }
    
    public Long getCodigo() {
        return codigo;
    }
    
    public void setCodigo(Long codigo) {
        this.codigo = codigo;
    }
    
    public Long getNumeroPrescricao() {
        return numeroPrescricao;
    }
    
    public void setNumeroPrescricao(Long numeroPrescricao) {
        this.numeroPrescricao = numeroPrescricao;
    }
    
    public Long getNumeroSequencia() {
        return numeroSequencia;
    }
    
    public void setNumeroSequencia(Long numeroSequencia) {
        this.numeroSequencia = numeroSequencia;
    }
    
    public Long getCodigoProcedimento() {
        return codigoProcedimento;
    }
    
    public void setCodigoProcedimento(Long codigoProcedimento) {
        this.codigoProcedimento = codigoProcedimento;
    }
    
    public String getDescricaoProcedimento() {
        return descricaoProcedimento;
    }
    
    public void setDescricaoProcedimento(String descricaoProcedimento) {
        this.descricaoProcedimento = descricaoProcedimento;
    }
    
    public Double getQuantidadeProcedimento() {
        return quantidadeProcedimento;
    }
    
    public void setQuantidadeProcedimento(Double quantidadeProcedimento) {
        this.quantidadeProcedimento = quantidadeProcedimento;
    }
    
    public Date getDataAtualizacao() {
        return dataAtualizacao;
    }
    
    public void setDataAtualizacao(Date dataAtualizacao) {
        this.dataAtualizacao = dataAtualizacao;
    }
    
    public String getNomeUsuario() {
        return nomeUsuario;
    }
    
    public void setNomeUsuario(String nomeUsuario) {
        this.nomeUsuario = nomeUsuario;
    }
    
    public String getObservacao() {
        return observacao;
    }
    
    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }
    
    public String getFlagSuspenso() {
        return flagSuspenso;
    }
    
    public void setFlagSuspenso(String flgSuspenso) {
        this.flagSuspenso = flgSuspenso;
    }
    
    public Long getNumeroAtendimento() {
        return numeroAtendimento;
    }
    
    public void setNumeroAtendimento(Long numeroAtendimento) {
        this.numeroAtendimento = numeroAtendimento;
    }
    
    public String getCodigoMedico() {
        return codigoMedico;
    }
    
    public void setCodigoMedico(String codigoMedico) {
        this.codigoMedico = codigoMedico;
    }
    
    public Date getDataPrescricao() {
        return dataPrescricao;
    }
    
    public void setDataPrescricao(Date dataPrescricao) {
        this.dataPrescricao = dataPrescricao;
    }
    
    public Long getCodigoSetorPaciente() {
        return codigoSetorPaciente;
    }
    
    public void setCodigoSetorPaciente(Long codigoSetorPaciente) {
        this.codigoSetorPaciente = codigoSetorPaciente;
    }
    
    public String getNomePaciente() {
        return nomePaciente;
    }
    
    public void setNomePaciente(String nomePaciente) {
        this.nomePaciente = nomePaciente;
    }
    
    public Date getDataNascimento() {
        return dataNascimento;
    }
    
    public void setDataNascimento(Date dataNascimento) {
        this.dataNascimento = dataNascimento;
    }
    
    public String getSexo() {
        return sexo;
    }
    
    public void setSexo(String sexo) {
        this.sexo = sexo;
    }
    
    public String getCpf() {
        return cpf;
    }
    
    public void setCpf(String cpf) {
        this.cpf = cpf;
    }
    
    public String getNomeMedico() {
        return nomeMedico;
    }
    
    public void setNomeMedico(String nomeMedico) {
        this.nomeMedico = nomeMedico;
    }
    
    public String getCrm() {
        return crm;
    }
    
    public void setCrm(String crm) {
        this.crm = crm;
    }
    
    public Long getCodigoConvenio() {
        return codigoConvenio;
    }
    
    public void setCodigoConvenio(Long codigoConvenio) {
        this.codigoConvenio = codigoConvenio;
    }
    
    public String getCategoria() {
        return categoria;
    }
    
    public void setCategoria(String categoria) {
        this.categoria = categoria;
    }
    
    public String getDescricaoConvenio() {
        return descricaoConvenio;
    }
    
    public void setDescricaoConvenio(String descricaoConvenio) {
        this.descricaoConvenio = descricaoConvenio;
    }
    
    public String getEndereco() {
        return endereco;
    }
    
    public void setEndereco(String endereco) {
        this.endereco = endereco;
    }
    
    public Long getNumeroEndereco() {
        return numeroEndereco;
    }
    
    public void setNumeroEndereco(Long numeroEndereco) {
        this.numeroEndereco = numeroEndereco;
    }
    
    public String getComplemento() {
        return complemento;
    }
    
    public void setComplemento(String complemento) {
        this.complemento = complemento;
    }
    
    public String getBairro() {
        return bairro;
    }
    
    public void setBairro(String bairro) {
        this.bairro = bairro;
    }
    
    public String getMunicipio() {
        return municipio;
    }
    
    public void setMunicipio(String municipio) {
        this.municipio = municipio;
    }
    
    public String getSiglaEstado() {
        return siglaEstado;
    }
    
    public void setSiglaEstado(String siglaEstado) {
        this.siglaEstado = siglaEstado;
    }
    
    public String getTelefone() {
        return telefone;
    }
    
    public void setTelefone(String telefone) {
        this.telefone = telefone;
    }
    
    public String getCep() {
        return cep;
    }
    
    public void setCep(String cep) {
        this.cep = cep;
    }
    
    public String getDescricaoSetorPaciente() {
        return descricaoSetorPaciente;
    }
    
    public void setDescricaoSetorPaciente(String descricaoSetorPaciente) {
        this.descricaoSetorPaciente = descricaoSetorPaciente;
    }
    
    public Double getValorProcedimento() {
        return valorProcedimento;
    }
    
    public void setValorProcedimento(Double valorProcedimento) {
        this.valorProcedimento = valorProcedimento;
    }
    //</editor-fold>
    

    
}
