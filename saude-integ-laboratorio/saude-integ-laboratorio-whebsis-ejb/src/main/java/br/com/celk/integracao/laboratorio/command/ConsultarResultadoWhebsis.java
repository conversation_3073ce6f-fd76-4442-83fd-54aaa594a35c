package br.com.celk.integracao.laboratorio.command;

import br.com.celk.ejb.exception.BusinessException;
import br.com.celk.ejb.exception.ServerException;
import br.com.celk.integracao.laboratorio.command.util.LaboratorioWhebsisAbstractCommand;
import br.com.celk.integracao.laboratorio.connect.dto.ExameDTO;
import br.com.celk.integracao.laboratorio.entity.PrescricaoLaboratorio;
import br.com.celk.integracao.laboratorio.entity.ResultadoLaboratorio;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.hibernate.criterion.Restrictions;

/**
 * <AUTHOR>
 * Criado em: Apr 15, 2013
 */
public class ConsultarResultadoWhebsis extends LaboratorioWhebsisAbstractCommand{

    private Map<Long, ExameDTO> dtoMap;
    
    @Override
    public Object start() throws BusinessException, ServerException {
        Date dataIntegracao = new Date();
        
        List<ResultadoLaboratorio> resultadoList = getSession().createCriteria(ResultadoLaboratorio.class)
                .add(Restrictions.isNull(ResultadoLaboratorio.PROP_DATA_INTEGRACAO))
                .createCriteria(ResultadoLaboratorio.PROP_PRESCRICAO_LABORATORIO)
                .add(Restrictions.isNotNull(PrescricaoLaboratorio.PROP_CHAVE_INTEGRACAO))
                .list();
        
        dtoMap = new HashMap<Long, ExameDTO>();
        for (ResultadoLaboratorio resultadoLaboratorio : resultadoList) {
            ExameDTO dto = new ExameDTO();

            dto.setDataColeta(resultadoLaboratorio.getDataColeta());
            dto.setDescricaoResultado(resultadoLaboratorio.getDescricaoResultado());
            
            dtoMap.put(resultadoLaboratorio.getPrescricaoLaboratorio().getChaveIntegracao(), dto);
            
            resultadoLaboratorio.setDataIntegracao(dataIntegracao);
            getSession().save(resultadoLaboratorio);
        }
        
        return dtoMap;
    }

    public Map<Long, ExameDTO> getDtoMap() {
        return dtoMap;
    }

}
