/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.celk.integracao.laboratorio.connect.facade;

import br.com.celk.ejb.exception.BusinessException;
import br.com.celk.ejb.exception.ServerException;
import br.com.celk.integracao.laboratorio.connect.dto.ExameDTO;
import br.com.celk.provider.ejb.EJBLocation;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
@EJBLocation(value="br.com.celk.integracao.laboratorio.ejb.IntegracaoLaboratorioBO", moduleName="saude-integ-laboratorio-ejb")
public interface IntegracaoLaboratorioFacade {

    public Long gerarExameLaboratorio(ExameDTO dto) throws BusinessException, ServerException;

    public Map<Long, ExameDTO> consultarResultadoExames() throws BusinessException, ServerException;
    
}
