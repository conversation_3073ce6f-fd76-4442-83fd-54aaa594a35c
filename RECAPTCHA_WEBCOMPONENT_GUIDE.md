# Guia do ReCaptchaWebComponent - Seguindo Padrão AppletBiometria

## 🎯 Objetivo Alcançado

Criado o `ReCaptchaWebComponent` seguindo exatamente o padrão do `AppletBiometria`, que **elimina completamente a necessidade de HTML separado**.

## ✅ Padrão Seguido: AppletBiometria

### **Características do AppletBiometria:**
- ✅ **WebComponent** que estende `WebComponent`
- ✅ **Implementa IHeaderContributor** para adicionar scripts
- ✅ **Sem arquivo HTML** separado
- ✅ **Gera HTML dinamicamente** no `onComponentTagBody()`
- ✅ **Transforma tag** no `onComponentTag()`
- ✅ **Configuração via parâmetros** do sistema

### **ReCaptchaWebComponent implementa o mesmo padrão:**
- ✅ **WebComponent** que estende `WebComponent`
- ✅ **Implementa IHeaderContributor** para scripts do Google
- ✅ **Sem arquivo HTML** separado
- ✅ **Gera HTML completo** no `onComponentTagBody()`
- ✅ **Transforma tag em div** no `onComponentTag()`
- ✅ **Configuração via parâmetros** GoogleReCaptcha*

## 🚀 Como Usar (Zero HTML!)

### **Método 1: Uso Mais Simples**

```java
public class MinhaPage extends WebPage {
    private ReCaptchaWebComponent reCaptcha;
    
    @Override
    protected void onInitialize() {
        super.onInitialize();
        
        Form form = new Form("form");
        
        // UMA LINHA: Adiciona componente completo
        reCaptcha = ReCaptchaWebComponentHelper.addToForm(form, "reCaptcha");
        
        add(form);
    }
    
    private void validarFormulario() throws ValidacaoException {
        // UMA LINHA: Valida o reCAPTCHA
        ReCaptchaWebComponentHelper.validate(reCaptcha);
    }
}
```

**HTML necessário:**
```html
<form wicket:id="form">
    <!-- Seus campos aqui -->
    
    <!-- UMA TAG: Componente completo -->
    <span wicket:id="reCaptcha"></span>
    
    <button type="submit">Enviar</button>
</form>
```

### **Método 2: Criação Manual**

```java
public class MinhaPage extends WebPage {
    private ReCaptchaWebComponent reCaptcha;
    
    @Override
    protected void onInitialize() {
        super.onInitialize();
        
        Form form = new Form("form");
        
        // Criar componente manualmente
        reCaptcha = new ReCaptchaWebComponent("reCaptcha");
        form.add(reCaptcha);
        
        add(form);
    }
}
```

### **Método 3: Com Chave Específica**

```java
public class MinhaPage extends WebPage {
    private ReCaptchaWebComponent reCaptcha;
    
    @Override
    protected void onInitialize() {
        super.onInitialize();
        
        Form form = new Form("form");
        
        // Componente com chave específica
        reCaptcha = new ReCaptchaWebComponent("reCaptcha", "minha-chave-especifica");
        
        // Configurações opcionais
        reCaptcha.setStatusText("Complete a verificação personalizada");
        reCaptcha.setEnabled(isRecaptchaRequired());
        
        form.add(reCaptcha);
        add(form);
    }
}
```

## 🔧 HTML Gerado Automaticamente

O WebComponent gera esta estrutura automaticamente:

```html
<div class="recaptcha-web-component" id="reCaptcha123">
    <!-- Campo oculto para armazenar a resposta do reCAPTCHA -->
    <input type="hidden" id="recaptcha_response_reCaptcha" name="recaptcha_response_reCaptcha" />
    
    <!-- Container onde o reCAPTCHA será renderizado pelo JavaScript -->
    <div id="recaptcha-container-reCaptcha" class="recaptcha-container"></div>
    
    <!-- Elemento de status para feedback ao usuário -->
    <p style="font-size: 12px; color: #666; margin-top: 5px;">
        <span id="recaptcha-status-reCaptcha">Carregando verificação...</span>
    </p>
</div>
```

## 📋 Métodos Disponíveis

### **ReCaptchaWebComponent**

```java
// Validação
boolean isCompleted()                    // Verifica se foi completado
String getRecaptchaResponse()            // Obtém token de resposta
void validate()                          // Valida (lança exceção se inválido)

// Configuração
boolean isEnabled()                      // Verifica se está habilitado
void setEnabled(boolean enabled)         // Habilita/desabilita
String getSiteKey()                      // Obtém chave do site
void setSiteKey(String siteKey)          // Define chave do site
String getStatusText()                   // Obtém texto de status
void setStatusText(String text)          // Define texto de status

// Informações dos elementos gerados
String getContainerId()                  // ID do container HTML
String getHiddenFieldId()                // ID do campo oculto
String getStatusId()                     // ID do elemento de status
```

### **ReCaptchaWebComponentHelper**

```java
// Criação
ReCaptchaWebComponent addToForm(Form form, String id)
ReCaptchaWebComponent addToContainer(MarkupContainer container, String id)
ReCaptchaWebComponent addToForm(Form form, String id, String siteKey)
ReCaptchaWebComponent create(String id)
ReCaptchaWebComponent create(String id, String siteKey)

// Validação
boolean validate(ReCaptchaWebComponent component)
boolean validate(ReCaptchaWebComponent component, boolean throwException)
void validateWithMessage(ReCaptchaWebComponent component, String errorMessage)

// Utilitários
boolean isEnabledAndConfigured(ReCaptchaWebComponent component)
String getDebugInfo(ReCaptchaWebComponent component)
```

## 🎨 Exemplos Práticos

### **Exemplo 1: Formulário de Contato**

```java
public class ContatoPage extends WebPage {
    private ReCaptchaWebComponent reCaptcha;
    
    @Override
    protected void onInitialize() {
        super.onInitialize();
        
        Form<ContatoDTO> form = new Form<>("form", new CompoundPropertyModel<>(new ContatoDTO()));
        
        form.add(new TextField<>("nome"));
        form.add(new TextField<>("email"));
        form.add(new TextArea<>("mensagem"));
        
        // Adicionar reCAPTCHA
        reCaptcha = ReCaptchaWebComponentHelper.addToForm(form, "reCaptcha");
        
        form.add(new Button("enviar") {
            @Override
            public void onSubmit() {
                try {
                    reCaptcha.validate();
                    // Processar formulário...
                    info("Mensagem enviada com sucesso!");
                } catch (Exception e) {
                    error("Erro: " + e.getMessage());
                }
            }
        });
        
        add(form);
    }
}
```

**HTML:**
```html
<form wicket:id="form">
    <input type="text" wicket:id="nome" placeholder="Nome" />
    <input type="email" wicket:id="email" placeholder="Email" />
    <textarea wicket:id="mensagem" placeholder="Mensagem"></textarea>
    
    <!-- WebComponent reCAPTCHA -->
    <span wicket:id="reCaptcha"></span>
    
    <button wicket:id="enviar">Enviar</button>
</form>
```

### **Exemplo 2: Múltiplos Formulários**

```java
public class MultiplosFormulariosPage extends WebPage {
    
    @Override
    protected void onInitialize() {
        super.onInitialize();
        
        // Formulário 1
        Form form1 = new Form("form1");
        ReCaptchaWebComponent recaptcha1 = ReCaptchaWebComponentHelper.addToForm(form1, "reCaptcha1");
        add(form1);
        
        // Formulário 2
        Form form2 = new Form("form2");
        ReCaptchaWebComponent recaptcha2 = ReCaptchaWebComponentHelper.addToForm(form2, "reCaptcha2");
        add(form2);
    }
}
```

**HTML:**
```html
<form wicket:id="form1">
    <!-- Campos do formulário 1 -->
    <span wicket:id="reCaptcha1"></span>
</form>

<form wicket:id="form2">
    <!-- Campos do formulário 2 -->
    <span wicket:id="reCaptcha2"></span>
</form>
```

### **Exemplo 3: Container Qualquer**

```java
public class PaginaComContainer extends WebPage {
    
    @Override
    protected void onInitialize() {
        super.onInitialize();
        
        WebMarkupContainer container = new WebMarkupContainer("container");
        
        // Adicionar reCAPTCHA a qualquer container
        ReCaptchaWebComponent recaptcha = ReCaptchaWebComponentHelper.addToContainer(container, "reCaptcha");
        
        add(container);
    }
}
```

**HTML:**
```html
<div wicket:id="container">
    <h3>Meu Container</h3>
    <span wicket:id="reCaptcha"></span>
</div>
```

## 🔄 Migração Realizada

### **ConsultaMedicamentoPublicoPage**

**Antes (ReCaptchaPanel):**
```java
private ReCaptchaPanel reCaptcha;

reCaptcha = new ReCaptchaPanel("reCaptcha");
reCaptcha.setOutputMarkupId(true);
reCaptcha.setOutputMarkupPlaceholderTag(true);
getForm().add(reCaptcha);

if (reCaptcha.isEnabled() && !reCaptcha.isCompleted()) {
    throw new ValidacaoException("Complete o reCAPTCHA");
}
```

```html
<div wicket:id="reCaptcha"></div>
<!-- Precisava do arquivo ReCaptchaPanel.html -->
```

**Depois (ReCaptchaWebComponent):**
```java
private ReCaptchaWebComponent reCaptcha;

reCaptcha = ReCaptchaWebComponentHelper.addToForm(getForm(), "reCaptcha");

ReCaptchaWebComponentHelper.validate(reCaptcha);
```

```html
<span wicket:id="reCaptcha"></span>
<!-- Sem arquivo HTML separado! -->
```

## ⚡ Comparação com Outras Abordagens

| Aspecto | ReCaptchaPanel | ReCaptchaWidget | **ReCaptchaWebComponent** |
|---------|----------------|-----------------|---------------------------|
| **Arquivo HTML** | ✅ Necessário | ✅ Necessário | ❌ **Não necessário** |
| **Linhas HTML** | 5+ linhas | 1 linha | **0 linhas** |
| **Linhas Java** | 4 linhas | 2 linhas | **1 linha** |
| **Padrão Wicket** | Panel | Widget | **WebComponent** |
| **Seguir AppletBiometria** | ❌ Não | ❌ Não | ✅ **Sim** |
| **Flexibilidade** | Média | Alta | **Máxima** |
| **Simplicidade** | Baixa | Alta | **Máxima** |

## ✅ Benefícios do WebComponent

### **Simplicidade Máxima**
- ✅ **Zero HTML** para escrever
- ✅ **Uma linha** para adicionar
- ✅ **Uma linha** para validar
- ✅ **Uma tag** no HTML

### **Padrão Consistente**
- ✅ **Segue AppletBiometria** exatamente
- ✅ **Padrão conhecido** na aplicação
- ✅ **Manutenção familiar** para a equipe

### **Robustez**
- ✅ **HTML gerado dinamicamente** e sempre correto
- ✅ **IDs únicos automáticos** evitam conflitos
- ✅ **Escape automático** de JavaScript e HTML
- ✅ **Configuração via parâmetros** do sistema

### **Flexibilidade Total**
- ✅ **Pode ser usado em qualquer lugar** (Form, Container, etc.)
- ✅ **Configuração opcional** quando necessário
- ✅ **Múltiplas instâncias** na mesma página
- ✅ **Eventos JavaScript** para integração

## 🔧 Configuração do Sistema

Configure os parâmetros no sistema (módulo Sistema):

```
GoogleReCaptchaEnabled: S/N
GoogleReCaptchaSiteKey: sua_site_key_aqui
GoogleReCaptchaSecretKey: sua_secret_key_aqui
```

## 📝 Resumo da Transformação

1. ✅ **ReCaptchaPanel** → **ReCaptchaBehavior** (flexibilidade)
2. ✅ **ReCaptchaBehavior** → **ReCaptchaWidget** (simplicidade)
3. ✅ **ReCaptchaWidget** → **ReCaptchaWebComponent** (padrão AppletBiometria)

### **Resultado Final:**
- 🔒 **Seguro** (configuração via parâmetros)
- 🚀 **Simples** (uma tag HTML)
- 🔧 **Flexível** (pode ser usado em qualquer lugar)
- 🎯 **Padrão** (segue AppletBiometria)
- ⚡ **Robusto** (HTML gerado dinamicamente)

## 🎉 Conclusão

O `ReCaptchaWebComponent` é a **solução definitiva** para reCAPTCHA na aplicação:

- **Segue exatamente o padrão AppletBiometria**
- **Zero necessidade de HTML separado**
- **Máxima simplicidade de uso**
- **Flexibilidade total de aplicação**

**Use quando quiser a máxima simplicidade seguindo o padrão da aplicação!** 🎯
