# Guia de Debug: reCAPTCHA v3 Token não Recebido

## 🐛 Problema

O token do reCAPTCHA v3 não está sendo recebido no `hiddenFieldId`.

## 🔍 Debug Implementado

Adicionei logs detalhados para identificar onde está o problema:

### **1. Logs do Servidor (Java)**

#### **Inicialização do Componente:**
```
DEBUG ReCaptchaV3 - Component initialized:
  Component ID: reCaptcha
  Hidden Field ID: recaptcha_token_reCaptcha
  Score Field ID: recaptcha_score_reCaptcha
```

#### **Geração do HTML:**
```
DEBUG ReCaptchaV3 - Generated HTML:
<!-- Campos ocultos para reCAPTCHA v3 -->
<input type="hidden" id="recaptcha_token_reCaptcha" name="recaptcha_token_reCaptcha" />
<input type="hidden" id="recaptcha_score_reCaptcha" name="recaptcha_score_reCaptcha" />
```

#### **Verificação do Token:**
```
DEBUG ReCaptchaV3 - Looking for parameter: recaptcha_token_reCaptcha
DEBUG ReCaptchaV3 - Found parameter: recaptcha_token_reCaptcha = ABC123...
DEBUG ReCaptchaV3 - Token for recaptcha_token_reCaptcha: ABC123..., Completed: true
```

### **2. Logs do Navegador (JavaScript)**

#### **Console do Navegador:**
```javascript
reCAPTCHA v3 token received: 03AGdBq26...
Looking for hidden field with ID: recaptcha_token_reCaptcha
Hidden field found: YES
reCAPTCHA v3 token set successfully for action: search
Field value after setting: 03AGdBq26...
```

#### **Se Houver Problema:**
```javascript
ERROR: Hidden field not found with ID: recaptcha_token_reCaptcha
Available elements with similar IDs:
  Found: recaptcha_token_reCaptcha1
  Found: recaptcha_token_reCaptcha2
```

## 🔧 Como Debugar

### **Passo 1: Verificar Logs do Servidor**

1. **Acesse a página** que usa reCAPTCHA v3
2. **Verifique os logs** do servidor para:
   - IDs gerados na inicialização
   - HTML gerado
   - Se o componente está habilitado

### **Passo 2: Verificar Console do Navegador**

1. **Abra DevTools** (F12)
2. **Vá para Console**
3. **Recarregue a página**
4. **Procure por logs** do reCAPTCHA v3:
   - Token recebido do Google
   - Campo oculto encontrado
   - Token definido com sucesso

### **Passo 3: Verificar HTML Gerado**

1. **Inspecione o elemento** do componente
2. **Verifique se existe** o campo oculto:
   ```html
   <input type="hidden" id="recaptcha_token_reCaptcha" name="recaptcha_token_reCaptcha" />
   ```
3. **Confirme o ID** está correto

### **Passo 4: Verificar Parâmetros do Request**

1. **Submeta o formulário**
2. **Verifique logs** do servidor para parâmetros encontrados
3. **Confirme** se o token está sendo enviado

## 🚨 Problemas Comuns e Soluções

### **Problema 1: Campo Oculto Não Encontrado**

**Sintomas:**
```javascript
ERROR: Hidden field not found with ID: recaptcha_token_reCaptcha
```

**Possíveis Causas:**
- Wicket alterou o ID do elemento
- Componente não está sendo renderizado
- JavaScript executando antes do HTML estar pronto

**Soluções:**
1. **Verificar HTML gerado** no DevTools
2. **Confirmar IDs** nos logs do servidor
3. **Aumentar timeout** do JavaScript

### **Problema 2: Token Não Sendo Gerado**

**Sintomas:**
```javascript
// Nenhum log de token recebido
```

**Possíveis Causas:**
- Chave do site inválida
- Domínio não autorizado
- API do Google não carregada

**Soluções:**
1. **Verificar chave** no console do Google reCAPTCHA
2. **Confirmar domínio** está autorizado
3. **Verificar se script** está carregando

### **Problema 3: Token Gerado Mas Não Enviado**

**Sintomas:**
```javascript
reCAPTCHA v3 token set successfully for action: search
Field value after setting: 03AGdBq26...
```
```
DEBUG ReCaptchaV3 - Token for recaptcha_token_reCaptcha: null, Completed: false
```

**Possíveis Causas:**
- Campo não está dentro do formulário
- Nome do campo não confere
- Formulário sendo resetado

**Soluções:**
1. **Verificar estrutura HTML** do formulário
2. **Confirmar atributo name** do campo
3. **Verificar se formulário** não está sendo limpo

### **Problema 4: Componente Desabilitado**

**Sintomas:**
```
DEBUG ReCaptchaV3 - Component disabled, no HTML generated
```

**Possíveis Causas:**
- Parâmetro `GoogleReCaptchaEnabled` = "N"
- Componente explicitamente desabilitado
- Erro ao carregar configuração

**Soluções:**
1. **Verificar parâmetro** `GoogleReCaptchaEnabled`
2. **Confirmar** `component.setEnabled(true)`
3. **Verificar logs** de carregamento de configuração

## 🔧 Comandos de Debug

### **JavaScript para Testar Manualmente:**

```javascript
// Verificar se API está carregada
console.log('grecaptcha available:', typeof grecaptcha !== 'undefined');

// Executar reCAPTCHA manualmente
if (typeof grecaptcha !== 'undefined') {
    grecaptcha.ready(function() {
        grecaptcha.execute('SUA_SITE_KEY', {action: 'test'}).then(function(token) {
            console.log('Manual token:', token);
        });
    });
}

// Verificar campos ocultos
var hiddenFields = document.querySelectorAll('input[type="hidden"]');
hiddenFields.forEach(function(field) {
    if (field.id.indexOf('recaptcha') !== -1) {
        console.log('Hidden field:', field.id, 'value:', field.value);
    }
});
```

### **SQL para Verificar Configuração:**

```sql
-- Verificar parâmetros do reCAPTCHA
SELECT nome, valor 
FROM parametro 
WHERE nome LIKE '%reCaptcha%' 
AND modulo = 1;

-- Verificar se está habilitado
SELECT nome, valor 
FROM parametro 
WHERE nome = 'GoogleReCaptchaEnabled' 
AND modulo = 1;
```

## 📋 Checklist de Verificação

### **Frontend (JavaScript)**
- [ ] Script do Google carregando corretamente
- [ ] Token sendo gerado pelo Google
- [ ] Campo oculto sendo encontrado
- [ ] Token sendo definido no campo
- [ ] Formulário enviando o campo

### **Backend (Java)**
- [ ] Componente sendo inicializado
- [ ] IDs sendo gerados corretamente
- [ ] HTML sendo renderizado
- [ ] Parâmetro sendo encontrado no request
- [ ] Token sendo extraído corretamente

### **Configuração**
- [ ] Chave do site configurada
- [ ] reCAPTCHA habilitado
- [ ] Domínio autorizado no Google
- [ ] Versão v3 configurada no Google

## 🎯 Próximos Passos

1. **Execute a página** e colete todos os logs
2. **Identifique** em qual etapa o processo falha
3. **Aplique a solução** correspondente
4. **Teste novamente** até funcionar
5. **Remova logs de debug** quando resolver

## 📞 Se Ainda Não Funcionar

Se após seguir todos os passos ainda não funcionar:

1. **Colete todos os logs** (servidor + navegador)
2. **Tire screenshots** do HTML gerado
3. **Verifique configuração** no Google reCAPTCHA Admin
4. **Teste com chave de teste** primeiro
5. **Compare com implementação** que funciona

O debug detalhado deve revelar exatamente onde está o problema! 🔍
